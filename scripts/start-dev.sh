#!/bin/bash

# 简化版开发服务器启动脚本
# 功能：智能检查端口，关闭旧服务，启动新服务

PORT=${1:-3000}
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查并关闭端口
cleanup_port() {
    local port=$1
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        warning "端口 $port 被占用，正在关闭相关进程..."
        echo $pids | xargs kill -TERM 2>/dev/null
        sleep 2
        
        # 强制关闭如果还有残留
        local remaining=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$remaining" ]; then
            echo $remaining | xargs kill -KILL 2>/dev/null
            sleep 1
        fi
        
        success "端口 $port 已释放"
    else
        log "端口 $port 可用"
    fi
}

# 关闭所有 node/vite 相关进程
cleanup_processes() {
    log "检查并清理旧的开发服务器进程..."
    
    # 查找 vite 进程
    local vite_pids=$(pgrep -f "vite.*--mode.*env.local" 2>/dev/null)
    if [ -n "$vite_pids" ]; then
        warning "发现旧的 vite 进程，正在关闭..."
        echo $vite_pids | xargs kill -TERM 2>/dev/null
        sleep 2
        
        # 强制关闭
        local remaining_vite=$(pgrep -f "vite.*--mode.*env.local" 2>/dev/null)
        if [ -n "$remaining_vite" ]; then
            echo $remaining_vite | xargs kill -KILL 2>/dev/null
        fi
        success "旧的 vite 进程已清理"
    fi
    
    # 查找 pnpm 进程
    local pnpm_pids=$(pgrep -f "pnpm.*local" 2>/dev/null)
    if [ -n "$pnpm_pids" ]; then
        warning "发现旧的 pnpm 进程，正在关闭..."
        echo $pnpm_pids | xargs kill -TERM 2>/dev/null
        sleep 1
        success "旧的 pnpm 进程已清理"
    fi
}

# 启动服务器
start_server() {
    log "启动开发服务器 (端口: $PORT)..."
    
    # 直接启动，不重定向输出
    pnpm local &
    local server_pid=$!
    
    log "服务器进程 PID: $server_pid"
    log "等待服务器启动..."
    
    # 等待端口开放
    local max_wait=30
    local count=0
    
    while [ $count -lt $max_wait ]; do
        if lsof -i:$PORT &> /dev/null; then
            success "🎉 服务器启动成功！"
            success "🌐 访问地址: http://localhost:$PORT"
            return 0
        fi
        
        sleep 1
        count=$((count + 1))
        
        if [ $((count % 5)) -eq 0 ]; then
            log "等待中... ($count/$max_wait 秒)"
        fi
    done
    
    error "服务器启动超时"
    return 1
}

# 主函数
main() {
    echo
    log "🚀 启动 OLP Portal 开发服务器"
    echo "================================"
    
    # 清理进程和端口
    cleanup_processes
    cleanup_port $PORT
    
    echo
    
    # 启动服务器
    if start_server; then
        echo
        log "💡 提示："
        log "   - 按 Ctrl+C 停止服务器"
        log "   - 重新启动: $0"
        echo
        
        # 等待用户中断
        wait
    else
        error "启动失败"
        exit 1
    fi
}

# 捕获中断信号
trap 'echo; warning "正在停止服务器..."; cleanup_port $PORT; exit 0' INT TERM

main "$@"
