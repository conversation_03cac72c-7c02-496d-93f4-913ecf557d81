#!/bin/bash

# 开发服务器智能启动脚本
# 功能：检查端口占用，自动关闭旧服务，启动新服务

# 配置
PORT=${1:-3000}  # 默认端口3000，可通过参数传入
PROJECT_NAME="OLP Portal"
LOG_FILE="dev-server.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local pid=$(lsof -ti:$port 2>/dev/null)
    echo $pid
}

# 关闭指定端口的进程
kill_port() {
    local port=$1
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        log "发现端口 $port 被以下进程占用："
        lsof -i:$port 2>/dev/null | head -10
        
        echo
        warning "正在关闭端口 $port 上的进程..."
        
        # 首先尝试优雅关闭 (SIGTERM)
        echo $pids | xargs kill -TERM 2>/dev/null
        sleep 2
        
        # 检查是否还有进程
        local remaining_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$remaining_pids" ]; then
            warning "优雅关闭失败，强制关闭进程..."
            echo $remaining_pids | xargs kill -KILL 2>/dev/null
            sleep 1
        fi
        
        # 最终检查
        local final_pids=$(lsof -ti:$port 2>/dev/null)
        if [ -n "$final_pids" ]; then
            error "无法关闭端口 $port 上的进程，请手动处理"
            return 1
        else
            success "端口 $port 已释放"
        fi
    else
        log "端口 $port 未被占用"
    fi
    return 0
}

# 检查 Node.js 和 pnpm
check_dependencies() {
    log "检查依赖环境..."
    
    if ! command -v node &> /dev/null; then
        error "Node.js 未安装，请先安装 Node.js"
        return 1
    fi
    
    if ! command -v pnpm &> /dev/null; then
        error "pnpm 未安装，请先安装 pnpm"
        return 1
    fi
    
    local node_version=$(node --version)
    local pnpm_version=$(pnpm --version)
    
    success "Node.js: $node_version"
    success "pnpm: $pnpm_version"
    return 0
}

# 清理旧的日志文件
cleanup_logs() {
    if [ -f "$LOG_FILE" ]; then
        local log_size=$(du -h "$LOG_FILE" | cut -f1)
        if [ -f "$LOG_FILE" ] && [ $(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null) -gt 10485760 ]; then
            warning "日志文件过大 ($log_size)，正在清理..."
            > "$LOG_FILE"
            success "日志文件已清理"
        fi
    fi
}

# 启动开发服务器
start_server() {
    log "启动 $PROJECT_NAME 开发服务器..."
    log "端口: $PORT"
    log "模式: local development"
    
    # 启动服务器并将输出重定向到日志文件
    pnpm local > "$LOG_FILE" 2>&1 &
    local server_pid=$!
    
    log "服务器进程 PID: $server_pid"
    
    # 等待服务器启动
    log "等待服务器启动..."
    local max_wait=60  # 最大等待60秒
    local wait_count=0
    
    while [ $wait_count -lt $max_wait ]; do
        if lsof -i:$PORT &> /dev/null; then
            success "服务器启动成功！"
            success "访问地址: http://localhost:$PORT"
            
            # 显示最近的日志
            echo
            log "最近的服务器日志："
            tail -n 10 "$LOG_FILE" 2>/dev/null || echo "暂无日志输出"
            
            return 0
        fi
        
        sleep 1
        wait_count=$((wait_count + 1))
        
        # 每5秒显示一次等待状态
        if [ $((wait_count % 5)) -eq 0 ]; then
            log "等待中... ($wait_count/$max_wait 秒)"
        fi
    done
    
    error "服务器启动超时，请检查日志文件: $LOG_FILE"
    return 1
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [端口号]"
    echo
    echo "选项:"
    echo "  端口号    指定服务器端口 (默认: 3000)"
    echo "  -h, --help    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0        # 使用默认端口 3000"
    echo "  $0 8080   # 使用端口 8080"
    echo
    echo "功能:"
    echo "  - 自动检查端口占用情况"
    echo "  - 智能关闭旧服务"
    echo "  - 启动新的开发服务器"
    echo "  - 实时监控启动状态"
}

# 主函数
main() {
    # 处理帮助参数
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    echo
    log "🚀 $PROJECT_NAME 开发服务器启动脚本"
    echo "================================================"
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 清理日志
    cleanup_logs
    
    # 检查并关闭端口
    if ! kill_port $PORT; then
        exit 1
    fi
    
    echo
    
    # 启动服务器
    if start_server; then
        echo
        success "🎉 开发服务器启动完成！"
        echo
        log "提示："
        log "- 按 Ctrl+C 停止服务器"
        log "- 查看完整日志: tail -f $LOG_FILE"
        log "- 重新启动: $0 $PORT"
        echo
    else
        error "服务器启动失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
