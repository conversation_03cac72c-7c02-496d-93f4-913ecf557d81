import { ref, h, render } from 'vue'
import { toast } from '@/components/ui/toast/use-toast'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog'

/**
 * Toast notification to replace native alert()
 */
export function useAlert() {
  const showAlert = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    toast({
      title:
        type === 'error'
          ? 'Error'
          : type === 'warning'
            ? 'Warning'
            : type === 'success'
              ? 'Success'
              : 'Notice',
      description: message,
      variant: type === 'error' ? 'destructive' : 'default'
    })
  }

  return {
    showAlert,
    // Simplified methods compatible with native alert
    alert: (message: string) => showAlert(message, 'info'),
    success: (message: string) => showAlert(message, 'success'),
    error: (message: string) => showAlert(message, 'error'),
    warning: (message: string) => showAlert(message, 'warning')
  }
}

/**
 * Confirmation dialog to replace native confirm()
 */
export function useConfirm() {
  const showConfirm = (
    title: string,
    description?: string,
    options?: {
      confirmText?: string
      cancelText?: string
      variant?: 'default' | 'destructive'
    }
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const { confirmText = 'Confirm', cancelText = 'Cancel', variant = 'default' } = options || {}

      // Create a reactive display state
      const isOpen = ref(true)
      let isResolved = false // Prevent multiple resolve calls

      // Create a temporary container to render the dialog
      const container = document.createElement('div')
      document.body.appendChild(container)

      // Cleanup function
      const cleanup = () => {
        if (isResolved) return // Prevent duplicate cleanup

        // Delayed cleanup, wait for animation to complete
        setTimeout(() => {
          try {
            render(null, container)
            if (container.parentNode) {
              container.parentNode.removeChild(container)
            }
          } catch (error) {
            console.warn('Dialog cleanup error:', error)
          }
        }, 300)
      }

      // Safe resolve function
      const safeResolve = (value: boolean) => {
        if (isResolved) return
        isResolved = true
        console.log('Dialog resolving with value:', value)
        cleanup()
        resolve(value)
      }

      // Create dialog component
      const DialogComponent = {
        setup() {
          const handleConfirm = () => {
            console.log('Dialog confirm clicked')
            isOpen.value = false
            safeResolve(true)
          }

          const handleCancel = () => {
            console.log('Dialog cancel clicked')
            isOpen.value = false
            safeResolve(false)
          }

          // Remove handleOpenChange, don't handle external close events

          return () =>
            h(
              AlertDialog,
              {
                open: isOpen.value
                // Remove onUpdate:open event handling
              },
              {
                default: () =>
                  h(
                    AlertDialogContent,
                    {},
                    {
                      default: () => [
                        h(
                          AlertDialogHeader,
                          {},
                          {
                            default: () => [
                              h(AlertDialogTitle, {}, { default: () => title }),
                              description
                                ? h(AlertDialogDescription, {}, { default: () => description })
                                : null
                            ]
                          }
                        ),
                        h(
                          AlertDialogFooter,
                          {},
                          {
                            default: () => [
                              h(
                                AlertDialogCancel,
                                {
                                  onClick: handleCancel
                                },
                                { default: () => cancelText }
                              ),
                              h(
                                AlertDialogAction,
                                {
                                  onClick: handleConfirm,
                                  variant: variant
                                },
                                { default: () => confirmText }
                              )
                            ]
                          }
                        )
                      ]
                    }
                  )
              }
            )
        }
      }

      // Render dialog
      const vnode = h(DialogComponent)
      render(vnode, container)
    })
  }

  return {
    showConfirm,
    // Simplified methods compatible with native confirm
    confirm: (message: string) => showConfirm('Confirm', message),
    confirmDelete: (
      message: string = 'Are you sure you want to delete? This action cannot be undone.'
    ) =>
      showConfirm('Delete Confirmation', message, {
        confirmText: 'Delete',
        variant: 'destructive'
      }),
    confirmLeave: (
      message: string = 'Are you sure you want to leave? Unsaved answers will be lost.'
    ) =>
      showConfirm('Leave Confirmation', message, { confirmText: 'Leave', variant: 'destructive' })
  }
}

/**
 * Combined use of alert and confirm
 */
export function useDialog() {
  const alertFunctions = useAlert()
  const confirmFunctions = useConfirm()

  return {
    // Alert related methods
    alert: alertFunctions.alert,
    // Confirm related methods
    confirm: confirmFunctions.confirm,
    confirmDelete: confirmFunctions.confirmDelete,
    confirmLeave: confirmFunctions.confirmLeave,
    showConfirm: confirmFunctions.showConfirm
  }
}
