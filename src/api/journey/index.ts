import request from '@/config/axios'
export interface JourneyDetail {
  categoryId: number
  courseCount: number
  courseIds: string
  cover: string
  createBy: string
  createTime: string
  createId: number
  id: number
  introduction: string
  keywords: string
  pageNum: number
  pageSize: number
  remark: string
  status: number
  title: string
  updateBy: string
  updateId: number
  updateTime: string
  courseList: Course[]
}
export interface Course {
  duration: number
  handDuration: number
  cover: string
  status: number
  studyStatus: number
  name: string
  isComplete?: boolean
  statusText?: string
  id: number
}
export const listJourney = async (query: any) => {
  return await request.get({
    url: '/learning/journey/nav/page',
    params: query
  })
}

export const listMyToDoJourney = async (query: any) => {
  return await request.get({
    url: '/learning/journey/todo/page',
    params: query
  })
}

export const getJourneyCategoryAll = async () => {
  return await request.get({
    url: '/learning/journey/category/list'
  })
}

// 获取学习地图详情
export const getJourney = async (journeyId: number) => {
  return await request.get({
    url: `/learning/journey/get?id=${journeyId}`
  })
}

// 将学习地图添加到我的课程
export const addMyJourney = async (journeyId: number) => {
  return await request.post({
    url: '/learning/journey/mycourse',
    data: { journeyId }
  })
}

// 将学习地图从我的课程取消
export const removeMyJourney = async (journeyId: number) => {
  return await request.delete({
    url: '/learning/journey/mycourse',
    params: { journeyId }
  })
}
