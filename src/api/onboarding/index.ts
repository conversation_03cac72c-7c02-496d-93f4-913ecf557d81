import request from '@/config/axios'

export interface OnboardingNavReqVO {
  title: string
  status: string
  categoryId?: number
  pageNum: number
  pageSize: number
}
export interface OnboardingNavListVO {
  categoryId: number
  categoryName: string
  content: string
  cover: string
  createBy: string
  createId: number
  createTime: string
  departmentId: number
  departmentName: string
  description: string
  duration: number
  durationLower: number
  durationUpperL: number
  format: string
  id: number
  isMandatory: boolean
  keywords: string
  lang: string
  mediaType: number
  origin: number
  pageNum: number
  pageSize: number
  remark: string
  sort: number
  title: string
  updateBy: string
  updateId: number
  updateTime: string
}

export interface OnboardingReqVO extends OnboardingNavReqVO {
  categoryId: number
  categoryName: string
}

export interface CompanyPolicyVO extends OnboardingNavReqVO {
  departmentId: number
  departmentName: string
  content: string
}

export interface CategoryReqVO extends OnboardingNavReqVO {
  sort: number
  remark: string
}

export interface OnboardingStatusUpdateVO {
  onBoardingId: number
  status: number
}

// 获取导航 onboarding列表
export const onboardingNavList = async (params: OnboardingNavReqVO) => {
  return await request.get<OnboardingNavListVO[]>({
    url: '/adapter/v1/onboarding/nav/page',
    params
  })
}
// 获取todo-onboarding列表
export const onboardingList = async (params: OnboardingReqVO) => {
  return await request.get({
    url: '/adapter/v1/onboarding',
    params
  })
}
/** Onboarding Policy对应的详情页面 **/
export const getOnboardingDetail = async (id: number) => {
  return await request.get({
    url: `/adapter/v1/onboarding/${id}`
  })
}
export const companyPolicyList = async (params: CompanyPolicyVO) => {
  return await request.get({
    url: '/adapter/v1/company/policy',
    params
  })
}
/**
 * Category 查询
 * @param params
 * @returns
 */
export const categoryList = async (params: CategoryReqVO) => {
  return await request.get({
    url: '/adapter/v1/onboarding/category/list',
    params
  })
}
/**
 * onboarding 修改状态
 */
export const editStatus = async (data: OnboardingStatusUpdateVO) => {
  return await request.put({
    url: `/adapter/v1/onboarding/assignment/status`,
    data
  })
}
/**
 * onboarding-Todo查询
 */
export const onboardTodoList = async (params: OnboardingNavReqVO) => {
  return await request.get({
    url: '/adapter/v1/onboarding/todo/page',
    params
  })
}
/** Todo-查询状态 */
export const searchStatus = async (onBoardingId: number) => {
  return await request.get({
    url: `/adapter/v1/onboarding/assignment/${onBoardingId}`
  })
}
/**My Center-onboarding */
export const onboardMyList = async (params: OnboardingNavReqVO) => {
  return await request.get({
    url: '/adapter/v1/onboarding/center/page',
    params
  })
}
