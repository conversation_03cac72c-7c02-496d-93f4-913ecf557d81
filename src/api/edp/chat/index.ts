import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getAccessToken } from '@/utils/auth'
import { config } from '@/config/axios/config'

export interface TrainingNeedReqVO {
  positionName: string
  skillName: string
  skillLevel?: number
}

// AI对话接口
export const AIChatApi = {
  // 生成目录
  getContentCatalog: async (
    data: TrainingNeedReqVO,
    ctrl: AbortController,
    onMessage: (event: any) => void,
    onError: (event: any) => void,
    onClose: () => void
  ) => {
    const token = getAccessToken()
    return fetchEventSource(
      `${config.base_url}/edp/chat-history/TNI/getContentCatalog`,
      {
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
        openWhenHidden: true,
        onmessage: onMessage,
        onerror: onError,
        onclose: onClose,
        signal: ctrl.signal
      }
    )
  }
}