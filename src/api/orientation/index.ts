import request from '@/config/axios'

/** Orientation查询参数 */
export interface OrientationNavReqVO {
  /** 页码，从 1 开始 */
  pageNo: number
  /** 每页条数，最大值为 100 */
  pageSize: number
  /** 标题 */
  title?: string
  /** 分类id */
  categoryId?: string
  /** 封面图 */
  cover?: string
  /** 内容 */
  content?: string
  /** 关键字 */
  keywords?: string
  /** 格式 */
  format?: string
  /** 来源，0：本地，1：资源库 */
  origin?: string
  /** 媒体类型（1：视频，2：音频，3：文档，4：SCORM） */
  mediaType?: string
  /** 语言 */
  lang?: string
  /** 预估时间，秒 */
  duration?: string
  /** 描述 */
  description?: string
  /** 是否显示，false：不显示，true：显示 */
  display?: string
  /** 排序码 */
  sort?: string
  /** 创建时间 */
  createTime?: string
}

/** Category查询参数 */
export interface CategoryReqVO {
  sort?: number
  remark?: string
}

/** Orientation附件对象 */
export interface OrientationAttachmentDO {
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
  id: number
  orientationId: number
  origin: number
  resourceId: number
  fileId: number
  fileUrl: string
  fileName: string
  fileType: string
  mediaType: number
  lang: string
  size: number
  duration: number
}

/** Orientation响应对象 */
export interface OrientationRespVO {
  /** 主键ID */
  id: number
  /** 分类id */
  categoryId: number
  /** 分类名称 */
  categoryName: string
  /** 部门id */
  departmentId: number
  /** 部门名称 */
  departmentName: string
  /** 标题 */
  title: string
  /** 封面图 */
  cover: string
  /** 内容 */
  content: string
  /** 关键词 */
  keywords: string
  /** 来源，0：本地，1：资源库 */
  origin: number
  /** 格式 */
  format: string
  /** 媒体类型（1：视频，2：音频，3：文档，4：SCORM） */
  mediaType: number
  /** 语言 */
  lang: string
  /** 预估时间，秒 */
  duration: number
  /** 时长下限 */
  durationLower: number
  /** 时长上限 */
  durationUpper: number
  /** 描述 */
  description: string
  /** 是否显示，false：不显示，true：显示 */
  display: boolean
  /** 排序码 */
  sort: number
  /** 附件列表 */
  attachmentList: OrientationAttachmentDO[]
  /** 文件类型列表 */
  fileTypeList: string[]
}

/** 分页结果 */
export interface PageResultOrientationRespVO {
  /** 数据列表 */
  list: OrientationRespVO[]
  /** 总量 */
  total: number
}

/** 向后兼容的类型别名 */
export interface OrientationNavListVO extends OrientationRespVO {}
/**
 * 导航栏-分页查询orientation
 * @param params 查询参数
 * @returns 分页结果
 */
export const orientationNavList = async (params: OrientationNavReqVO) => {
  return await request.get<PageResultOrientationRespVO>({
    url: '/learning/orientation/nav/page',
    params
  })
}
// 获取todo-onboarding列表
export const onboardingList = async (params: OrientationNavReqVO) => {
  return await request.get({
    url: '/learning/onboarding',
    params
  })
}
/** orientation对应的详情页面 **/
export const getOrientationDetail = async (id: number) => {
  return await request.get({
    url: `/learning/orientation/get?id=${id}`
  })
}
/**
 * Category 查询
 * @param params
 * @returns
 */
export const categoryList = async (params: CategoryReqVO | string = {}) => {
  return await request.get({
    url: '/learning/orientation-category/list',
    params: typeof params === 'string' ? {} : params
  })
}
