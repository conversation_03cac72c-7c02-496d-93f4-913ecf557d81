import request from '@/config/axios'

export enum OnboardingMapTaskTypeEnum {
  ONLINE_COURSE = 10,
  ONBOARDING = 20,
  ORIENTATION = 30,
  COMPANY_POLICY = 40,
  MLC_TRAINING = 50
}

export enum OnboardingMapTaskStatusEnum {
  NOT_STARTED = 10, // 未开始
  IN_PROGRESS = 50, // 进行中
  COMPLETED = 90, // 已完成
}

export enum OnboardingMapModeEnum {
  FREE = 10,
  STAGE = 20
}

export enum OnboardingMapNodeStatusEnum {
  IN_PROGRESS = 10,
  COMPLETED = 90
}

// 学习任务
export interface OnboardingMapTask {
  id?: number
  nodeId: number
  bizType: OnboardingMapTaskTypeEnum
  bizId: string
  bizName: string
  duration: number
  mandatory: boolean
  status: OnboardingMapTaskStatusEnum;
  progress: number;
}

// 节点
export interface OnboardingMapNode {
  id?: number
  mapId: number
  name: string
  icon: string
  sort: number
  status: number
  tasks: OnboardingMapTask[]

  position?: string // 节点位置信息
}

// 学习地图 VO
export interface OnboardingMapVO {
  id: number // 主键id
  name: string // 学习地图名称
  mode: OnboardingMapModeEnum // 学习模式 10.自由学习 20. 阶段性学习
  // meta: string;
  nodes: OnboardingMapNode[]
}

// 学习地图 API
export const OnboardingMapApi = {

  // 查询学习地图详情
  getOnboardingMap: async () => {
    return await request.get({ url: `/learning/onboarding-map/get-detail` })
  },

  // 完成学习地图节点
  completeOnboardingNode: async (nodeId: number) => {
    return await request.post({ url: `/learning/onboarding-map/node-complete`, data: {nodeId} })
  },
}
