import request from '@/config/axios'
import dayjs from 'dayjs'

/** ----- ENUM ----- */
/** 日历活动类型枚举 */
export enum CalendarActivityTypeEnum {
  COURSE = 10, // 课程
  LIVE = 20    // 直播
}

// 兼容性别名
export const ActivityType = CalendarActivityTypeEnum

/** ----- INTERFACE ----- */
/** 获取日历信息请求参数 */
export interface CalendarInfoReqVO {
  startTime?: string // 开始时间
  endTime?: string // 结束时间
}

// 兼容性别名
export type CalendarParams = CalendarInfoReqVO

/** 日历活动信息 */
export interface CalendarActivityBO {
  activityName: string // 日历活动名称
  activityType: number // 日历活动类型 10.课程 20.直播
  startTime: number | string // 日历活动开始时间 (时间戳或date-time)
  endTime: number | string | null // 日历活动结束时间 (时间戳或date-time)
  roomId?: string // 房间ID
  status?: number // 状态
  privacy?: number // 隐私设置
  reserved?: boolean // 是否预约
  joinType?: number // 加入类型
}

/** 日历日期响应数据 */
export interface CalendarDateRespVO {
  date: number | string // 日期 (时间戳或date-time)
  activities: Record<string, CalendarActivityBO[]> // 活动列表
}

/** 日历信息响应数据 */
export interface CalendarInfoRespVO {
  code: number
  data: CalendarDateRespVO[]
  msg: string
}

// 兼容性别名
export type CalendarResponse = CalendarInfoRespVO

// 活动状态类型
export type ActivityStatus =
  | 'UNBOOKED' | 'APPROVING' | 'BOOKED' | 'BOOK_REJECTED'
  | 'STUDYING' | 'PASSED' | 'FAILED' | 'REJECTED' | 'NO_SHOW'
  | 'APPROVE'

// 前端使用的日历活动接口
export interface CalendarActivity {
  id: string
  name: string
  type: CalendarActivityTypeEnum
  startTime: string
  endTime: string
  date: string
  // 扩展属性
  color?: string
  location?: string
  classroom?: string
  language?: 'AR' | 'EN' | 'CN'
  instructor?: string
  description?: string
  status?: ActivityStatus
}

// 预订活动参数
export interface BookActivityParams {
  activityId: string
  userId: string
  reason?: string
}

// 审批活动参数
export interface ApproveActivityParams {
  activityId: string
  userId: string
  action: 'approve' | 'reject'
  reason?: string
}

/** ----- API ----- */
export const CalendarApi = {
  // 获取我的日历
  getCalendarInfo: async (params?: CalendarInfoReqVO) => {
    return request.appGet<CalendarInfoRespVO>({
      url: '/learning/calendar',
      params
    })
  },

  // 预订活动
  bookActivity: async (params: BookActivityParams) => {
    return await request.appPost<{ success: boolean; message: string }>({
      url: '/learning/calendar/book',
      data: params
    })
  },

  // 取消预订
  cancelBooking: async (activityId: string, userId: string, reason?: string) => {
    return await request.appPost<{ success: boolean; message: string }>({
      url: '/learning/calendar/cancel',
      data: { activityId, userId, reason }
    })
  },

  // 审批活动预订
  approveActivity: async (params: ApproveActivityParams) => {
    return await request.appPost<{ success: boolean; message: string }>({
      url: '/learning/calendar/approve',
      data: params
    })
  }
}

/** ----- DATA TRANSFORMATION FUNCTIONS ----- */
// 数据转换：将 API 返回的数据转换为前端使用的格式
export const transformCalendarData = (apiData: CalendarDateRespVO[]): CalendarActivity[] => {
  const activities: CalendarActivity[] = []

  console.log('Transforming calendar data:', apiData)

  apiData.forEach(dateItem => {
    // 处理时间戳或字符串格式的日期
    const date = typeof dateItem.date === 'number'
      ? dayjs(dateItem.date).format('YYYY-MM-DD')
      : dayjs(dateItem.date).format('YYYY-MM-DD')

    console.log('Processing date item:', {
      originalDate: dateItem.date,
      formattedDate: date,
      activities: dateItem.activities
    })

    // 遍历 activities 对象的所有键值对
    Object.entries(dateItem.activities).forEach(([key, activityList]) => {
      console.log(`Processing activity type ${key}:`, activityList)

      activityList.forEach((activity, index) => {
        // 处理时间戳格式的开始和结束时间
        const startTime = typeof activity.startTime === 'number'
          ? dayjs(activity.startTime).toISOString()
          : activity.startTime

        const endTime = activity.endTime
          ? (typeof activity.endTime === 'number'
              ? dayjs(activity.endTime).toISOString()
              : activity.endTime)
          : null

        const transformedActivity = {
          id: `${date}-${key}-${index}`, // 生成唯一ID
          name: activity.activityName,
          type: activity.activityType as CalendarActivityTypeEnum,
          startTime: startTime,
          endTime: endTime,
          date: date,
          color: getActivityTypeColor(activity.activityType as CalendarActivityTypeEnum),
          // 扩展属性
          roomId: activity.roomId,
          status: mapActivityStatus(activity.status),
          privacy: activity.privacy,
          reserved: activity.reserved,
          joinType: activity.joinType
        }

        console.log('Transformed activity:', transformedActivity)
        activities.push(transformedActivity)
      })
    })
  })

  console.log('Final transformed activities:', activities)
  return activities
}

/** ----- MOCK DATA FUNCTIONS ----- */
// 生成模拟日历数据
export const generateMockCalendarData = (startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): CalendarInfoRespVO => {
  const data: CalendarDateRespVO[] = []
  let current = startDate

  while (current.isBefore(endDate) || current.isSame(endDate, 'day')) {
    // 随机生成 0-2 个活动
    const activityCount = Math.floor(Math.random() * 3)
    const activities: Record<string, CalendarActivityBO[]> = {}

    if (activityCount > 0) {
      activities['courses'] = []

      for (let i = 0; i < activityCount; i++) {
        const startHour = 8 + Math.floor(Math.random() * 8) // 8 AM to 4 PM
        const startTime = current.hour(startHour).minute(0).second(0)
        const endTime = startTime.add(1 + Math.floor(Math.random() * 3), 'hour') // 1-4 hours

        activities['courses'].push({
          activityName: `Course ${i + 1} - ${current.format('MMM DD')}`,
          activityType: CalendarActivityTypeEnum.COURSE,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString()
        })
      }
    }

    data.push({
      date: current.toISOString(),
      activities
    })

    current = current.add(1, 'day')
  }

  return {
    code: 0,
    data,
    msg: 'success'
  }
}

// 生成模拟课程数据（向后兼容）
export const generateMockCourses = (startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): CalendarActivity[] => {
  const mockResponse = generateMockCalendarData(startDate, endDate)
  return transformCalendarData(mockResponse.data)
}

/** ----- HELPER FUNCTIONS ----- */
// 获取用户角色（模拟）
export const getUserRole = (): 'student' | 'approver' => {
  // 在实际应用中，这应该从用户 store 或 API 获取
  return Math.random() > 0.7 ? 'approver' : 'student'
}

// 映射后端状态到前端状态
export const mapActivityStatus = (backendStatus?: number): ActivityStatus => {
  switch (backendStatus) {
    case 90:
      return 'BOOKED'
    case 10:
      return 'UNBOOKED'
    case 20:
      return 'APPROVING'
    case 30:
      return 'REJECTED'
    case 40:
      return 'STUDYING'
    case 50:
      return 'PASSED'
    case 60:
      return 'FAILED'
    case 70:
      return 'NO_SHOW'
    default:
      return 'UNBOOKED'
  }
}

// 获取活动类型颜色
export const getActivityTypeColor = (type: CalendarActivityTypeEnum) => {
  switch (type) {
    case CalendarActivityTypeEnum.COURSE:
      return '#0079b4' // 与SidebarRight中的MLC Training颜色保持一致
    case CalendarActivityTypeEnum.LIVE:
      return '#017B3D' // 与SidebarRight中的Live颜色保持一致
    default:
      return '#6B7280' // Gray
  }
}

// 获取活动类型名称
export const getActivityTypeName = (type: CalendarActivityTypeEnum) => {
  switch (type) {
    case CalendarActivityTypeEnum.COURSE:
      return 'MLC Training'
    case CalendarActivityTypeEnum.LIVE:
      return 'Live'
    default:
      return 'Activity'
  }
}

// 格式化活动状态显示
export const formatActivityStatus = (status: ActivityStatus) => {
  const statusMap = {
    'UNBOOKED': 'Available',
    'APPROVING': 'Pending Approval',
    'BOOKED': 'Booked',
    'BOOK_REJECTED': 'Booking Rejected',
    'STUDYING': 'In Progress',
    'PASSED': 'Completed',
    'FAILED': 'Failed',
    'REJECTED': 'Rejected',
    'NO_SHOW': 'No Show',
    'APPROVE': 'Awaiting Approval'
  }
  return statusMap[status] || status
}