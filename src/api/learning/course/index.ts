import request from '@/config/axios'

/** ----- COURSE ENUMS ----- */

/** 课程状态枚举 */
export enum CourseStatusEnum {
  OFF_SHELF = 0, // 下架
  ON_SHELF = 1 // 上架
}

/** 课程类型枚举 */
export enum CourseTypeEnum {
  ELECTIVE = 0, // 选修
  MANDATORY = 1 // 必修
}

/** 学习状态枚举 */
export enum StudyStatusEnum {
  PENDING = 0, // 未开始
  IN_PROGRESS = 1, // 进行中
  COMPLETED = 3 // 已完成
}

/** 课程级别枚举 */
export enum CourseLevelEnum {
  SUITABLE_FOR_ALL = 0, // 适合所有人（默认）
  BEGINNER = 1, // 初级
  INTERMEDIATE = 2, // 中级
  ADVANCED = 3 // 高级
}

/** 语言枚举 */
export enum LanguageEnum {
  ENGLISH = 1, // 英语
  ARABIC = 2 // 阿拉伯语
}

/** 课程来源枚举 */
export enum CourseSourceEnum {
  LOCAL = 1, // 本地
  CLOUD = 2 // 云端
}

/** 媒体类型枚举 */
export enum MediaTypeEnum {
  VIDEO = 1, // 视频
  AUDIO = 2, // 音频
  DOCUMENT = 3, // 文档
  SCORM = 4 // SCORM
}

/** 时长类型枚举 */
export enum DurationTypeEnum {
  LESS_THAN_15 = 1, // <15min
  BETWEEN_15_30 = 2, // 15-30min
  BETWEEN_30_60 = 3, // 30-60min
  MORE_THAN_60 = 4 // >60min
}

/** 考试枚举 */
export enum ExamEnum {
  NO = 0, // 否
  YES = 1 // 是
}

/** 新课热课类型枚举 */
export enum NewCourseTypeEnum {
  NEW = 0, // 新课
  HOT = 1 // 热课
}

/** ----- COURSE LABEL MAPPINGS ----- */

/** 课程状态标签映射 */
export const CourseStatusLabels = {
  [CourseStatusEnum.OFF_SHELF]: 'Off Shelf',
  [CourseStatusEnum.ON_SHELF]: 'On Shelf'
} as const

/** 课程类型标签映射 */
export const CourseTypeLabels = {
  [CourseTypeEnum.ELECTIVE]: 'Elective',
  [CourseTypeEnum.MANDATORY]: 'Mandatory'
} as const

/** 学习状态标签映射 */
export const StudyStatusLabels = {
  [StudyStatusEnum.PENDING]: 'Not Started',
  [StudyStatusEnum.IN_PROGRESS]: 'In Progress',
  [StudyStatusEnum.COMPLETED]: 'Completed'
} as const

/** 课程级别标签映射 */
export const CourseLevelLabels = {
  [CourseLevelEnum.SUITABLE_FOR_ALL]: 'All Levels',
  [CourseLevelEnum.BEGINNER]: 'Beginner',
  [CourseLevelEnum.INTERMEDIATE]: 'Intermediate',
  [CourseLevelEnum.ADVANCED]: 'Advanced'
} as const

/** 语言标签映射 */
export const LanguageLabels = {
  [LanguageEnum.ENGLISH]: 'English',
  [LanguageEnum.ARABIC]: 'Arabic'
} as const

/** 课程来源标签映射 */
export const CourseSourceLabels = {
  [CourseSourceEnum.LOCAL]: 'Local',
  [CourseSourceEnum.CLOUD]: 'Cloud'
} as const

/** 媒体类型标签映射 */
export const MediaTypeLabels = {
  [MediaTypeEnum.VIDEO]: 'Video',
  [MediaTypeEnum.AUDIO]: 'Audio',
  [MediaTypeEnum.DOCUMENT]: 'Document',
  [MediaTypeEnum.SCORM]: 'SCORM'
} as const

/** 时长类型标签映射 */
export const DurationTypeLabels = {
  [DurationTypeEnum.LESS_THAN_15]: '<15min',
  [DurationTypeEnum.BETWEEN_15_30]: '15-30min',
  [DurationTypeEnum.BETWEEN_30_60]: '30-60min',
  [DurationTypeEnum.MORE_THAN_60]: '>60min'
} as const

/** 考试标签映射 */
export const ExamLabels = {
  [ExamEnum.NO]: 'No Exam',
  [ExamEnum.YES]: 'Has Exam'
} as const

/** 新课热课类型标签映射 */
export const NewCourseTypeLabels = {
  [NewCourseTypeEnum.NEW]: 'New Course',
  [NewCourseTypeEnum.HOT]: 'Hot Course'
} as const

/** ----- COURSE TYPE DEFINITIONS ----- */

/** 课程分配范围 */
export interface CourseAssignScopeVO {
  id: number
  relevanceId: number
  relevanceName: string
  scope: number // 范围（1：Company，2：Department，3：Section，4：Position，5：Employee）
  type: number // 类型（0：公开，1：必修）
}

/** 课程专题 */
export interface CourseTopicVO {
  id: number
  parentId: number
  ancestors: string
  name: string
  keywords: string
  introduction: string
  sort: number
  cover: string
  createTime: string
  updateTime: string
  creator: string
  updater: string
  children?: CourseTopicVO[] // 子集节点
}

/** 课程专题（Subject）- 用于下拉选择 */
export interface SubjectVO {
  id: number
  parentId: number
  name: string
  sort: number
  cover: string
  children?: SubjectVO[]
}

/** 课程完整信息响应对象 */
export interface CourseRespVO {
  // 基础信息
  id: number
  topicId: string
  parentTopicId: number
  name: string
  cover: string
  keywords: string
  lang: string
  duration: number
  durationLower: number
  durationUpper: number
  introduction: string
  deptId: number

  // 状态信息
  status: CourseStatusEnum
  shelfTime: string
  effectiveDay: number // 课程时效（单位：天，-1表示永久）
  isAutoAssign: boolean
  isCertificateGenerated: boolean
  isRecommend: boolean
  isNew: boolean

  // 统计信息
  level: CourseLevelEnum
  enrollNumber: number
  pageView: number
  star: number

  // 分配信息
  scopes: CourseAssignScopeVO[]
  topic: string
  type: CourseTypeEnum
  deadline: string
  myStar: number

  // 学习统计
  electiveNum: number
  requiredNum: number
  requiredAndCompletedNum: number
  requiredAndNotCompletedNum: number
  studyStatus: StudyStatusEnum
  chapterNum: number
  examNum: number
  assigned: boolean
  certificateUrl: string

  // 扩展信息
  topicIdList: number[]
  subTopicIdList: number[]
  language: LanguageEnum
  subtitle: boolean
  source: CourseSourceEnum
  handDuration: number
  handDurationLower: number
  handDurationUpper: number
  contentId: string
  assetUuid: string
  exam: ExamEnum
  courseIds: number[]

  // 系统字段
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
}

/** 应用端课程响应对象 */
export interface AppCourseRespVO extends CourseRespVO {
  // 应用端特有字段
  progress?: number
}

/** 课程卡片数据接口（用于组件） */
export interface CourseCardData extends Partial<CourseRespVO> {
  id: string | number
  name: string
  cover: string
  progress?: number
  // 扩展字段用于UI显示
  noteCount?: number
  codeCount?: number
  qaCount?: number
}

/** 课程查询参数 */
export interface CourseQueryParams {
  pageNo: number
  pageSize: number
  userId?: number
  name?: string
  topicId?: string
  parentTopicId?: number
  subTopicIdList?: number[]
  type?: CourseTypeEnum
  courseType?: number // 新课热课类型（0：新课，1：热课）
  studyStatus?: StudyStatusEnum // 学习状态过滤
  levelList?: CourseLevelEnum[]
  languageList?: LanguageEnum[]
  subtitleList?: number[]
  sourceList?: CourseSourceEnum[]
  durationTypeList?: DurationTypeEnum[]
}

/** 已完成课程查询参数 */
export interface DoneCourseQueryParams {
  pageNo: number
  pageSize: number
  userId?: number
  name?: string
  topicId?: string
  parentTopicId?: number
  subTopicIdList?: number[]
  type?: CourseTypeEnum
  courseType?: number
  levelList?: CourseLevelEnum[]
  languageList?: LanguageEnum[]
  subtitleList?: number[]
  sourceList?: CourseSourceEnum[]
  durationTypeList?: DurationTypeEnum[]
}

/** 分页结果通用接口 */
export interface PageResult<T> {
  list: T[]
  total: number
}

/** 课程评分请求 */
export interface CourseStarVO {
  courseId: number
  star: number
}

/** 我的课程请求 */
export interface MyCourseVO {
  courseId: number
}

/** ----- UTILITY FUNCTIONS ----- */

/** 格式化课程时长（秒转换为小时分钟） */
export const formatCourseDuration = (seconds?: number): string => {
  if (!seconds) return ''
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`
}

/** 获取课程状态标签 */
export const getCourseStatusLabel = (status: CourseStatusEnum): string => {
  return CourseStatusLabels[status] || 'Unknown'
}

/** 获取课程类型标签 */
export const getCourseTypeLabel = (type: CourseTypeEnum): string => {
  return CourseTypeLabels[type] || 'Unknown'
}

/** 获取学习状态标签 */
export const getStudyStatusLabel = (status: StudyStatusEnum): string => {
  return StudyStatusLabels[status] || 'Unknown'
}

/** 获取课程级别标签 */
export const getCourseLevelLabel = (level: CourseLevelEnum): string => {
  return CourseLevelLabels[level] || 'All Levels'
}

/** 获取语言标签 */
export const getLanguageLabel = (language: LanguageEnum): string => {
  return LanguageLabels[language] || 'English'
}

/** 获取课程来源标签 */
export const getCourseSourceLabel = (source: CourseSourceEnum): string => {
  return CourseSourceLabels[source] || 'Local'
}

/** 获取课程时效标签 */
export const getCourseEffectiveLabel = (effectiveDay: number): string => {
  if (effectiveDay === -1) return 'Permanent'
  if (effectiveDay > 0) return `${effectiveDay} days`
  return ''
}

/** 检查课程是否有证书 */
export const hasCertificate = (course: Partial<CourseRespVO>): boolean => {
  return !!(course.isCertificateGenerated && course.certificateUrl)
}

/** 检查课程是否有考试 */
export const hasExam = (course: Partial<CourseRespVO>): boolean => {
  return course.exam === ExamEnum.YES || (course.examNum && course.examNum > 0) || false
}

/** ----- API FUNCTIONS ----- */

export const CourseApi = {
  // 获取课程详情
  getCourse: async (id: number) => {
    return await request.appGet<AppCourseRespVO>({
      url: `/learning/course/get?id=${id}`
    })
  },

  // 获取课程列表
  getCourseList: async (params: CourseQueryParams) => {
    return await request.appGet<PageResult<CourseRespVO>>({
      url: `/learning/course/list`,
      params
    })
  },

  // 获取待处理课程
  getTodoCourses: async (params: CourseQueryParams) => {
    return await request.appGet<PageResult<CourseRespVO>>({
      url: `/learning/course/todoList`,
      params
    })
  },

  // 获取已完成课程
  getDoneCourses: async (params: DoneCourseQueryParams) => {
    return await request.appGet<PageResult<CourseRespVO>>({
      url: `/learning/course/doneList`,
      params
    })
  },

  // 获取所有课程专题
  getAllTopics: async () => {
    return await request.appGet<CourseTopicVO[]>({
      url: `/learning/course-topic/alltopics`
    })
  },

  // 获取课程专题列表（用于下拉选择）
  getSubjects: async () => {
    return await request.appGet<SubjectVO[]>({
      url: `/learning/course-topic/subjects`
    })
  },

  // 获取课程章节
  getCourseChapters: async (courseId: number) => {
    return await request.appGet({
      url: `/learning/course/chapter?courseId=${courseId}`
    })
  },

  // 获取所有课程专题
  getAllTopics: async () => {
    return await request.appGet<CourseTopicVO[]>({
      url: `/learning/course-topic/alltopics`
    })
  },

  // 添加到我的课程
  addToMyCourse: async (data: MyCourseVO) => {
    return await request.appPost({
      url: `/learning/course/mycourse`,
      data
    })
  },

  // 从我的课程中移除
  removeFromMyCourse: async (courseId: number) => {
    return await request.appDelete({
      url: `/learning/course/mycourse?courseId=${courseId}`
    })
  },

  // 课程评分
  starCourse: async (data: CourseStarVO) => {
    return await request.appPost({
      url: `/learning/course/star`,
      data
    })
  },

  // 增加课程访问量
  addPageView: async (courseId: number) => {
    return await request.appPost({
      url: `/learning/course/pageView?courseId=${courseId}`
    })
  }
}
