// Survey module type definitions

// ==================== Enum Definitions ====================

/** Question type enum */
export enum SurveyQuestionTypeEnum {
  /** Single choice */
  SINGLE_CHOICE = 1,
  /** Multiple choice */
  MULTIPLE_CHOICE = 2,
  /** True/False */
  TRUE_FALSE = 3,
  /** Rating */
  RATING = 4,
  /** File upload */
  FILE_UPLOAD = 5,
  /** Text */
  TEXT = 6
}

/** Survey instance status enum */
export enum SurveyInstanceStatusEnum {
  /** Unpublished */
  UNPUBLISHED = 0,
  /** In progress */
  IN_PROGRESS = 1,
  /** Ended */
  ENDED = 2,
  /** Draft */
  DRAFT = 3
}

/** Submission frequency enum */
export enum SurveySubmissionFrequencyEnum {
  /** Single submission */
  SINGLE = 1,
  /** Multiple submissions */
  MULTIPLE = 2,
  /** Unlimited submissions */
  UNLIMITED = 3
}

/** Response status enum */
export enum SurveyResponseStatusEnum {
  /** Submitted */
  SUBMITTED = 1,
  /** Draft */
  DRAFT = 2
}

// ==================== Basic Interface Definitions ====================

/** Basic pagination request interface */
export interface PageReqVO {
  pageNo?: number
  pageSize?: number
}

/** Basic pagination response interface */
export interface PageResult<T> {
  list: T[]
  total: number
}

/** Common response interface */
export interface CommonResult<T> {
  code: number
  data: T
  msg: string
}

// ==================== Survey Related Interfaces ====================

/** Survey instance response VO */
export interface SurveyInstanceVO {
  /** Primary key ID */
  id: number
  /** Survey name */
  name: string
  /** Survey description */
  description: string
  /** Start time */
  startTime: string
  /** End time */
  endTime: string
  /** Status */
  status: SurveyInstanceStatusEnum
  /** Status name */
  statusName: string
  /** Response count */
  responseCount: number
  /** Maximum responses */
  maxResponses: number
  /** Whether anonymous participation is allowed */
  anonymousEnabled: boolean
  /** Submission frequency */
  submissionFrequency: SurveySubmissionFrequencyEnum
  /** Submission frequency name */
  submissionFrequencyName: string
  /** Maximum submissions */
  maxSubmissions: number
  /** Whether users can view statistics */
  allowViewStatistics: boolean
  /** Survey configuration */
  config?: any
  /** Question list */
  questions?: SurveyQuestionVO[]
  /** Whether user can participate */
  canParticipate: boolean
  /** Whether user can submit */
  canSubmit: boolean
  /** User submission count */
  userSubmissionCount: number
  /** Creator name */
  creatorName?: string
}

/** Question response VO */
export interface SurveyQuestionVO {
  /** Primary key ID */
  id: number
  /** Question type */
  questionType: SurveyQuestionTypeEnum
  /** Question type name */
  questionTypeName: string
  /** Question title */
  title: string
  /** Question description */
  description: string
  /** Whether required */
  required: boolean
  /** Sort field */
  sort: number
  /** Question configuration */
  config: any
}

/** Question option configuration */
export interface QuestionOption {
  /** Option text */
  text: string
  /** Option value */
  value: string
  /** Option score */
  score?: number
  /** Option description */
  description?: string
}

// ==================== Request Parameter Interfaces ====================

/** Get available survey list request parameters */
export interface AvailableInstancesReqVO extends PageReqVO {
  /** Survey name */
  name?: string
  /** Survey category ID */
  categoryId?: number
  /** Survey status */
  status?: SurveyInstanceStatusEnum
}

/** Survey answer request VO */
export interface SurveyAnswerReqVO {
  /** Question ID */
  questionId: number
  /** Answer display text */
  answerText: string
  /** Answer value */
  answerValue: string
  /** File URL (deprecated, use answerValue to store file ID) */
  fileUrl?: string | null
}

/** Submit survey request VO */
export interface SurveySubmitReqVO {
  /** Survey instance ID */
  instanceId: number
  /** Answer array */
  answers: SurveyAnswerReqVO[]
}

// ==================== Response Interfaces ====================

/** Response record response VO */
export interface SurveyResponseVO {
  /** Primary key ID */
  id: number
  /** Survey instance ID */
  instanceId: number
  /** Survey instance name */
  instanceName: string
  /** Submit time */
  submitTime: string
  /** Total score */
  totalScore: number
  /** Status */
  status: SurveyResponseStatusEnum
  /** Status name */
  statusName: string
  /** Submission sequence (which submission) */
  submissionSequence: number
  /** Whether is latest submission */
  isLatest: boolean
}

/** Response record detail response VO */
export interface SurveyResponseDetailVO extends SurveyResponseVO {
  /** Answer details */
  answers: SurveyAnswerDetailVO[]
}

/** Answer detail response VO */
export interface SurveyAnswerDetailVO {
  /** Primary key ID */
  id: number
  /** Question ID */
  questionId: number
  /** Question title */
  title: string
  /** Question type */
  questionType: SurveyQuestionTypeEnum
  /** Question type name */
  questionTypeName: string
  /** Whether required */
  required: boolean
  /** Sort number */
  sort: number
  /** Question configuration */
  config: any
  /** User answer display text */
  answerText: string
  /** User answer value */
  answerValue: string
  /** Score for this question */
  score: number
}

/** Statistics data response VO */
export interface SurveyStatisticsVO {
  /** Survey instance ID */
  instanceId: number
  /** Survey instance name */
  instanceName: string
  /** Total responses */
  totalResponses: number
  /** Valid responses */
  validResponses: number
  /** Average score */
  averageScore: number
  /** Completion rate */
  completionRate: number
  /** Question statistics */
  questionStatistics?: QuestionStatisticsVO[]
}

/** Question statistics VO */
export interface QuestionStatisticsVO {
  /** Question ID */
  questionId: number
  /** Question title */
  questionTitle: string
  /** Question type */
  questionType: SurveyQuestionTypeEnum
  /** Total answers */
  totalAnswers: number
  /** Average score */
  averageScore: number
  /** Option statistics */
  optionStatistics: OptionStatisticsVO[]
}

/** Option statistics VO */
export interface OptionStatisticsVO {
  /** Option text */
  optionText: string
  /** Option value */
  optionValue: string
  /** Selection count */
  count: number
  /** Selection percentage */
  percentage: number
}

// ==================== Utility Types ====================

/** Question type mapping */
export const QuestionTypeMap = {
  [SurveyQuestionTypeEnum.SINGLE_CHOICE]: 'Single Choice',
  [SurveyQuestionTypeEnum.MULTIPLE_CHOICE]: 'Multiple Choice',
  [SurveyQuestionTypeEnum.TRUE_FALSE]: 'True/False',
  [SurveyQuestionTypeEnum.RATING]: 'Rating',
  [SurveyQuestionTypeEnum.FILE_UPLOAD]: 'File Upload',
  [SurveyQuestionTypeEnum.TEXT]: 'Text'
}

/** Instance status mapping */
export const InstanceStatusMap = {
  [SurveyInstanceStatusEnum.UNPUBLISHED]: 'Unpublished',
  [SurveyInstanceStatusEnum.IN_PROGRESS]: 'In Progress',
  [SurveyInstanceStatusEnum.ENDED]: 'Ended',
  [SurveyInstanceStatusEnum.DRAFT]: 'Draft'
}

/** Submission frequency mapping */
export const SubmissionFrequencyMap = {
  [SurveySubmissionFrequencyEnum.SINGLE]: 'Single Submission',
  [SurveySubmissionFrequencyEnum.MULTIPLE]: 'Multiple Submissions',
  [SurveySubmissionFrequencyEnum.UNLIMITED]: 'Unlimited Submissions'
}
