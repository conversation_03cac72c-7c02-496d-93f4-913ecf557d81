import request from '@/config/axios'
import type {
  AvailableInstancesReqVO,
  SurveyInstanceVO,
  SurveySubmitReqVO,
  SurveyResponseVO,
  SurveyResponseDetailVO,
  SurveyStatisticsVO,
  PageResult,
  CommonResult,
  PageReqVO
} from './types'

// ==================== Survey Instance Related APIs ====================

/**
 * Get list of survey instances available for user participation
 * @param params Query parameters
 * @returns Survey instance list
 */
export const getAvailableInstances = (params: AvailableInstancesReqVO) => {
  return request.get<PageResult<SurveyInstanceVO>>({
    url: '/system/survey/available-instances',
    params
  })
}

/**
 * Get survey instance details
 * @param id Survey instance ID
 * @returns Survey instance details
 */
export const getInstance = (id: number) => {
  return request.get<SurveyInstanceVO>({
    url: `/system/survey/instance/${id}`
  })
}

/**
 * Check user permissions for survey instance
 * @param instanceId Survey instance ID
 * @returns Permission information
 */
export const checkPermissions = (instanceId: number) => {
  return request.get<{
    canParticipate: boolean
    canSubmit: boolean
    canViewStatistics: boolean
    userSubmissionCount: number
    maxSubmissions: number
    submissionFrequency: number
  }>({
    url: `/system/survey/instance/${instanceId}/permissions`
  })
}

/**
 * Check if statistics can be viewed
 * @param instanceId Survey instance ID
 * @returns Whether statistics can be viewed
 */
export const canViewStatistics = (instanceId: number) => {
  return request.get<boolean>({
    url: `/system/survey/can-view-statistics/${instanceId}`
  })
}

// ==================== Survey Submission Related APIs ====================

/**
 * Submit survey
 * @param data Submission data
 * @returns Submission result
 */
export const submitSurvey = (data: SurveySubmitReqVO) => {
  return request.post<CommonResult<any>>({
    url: '/system/survey/submit',
    data
  })
}

/**
 * Save survey draft
 * @param data Draft data
 * @returns Save result
 */
export const saveDraft = (data: SurveySubmitReqVO) => {
  return request.post<CommonResult<any>>({
    url: '/system/survey/save-draft',
    data
  })
}

// ==================== Response Record Related APIs ====================

/**
 * Get user's response record list
 * @param params Query parameters
 * @returns Response record list
 */
export const getMyResponses = (
  params: PageReqVO & {
    instanceId?: number
    status?: number
  }
) => {
  return request.get<PageResult<SurveyResponseVO>>({
    url: '/system/survey/my-responses',
    params
  })
}

/**
 * Get all user response records for specified survey
 * @param instanceId Survey instance ID
 * @returns Response record list
 */
export const getMyAllResponses = (instanceId: number) => {
  return request.get<SurveyResponseVO[]>({
    url: `/system/survey/my-all-responses/${instanceId}`
  })
}

/**
 * Get response record details
 * @param responseId Response record ID
 * @returns Response record details
 */
export const getResponseDetail = (responseId: number) => {
  return request.get<SurveyResponseDetailVO>({
    url: `/system/survey/response-detail/${responseId}`
  })
}

/**
 * Delete response record (if allowed)
 * @param responseId Response record ID
 * @returns Delete result
 */
export const deleteResponse = (responseId: number) => {
  return request.delete<CommonResult<any>>({
    url: `/system/survey/response/${responseId}`
  })
}

// ==================== Statistics Related APIs ====================

/**
 * Get survey statistics data
 * @param instanceId Survey instance ID
 * @returns Statistics data
 */
export const getStatistics = (instanceId: number) => {
  return request.get<SurveyStatisticsVO>({
    url: `/system/survey/statistics/${instanceId}`
  })
}

/**
 * Get user ranking information in survey
 * @param instanceId Survey instance ID
 * @returns Ranking information
 */
export const getUserRanking = (instanceId: number) => {
  return request.get<{
    userScore: number
    userRank: number
    totalParticipants: number
    percentile: number
  }>({
    url: `/system/survey/statistics/${instanceId}/user-ranking`
  })
}

// ==================== Utility Functions ====================

/**
 * Format survey status
 * @param status Status value
 * @returns Status text
 */
export const formatSurveyStatus = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: 'Unpublished',
    1: 'In Progress',
    2: 'Ended',
    3: 'Draft'
  }
  return statusMap[status] || 'Unknown Status'
}

/**
 * Format submission frequency
 * @param frequency Submission frequency value
 * @returns Submission frequency text
 */
export const formatSubmissionFrequency = (frequency: number): string => {
  const frequencyMap: Record<number, string> = {
    1: 'Single Submission',
    2: 'Multiple Submissions',
    3: 'Unlimited Submissions'
  }
  return frequencyMap[frequency] || 'Unknown'
}

/**
 * Format response status
 * @param status Response status value
 * @returns Response status text
 */
export const formatResponseStatus = (status: number): string => {
  const statusMap: Record<number, string> = {
    1: 'Submitted',
    2: 'Draft'
  }
  return statusMap[status] || 'Unknown'
}

/**
 * Format question type
 * @param type Question type value
 * @returns Question type text
 */
export const formatQuestionType = (type: number): string => {
  const typeMap: Record<number, string> = {
    1: 'Single Choice',
    2: 'Multiple Choice',
    3: 'True/False',
    4: 'Rating',
    5: 'File Upload',
    6: 'Text'
  }
  return typeMap[type] || 'Unknown Type'
}

/**
 * Check if survey can be participated
 * @param survey Survey instance
 * @returns Whether can participate
 */
export const canParticipateSurvey = (survey: SurveyInstanceVO): boolean => {
  const now = new Date()
  const startTime = new Date(survey.startTime)
  const endTime = new Date(survey.endTime)

  return (
    survey.status === 1 && // In Progress
    now >= startTime &&
    now <= endTime &&
    survey.canParticipate &&
    (survey.maxResponses === 0 || survey.responseCount < survey.maxResponses)
  )
}

/**
 * Check if user can still submit
 * @param survey Survey instance
 * @returns Whether can submit
 */
export const canSubmitSurvey = (survey: SurveyInstanceVO): boolean => {
  if (!canParticipateSurvey(survey)) {
    return false
  }

  // Check submission frequency limit
  if (survey.submissionFrequency === 1) {
    // Single submission
    return survey.userSubmissionCount === 0
  } else if (survey.submissionFrequency === 2) {
    // Multiple submissions
    return survey.userSubmissionCount < survey.maxSubmissions
  } else {
    // Unlimited submissions
    return true
  }
}

/**
 * Get survey remaining time
 * @param endTime End time
 * @returns Remaining time description
 */
export const getRemainingTime = (endTime: string): string => {
  const now = new Date()
  const end = new Date(endTime)
  const diff = end.getTime() - now.getTime()

  if (diff <= 0) {
    return 'Ended'
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) {
    return `${days} days ${hours} hours`
  } else if (hours > 0) {
    return `${hours} hours ${minutes} minutes`
  } else {
    return `${minutes} minutes`
  }
}

// Export all API functions
export const SurveyApi = {
  // Survey instances
  getAvailableInstances,
  getInstance,
  checkPermissions,
  canViewStatistics,

  // Survey submission
  submitSurvey,
  saveDraft,

  // Response records
  getMyResponses,
  getMyAllResponses,
  getResponseDetail,
  deleteResponse,

  // Statistics data
  getStatistics,
  getUserRanking,

  // Utility functions
  formatSurveyStatus,
  formatSubmissionFrequency,
  formatResponseStatus,
  formatQuestionType,
  canParticipateSurvey,
  canSubmitSurvey,
  getRemainingTime
}

export default SurveyApi
