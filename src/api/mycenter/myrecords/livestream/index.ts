import request from '@/config/axios'

export interface LiveRoomReqVO {
  pageNo: number
  pageSize: number
  name: string
  statusList: string
  startTime: string
  sortRule: string
  userId: string
}

export interface LiveRoomRespVO {
  id?: number
  cover: string
  name: string
  userId: number
  nickname: string
  status: number
  statusName: string
  privacy: number
  startTime: Date
  createTime: Date
  actualStartTime: Date
  speakers: speakersVO[]
  reservationTotal: number
  reserved: boolean
  joinType: number
  joinTypeName: string
  liveLinkUrl: string
  favourite: number
}

export interface speakersVO {
  userId: number
  nickname: string
  avatar: string
  deptId: number
  deptName: string
  joinType: number
  role: number
  attendance: boolean
}

/**
 * 直播状态枚举
 */
export enum LiveStatusEnum {
  TO_BE_RELEASED = 10,
  TO_BE_BROADCAST = 20,
  LIVE_BROADCAST = 50,
  CLOSED = 90
}

// Live Stream API 直播
export const LiveStreamApi = {
  // 分页查询我参与的直播房间信息
  getMyLiveRoomPage: async (params: LiveRoomReqVO) => {
    return await request.appGet<PageResult<LiveRoomRespVO[]>>({
      url: `/live/room/participated-page`,
      params
    })
  }
}
