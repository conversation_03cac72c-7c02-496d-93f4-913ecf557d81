import request from '@/config/axios'

export interface CertificateReqVO {
  pageNo: number // 页码
  pageSize: number // 页大小
  certificateId: number
  userId: number
  type: number
  number: number
  isRevoke: number
  validity: number
  expiresTime: Date
  image: string
  taskId: number
  createTime: Date
}

export interface CertificateRespVO {
  id: number
  certificateId: number
  userId: number
  type: number
  number: number
  isRevoke: number
  validity: number
  expiresTime: Date
  image: string
  taskId: number
  createTime: Date
  name: string
  numberPrefix: string
  logo: string
  officialSeal: string
  certificateImage: string
  expiresType?: number
}

/** 定义证书状态枚举值 */
export enum CertificateStatusEnum {
  EXPIRED = 1, // 已过期的证书
  VALID = 2 // 有效的证书
}
/** 证书类型枚举值 1:课程学习 2:完成任务 3:参加培训 4:手动颁发 5:参加考试 */
export enum CertificateTypeEnum {
  COURSE_LEARNING = 1, // 课程学习
  COMPLETE_THE_TASK = 2, // 完成任务
  JOIN_TRAINING = 3,
  MANUAL_ISSUANCE = 4,
  JOIN_EXAM = 5
}

/** 证书过期类型枚举值 1是已过期的证书，2是有效期的证书 */
export enum CertificateExpireTypeEnum {
  EXPIRED = 1, // 已过期
  VALID = 2 // 有效期
}

/** ----- 我的证书 ----- */
export const MyCertificateApi = {
  // 获得用户获得证书分页
  getMyCertificatePage: async (params: CertificateReqVO) => {
    return await request.appGet<PageResult<CertificateRespVO[]>>({
      url: '/system/certificate-user/page',
      params
    })
  }
}
