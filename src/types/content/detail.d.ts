import { ChapterStatus } from '@/enums/chapter'

export type ScromVersion =
  | '1.2'
  | '2004 1st Edition'
  | '2004 2nd Edition'
  | '2004 3rd Edition'
  | '2004 4th Edition'

export interface CourseDetail {
  createBy: string
  createTime: string
  updateBy: string
  updateTime: string
  remark: string | null
  pageNum: number
  pageSize: number
  id: number
  topicId: number
  name: string
  cover: string
  keywords: string
  introduction: string
  deptCode: string // 或者可以是空字符串 ""
  status: number
  shelfTime: string | null // 可能是日期字符串或者 null
  effectiveDay: number
  autoAssign: boolean
  enrollNumber: number
  pageView: number
  scopes: any // 可以根据实际情况定义更具体的类型
  topic: any // 可以根据实际情况定义更具体的类型
  type: any // 可以根据实际情况定义更具体的类型
  deadline: string | null // 可能是日期字符串或者 null
  star: number
  myStar: number
}
export interface Attachment {
  pageNum: number // 当前页码
  pageSize: number // 页大小
  createId: string | null // 创建者 ID（可能为空）
  createBy: string | null // 创建者姓名（可能为空）
  createTime: string // 创建时间
  updateId: string | null // 更新者 ID（可能为空）
  updateBy: string | null // 更新者姓名（可能为空）
  updateTime: string // 更新时间
  remark: string | null // 备注（可能为空）
  id: number // 文件 ID
  chapterId: number // 章节 ID
  fileId: number // 文件 ID
  fileUrl: string // 文件 URL
  fileName: string //
}
export interface CourseChapter {
  createId: number | null
  createBy: string
  createTime: string
  updateId: number | null
  updateBy: string | null
  updateTime: string | null
  remark: string | null
  pageNum: number
  pageSize: number
  id: number
  courseId: number
  sort: number
  title: string
  type: number
  scormVersion: ScromVersion | null
  fileId: number | null
  fileUrl: string | null
  contentExamId: number | null
  attachments: Attachment[] // 适当根据实际情况定义attachments的类型
  status: ChapterStatus | null
  scormRunPath: string
  aiccRunPath: string
  isRecord: boolean
}
