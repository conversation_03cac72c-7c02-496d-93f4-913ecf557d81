/**
 * Course related TypeScript type definitions
 *
 * Note: Core course types and enums are now defined in @/api/learning/course
 * This file maintains backward compatibility and UI-specific types
 */

// Re-export core types from API
export * from '@/api/learning/course'

// Legacy course item interface (for backward compatibility)
export interface CourseItem {
  id: number
  name: string
  cover?: string | null
  introduction?: string
  keywords?: string
  status?: number
  type?: number | string | null
  topic?: string
  topicId?: number
  parentTopicId?: number
  deptCode?: string
  shelfTime?: string
  effectiveDay?: number
  autoAssign?: boolean | null
  enrollNumber?: number
  pageView?: number
  scopes?: any
  deadline?: string | null
  createBy?: string | null
  createTime?: string | null
  updateBy?: string | null
  updateTime?: string | null
  remark?: string | null
  pageNum?: number
  pageSize?: number
  // Extended properties for UI
  progress?: number
  duration?: number
  studyCount?: number
  level?: number
  category?: string
  noteCount?: number
  codeCount?: number
  qaCount?: number
  star?: number
  myStar?: number
  studyStatus?: number
  teacherName?: string
  topicName?: string
  language?: number
  isRecommend?: boolean
  isNew?: boolean
}

// Query parameters for course API (legacy - use API version for new code)
export interface CourseQueryParams {
  name?: string
  topicId?: string
  parentTopicId?: string | number
  pageNum: number
  pageSize: number
  type?: number
  status?: string
  studyStatus?: number // 学习状态过滤
  languageList?: string[]
  levelList?: string[]
  subtitleList?: string[]
  sourceList?: string[]
  durationTypeList?: string[]
}

// API response interface
export interface CourseListResponse {
  list: CourseItem[]
  total: number
}

// Course topic interface
export interface CourseTopicVO {
  id: string
  parentId: number
  ancestors: string
  name: string
  keywords: string
  introduction: string
  sort: number
  cover: string
  pageNum: number
  pageSize: number
  params: Object
  createId: string
  creator: string
  createBy: string
  createTime: string
  updateId: string
  updater: string
  updateTime: string
  remark: string
}

// Note: Enums are now exported from @/api/learning/course
// These are kept for backward compatibility but should use the API versions

// Filter options
export interface CourseFilterOptions {
  status: Array<{ label: string; value: string }>
  type: Array<{ label: string; value: number }>
  level: Array<{ label: string; value: number }>
  language: Array<{ label: string; value: number }>
}

// Pagination interface
export interface PaginationData {
  total: number
  currentPage: number
  pageSize: number
}

// Course card props interface
export interface CourseCardProps {
  course: CourseItem
}

// Course card events interface
export interface CourseCardEmits {
  (e: 'click', course: CourseItem): void
}

// Tab configuration interface
export interface TabConfig {
  key: string
  label: string
}

// Status filter mapping
export const StatusFilterMap = {
  all: '',
  'not-started': 'not_started',
  'in-progress': 'in_progress',
  completed: 'completed',
  paused: 'paused',
  failed: 'failed'
} as const

// Note: Labels and enums are now exported from @/api/learning/course
// These legacy definitions are kept for backward compatibility
