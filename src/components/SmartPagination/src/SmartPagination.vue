<script setup lang="ts">
import {ChevronsLeft, ChevronLeftIcon, ChevronRightIcon, ChevronsRight} from 'lucide-vue-next'
const props = defineProps<{
  total: number
  currentPage: number
  pageSize: number
}>()

const emit = defineEmits(['current-change']) // 向父组件中传递
const totalPage = computed(() => Math.ceil(props.total / props.pageSize))
</script>

<template>
  <div class="flex items-center justify-between">
    <div class="flex-1 text-sm font-medium">
      Total {{total}}
    </div>
    <div class="flex items-center space-x-6 lg:space-x-8">

      <div class="flex w-[100px] items-center justify-center text-sm font-medium">
        Page {{ totalPage === 0 ? 0 : currentPage }} of
        {{ totalPage }}
      </div>
      <div class="flex items-center space-x-2">
        <Button
          variant="outline"
          class="hidden h-8 w-8 p-0 lg:flex cursor-pointer"
          :disabled="currentPage === 1 || totalPage === 0"
          @click="emit('current-change', 1)"
        >
          <span class="sr-only">Go to first page</span>
          <ChevronsLeft class="size-4"/>
        </Button>
        <Button
          variant="outline"
          class="h-8 w-8 p-0 cursor-pointer"
          :disabled="currentPage === 1 || totalPage === 0"
          @click="emit('current-change', currentPage - 1)"
        >
          <span class="sr-only">Go to previous page</span>
          <ChevronLeftIcon class="size-4"/>
        </Button>
        <Button
          variant="outline"
          class="h-8 w-8 p-0 cursor-pointer"
          :disabled="currentPage === totalPage || totalPage === 0"
          @click="emit('current-change', currentPage + 1)"
        >
          <span class="sr-only">Go to next page</span>
          <ChevronRightIcon class="size-4"/>
        </Button>
        <Button
          variant="outline"
          class="hidden h-8 w-8 p-0 lg:flex cursor-pointer"
          :disabled="currentPage === totalPage || totalPage === 0"
          @click="emit('current-change', totalPage)"
        >
          <span class="sr-only">Go to last page</span>
          <ChevronsRight class="size-4"/>
        </Button>
      </div>
    </div>
  </div>
</template>
