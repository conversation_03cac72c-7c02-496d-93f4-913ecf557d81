<script setup lang="ts">
defineProps({})
</script>

<template>
  <div class="min-h-[inherit] w-full flex justify-center items-center flex-col">
    <div class="text-center">
      <!-- 如果没有传入 image 插槽，则使用默认图片 -->
      <slot name="image">
        <img
          src="@/assets/images/content/detail/CertificateEmpty.png"
          alt="Empty State"
          class="max-w-[150px] mb-4"
        />
      </slot>

      <div class="mt-2">
        <!-- 插槽传入描述内容 -->
        <slot name="description">
          <p class="text-[#636363]">No data</p>
        </slot>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 可以在这里添加自定义样式 */
</style>
