<template>
  <div class="space-y-3 group" :class="cardClass">
    <!-- 简化版本 - 暂时移除右键菜单 -->
    <div
      class="relative overflow-hidden rounded-md cursor-pointer transition-transform hover:scale-105"
      @click="handleClick"
    >
      <slot name="image">
        <img
          :src="data.cover || data.image"
          :alt="data.name || data.title"
          :class="imageClass"
          class="w-full h-auto object-cover"
        />
      </slot>

      <!-- 状态标签 -->
      <slot name="badges">
        <div v-if="showBadges" class="absolute inset-0">
          <!-- 推荐/特色标签 -->
          <div v-if="data.isRecommended || data.isFeatured" class="absolute -left-px top-[-6px]">
            <div class="text-6xl">🏆</div>
          </div>

          <!-- 新内容标签 -->
          <div
            v-if="data.isNew"
            class="absolute top-[-6px]"
            :class="data.isRecommended || data.isFeatured ? 'left-[40px]' : '-left-px'"
          >
            <div class="text-6xl">✨</div>
          </div>

          <!-- 状态标签 -->
          <div
            v-if="data.status !== undefined && showStatus"
            class="w-9 h-9 absolute top-0 right-6 z-10 flex justify-center items-center shadow-[0_4px_4px_0px_rgba(0,0,0,0.25)] bg-blue-500 rounded"
          >
            <div class="z-10 text-xl text-white">{{ getStatusIcon(data.status) }}</div>
          </div>
        </div>
      </slot>

      <!-- 底部覆盖层信息 -->
      <slot name="overlay">
        <div v-if="showOverlay" class="absolute bottom-2 left-0 right-0 px-2 flex justify-between">
          <!-- 左侧信息 -->
          <div v-if="leftBadgeText" class="bg-transparent text-white px-2 py-1 rounded text-xs">
            {{ leftBadgeText }}
          </div>

          <!-- 右侧信息 -->
          <div v-if="rightBadgeText" class="bg-black/30 text-white px-2 py-1 rounded text-xs">
            {{ rightBadgeText }}
          </div>
        </div>
      </slot>
    </div>

    <!-- 卡片内容区域 -->
    <div class="space-y-1">
      <slot name="content">
        <!-- 标题 -->
        <div>
          <h3
            class="font-medium leading-none line-clamp-2 cursor-pointer"
            :title="data.name || data.title"
          >
            {{ data.name || data.title }}
          </h3>
        </div>

        <!-- 副标题/描述 -->
        <div v-if="showSubtitle" class="text-sm text-gray-600">
          <slot name="subtitle">
            {{ data.subtitle || data.description }}
          </slot>
        </div>

        <!-- 评分 -->
        <div v-if="showRating && (data.rating || data.star)" class="flex items-center">
          <div class="flex">
            <span v-for="i in 5" :key="i" class="text-yellow-400">
              {{ i <= (data.rating || data.star || 0) ? '★' : '☆' }}
            </span>
          </div>
          <span class="ml-1 text-sm text-gray-600">{{ data.rating || data.star }}</span>
        </div>

        <!-- 进度条 -->
        <div v-if="showProgress && data.progress !== undefined">
          <div class="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{{ data.progress }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${data.progress}%` }"
            ></div>
          </div>
        </div>

        <!-- 作者信息 -->
        <div v-if="showAuthor && data.author" class="text-sm text-gray-600">
          <span class="font-medium">{{ data.author }}</span>
        </div>

        <!-- 标签 -->
        <div v-if="showTags && data.tags && data.tags.length > 0" class="flex flex-wrap gap-1">
          <span
            v-for="tag in data.tags.slice(0, 3)"
            :key="tag"
            class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
          >
            {{ tag }}
          </span>
        </div>

        <!-- 日期信息 -->
        <div v-if="showDate && (data.createdAt || data.updatedAt)" class="text-xs text-gray-500">
          {{ new Date(data.createdAt || data.updatedAt).toLocaleDateString() }}
        </div>

        <!-- 自定义元数据 -->
        <div v-if="showMeta && metaFields.length > 0" class="space-y-1">
          <div class="flex items-center gap-4 text-xs text-gray-600">
            <span v-for="field in metaFields" :key="field" class="flex items-center gap-1">
              <span class="font-medium">{{ field }}:</span>
              <span>{{ data[field] }}</span>
            </span>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'

// 通用内容数据接口
interface ContentData {
  id: string | number
  name?: string
  title?: string
  cover?: string
  image?: string
  thumbnail?: string
  subtitle?: string
  description?: string
  isRecommended?: boolean
  isNew?: boolean
  isFeatured?: boolean
  status?: number
  rating?: number
  star?: number
  progress?: number
  duration?: number
  author?: string
  category?: string
  tags?: string[]
  createdAt?: string
  updatedAt?: string
  [key: string]: any
}

interface Props {
  data: ContentData

  // 显示控制
  mode?: 'card' | 'list' | 'grid' | 'media' | 'custom'
  variant?: 'default' | 'compact' | 'detailed' | 'minimal'
  showBadges?: boolean
  showStatus?: boolean
  showOverlay?: boolean
  showSubtitle?: boolean
  showRating?: boolean
  showProgress?: boolean
  showMeta?: boolean
  showAuthor?: boolean
  showDate?: boolean
  showTags?: boolean

  // 样式控制
  cardClass?: string
  imageClass?: string
  aspectRatio?: 'square' | '16/9' | '4/3' | '3/2' | 'auto'

  // 功能配置
  routeName?: string
  routeParams?: Record<string, any>
  clickable?: boolean
  hoverable?: boolean

  // 覆盖层信息
  leftBadgeText?: string
  rightBadgeText?: string

  // 元数据字段
  metaFields?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'card',
  variant: 'default',
  showBadges: true,
  showStatus: false,
  showOverlay: false,
  showSubtitle: false,
  showRating: false,
  showProgress: false,
  showMeta: false,
  showAuthor: false,
  showDate: false,
  showTags: false,
  aspectRatio: 'square',
  clickable: true,
  hoverable: true,
  routeName: '',
  metaFields: () => []
})

const emit = defineEmits<{
  click: [data: ContentData]
  action: [action: string, data: ContentData]
}>()

const router = useRouter()

// 获取状态图标 - 通用状态映射
const getStatusIcon = (status: number) => {
  const statusMap = {
    0: '⭕', // 默认/未设置
    1: '📝', // 草稿/未开始
    2: '⏳', // 进行中/处理中
    3: '✅', // 完成/已发布
    4: '⏸️', // 暂停
    5: '❌', // 失败/错误
    6: '🔒', // 锁定/私有
    7: '📋', // 待审核
    8: '🔄', // 同步中
    9: '⭐' // 特色/推荐
  }
  return statusMap[status] || statusMap[0]
}

// 点击处理
const handleClick = () => {
  if (!props.clickable) return

  emit('click', props.data)

  if (props.routeName) {
    const params = props.routeParams || { id: props.data.id }
    router.push({ name: props.routeName, params })
  }
}

// 动作处理
const handleAction = (action: string) => {
  emit('action', action, props.data)
}
</script>
