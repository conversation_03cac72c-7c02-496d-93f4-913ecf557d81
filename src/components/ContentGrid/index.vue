<template>
  <div class="content-grid">
    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-8">
      <slot name="loading">
        <div class="text-gray-500">
          <div class="text-2xl mb-2">⏳</div>
          <div>Loading...</div>
        </div>
      </slot>
    </div>

    <!-- 空状态 -->
    <div
      v-else-if="itemList.length === 0"
      class="flex items-center justify-center h-full min-h-[400px]"
    >
      <slot name="empty">
        <div class="text-center text-gray-500">
          <div class="text-4xl mb-4">{{ computedEmptyIcon }}</div>
          <h3 class="text-lg font-medium mb-2">{{ emptyTitle }}</h3>
          <p class="text-sm">{{ emptyDescription }}</p>
        </div>
      </slot>
    </div>

    <!-- 内容网格/列表 -->
    <div v-else :class="containerClasses">
      <slot
        name="item"
        v-for="item in itemList"
        :key="item.id"
        :item="item"
        :index="itemList.indexOf(item)"
      >
        <ContentCard
          :data="item"
          :mode="ContentDisplayMode.CARD"
          :variant="cardVariant"
          :show-badges="showBadges"
          :show-status="showStatus"
          :show-rating="showRating"
          :show-progress="showProgress"
          :show-meta="showMeta"
          :show-author="showAuthor"
          :show-date="showDate"
          :show-tags="showTags"
          :show-subtitle="showSubtitle"
          :route-name="routeName"
          :meta-fields="metaFields"
          :hoverable="hoverable"
          :clickable="clickable"
          :aspect-ratio="aspectRatio"
          @click="handleItemClick"
          @action="handleItemAction"
        />
      </slot>
    </div>

    <!-- 分页或加载更多 -->
    <div v-if="showPagination && itemList.length > 0" class="mt-6">
      <slot name="pagination">
        <!-- 默认分页组件可以在这里添加 -->
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import ContentCard from '../ContentCard/index.vue'
import {
  ContentDisplayMode,
  ContentVariant,
  LayoutMode,
  AspectRatio,
  ContentType,
  ContentTypeIcons
} from '../../enums/content'

// 通用内容项接口
interface ContentItem {
  id: string | number
  name?: string
  title?: string
  cover?: string
  image?: string
  thumbnail?: string
  rating?: number
  star?: number
  progress?: number
  isRecommended?: boolean
  isNew?: boolean
  isFeatured?: boolean
  status?: number
  author?: string
  category?: string
  tags?: string[]
  createdAt?: string
  updatedAt?: string
  [key: string]: any
}

interface Props {
  // 数据源
  items?: ContentItem[]

  // 显示控制
  mode?: LayoutMode
  cardMode?: ContentDisplayMode
  cardVariant?: ContentVariant

  // 功能开关
  showBadges?: boolean
  showStatus?: boolean
  showRating?: boolean
  showProgress?: boolean
  showMeta?: boolean
  showAuthor?: boolean
  showDate?: boolean
  showTags?: boolean
  showSubtitle?: boolean
  showPagination?: boolean

  // 布局配置
  gridCols?: string
  listCols?: string
  gap?: string
  aspectRatio?: AspectRatio

  // 交互配置
  hoverable?: boolean
  clickable?: boolean

  // 路由配置
  routeName?: string

  // 元数据字段
  metaFields?: string[]

  // 空状态
  emptyTitle?: string
  emptyDescription?: string
  emptyIcon?: string
  contentType?: ContentType

  // 加载状态
  loading?: boolean

  // 自定义类名
  containerClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  mode: LayoutMode.GRID,
  cardMode: ContentDisplayMode.CARD,
  cardVariant: ContentVariant.DEFAULT,
  showBadges: true,
  showStatus: false,
  showRating: false,
  showProgress: false,
  showMeta: false,
  showAuthor: false,
  showDate: false,
  showTags: false,
  showSubtitle: false,
  showPagination: false,
  gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  listCols: 'grid-cols-1',
  gap: 'gap-4',
  aspectRatio: AspectRatio.SQUARE,
  hoverable: true,
  clickable: true,
  routeName: '',
  metaFields: () => [],
  emptyTitle: 'No items found',
  emptyDescription: 'Try adjusting your search or filter criteria',
  emptyIcon: '📋',
  contentType: ContentType.OTHER,
  loading: false,
  containerClass: ''
})

const emit = defineEmits<{
  'item-click': [item: ContentItem]
  'item-action': [action: string, item: ContentItem]
}>()

// 响应式数据
const itemList = ref<ContentItem[]>(props.items || [])

// 计算容器类名
const containerClasses = computed(() => {
  const baseClasses = ['grid', props.gap]

  switch (props.mode) {
    case LayoutMode.LIST:
      baseClasses.push(props.listCols)
      break
    case LayoutMode.MASONRY:
      baseClasses.push(props.gridCols, 'masonry')
      break
    case LayoutMode.CAROUSEL:
      baseClasses.push('flex', 'overflow-x-auto', 'space-x-4')
      break
    default: // LayoutMode.GRID
      baseClasses.push(props.gridCols)
  }

  if (props.containerClass) {
    baseClasses.push(props.containerClass)
  }

  return baseClasses.join(' ')
})

// 计算空状态图标
const computedEmptyIcon = computed(() => {
  if (props.emptyIcon !== '📋') {
    return props.emptyIcon
  }

  // 根据内容类型自动选择图标
  return ContentTypeIcons[props.contentType] || ContentTypeIcons[ContentType.OTHER]
})

// 事件处理
const handleItemClick = (item: ContentItem) => {
  if (!props.clickable) return
  emit('item-click', item)
}

const handleItemAction = (action: string, item: ContentItem) => {
  emit('item-action', action, item)
}

// 监听 props 变化
watch(
  () => props.items,
  (newList) => {
    itemList.value = newList || []
  },
  { immediate: true }
)

// 暴露方法
defineExpose({
  refresh: () => {
    itemList.value = [...(props.items || [])]
  },
  getItems: () => itemList.value
})
</script>

<style scoped>
.masonry {
  column-count: auto;
  column-width: 250px;
  column-gap: 1rem;
}

.masonry > * {
  break-inside: avoid;
  margin-bottom: 1rem;
}
</style>
