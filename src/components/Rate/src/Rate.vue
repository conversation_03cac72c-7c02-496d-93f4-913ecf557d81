<template>
  <div
    class="flex items-center space-x-1 cursor-pointer select-none"
    :class="{ 'pointer-events-none': readonly || disabled }"
  >
    <div
      v-for="i in max"
      :key="i"
      class="relative"
      :style="{ width: size + 'px', height: size + 'px' }"
    >
      <!-- 背景灰星 -->
      <svg :width="size" :height="size" viewBox="0 0 24 24" fill="none" class="text-gray-300">
        <path
          fill="currentColor"
          d="M12 2l3.09 6.26L22 9.27l-5 4.87L18.18 22 12 18.56 5.82 22 7 14.14l-5-4.87 6.91-1.01L12 2z"
        />
      </svg>

      <!-- 前景填充星星，根据评分裁剪宽度 -->
      <div
        class="absolute top-0 left-0 overflow-hidden pointer-events-none"
        :style="{ width: getStarFillWidth(i) + '%', height: size + 'px' }"
      >
        <svg :width="size" :height="size" viewBox="0 0 24 24" fill="none" class="text-yellow-400">
          <path
            fill="currentColor"
            d="M12 2l3.09 6.26L22 9.27l-5 4.87L18.18 22 12 18.56 5.82 22 7 14.14l-5-4.87 6.91-1.01L12 2z"
          />
        </svg>
      </div>

      <!-- 左半透明区域，打半分 -->
      <div
        v-if="!readonly"
        class="absolute top-0 left-0 z-10"
        :style="{ width: size / 2 + 'px', height: size + 'px' }"
        @mouseenter="handleHover(i - 0.5)"
        @mouseleave="handleHover(defaultValue)"
        @click="updateValue(i - 0.5)"
      ></div>

      <!-- 右半透明区域，打整分 -->
      <div
        v-if="!readonly"
        class="absolute top-0 right-0 z-10"
        :style="{ width: size / 2 + 'px', height: size + 'px' }"
        @mouseenter="handleHover(i)"
        @mouseleave="handleHover(defaultValue)"
        @click="updateValue(i)"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue'

interface Props {
  modelValue?: number // 可选的双向绑定值
  max?: number
  size?: number
  disabled?: boolean
  readonly?: boolean // 是否为只读模式，默认为 false
  defaultValue?: number // 评分组件的默认值
}

const props = withDefaults(defineProps<Props>(), {
  max: 5,
  size: 24,
  disabled: false,
  readonly: false,
  defaultValue: 0 // 默认值为0
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: number): void
}>()

const hoverValue = ref(0)

// 默认值，如果没有传入 modelValue，则使用 defaultValue
const value = computed(() => props.modelValue ?? props.defaultValue)

// 用来处理 hover 的状态
function handleHover(value: number) {
  if (!props.readonly && !props.disabled) {
    hoverValue.value = value
  }
}

// 更新评分
function updateValue(value: number) {
  if (!props.readonly && !props.disabled) {
    emit('update:modelValue', value)
  }
}

// 获取填充宽度，支持半星
function getStarFillWidth(i: number): number {
  const currentValue = hoverValue.value || value.value || 0
  if (currentValue >= i) return 100
  if (currentValue >= i - 0.5) return 50
  return 0
}

// 监听 modelValue 的变化，确保支持 v-model
watchEffect(() => {
  if (props.modelValue !== undefined) {
    hoverValue.value = props.modelValue
  }
})
</script>

<style scoped>
/* 自定义样式, 比如加上过渡效果等 */
svg {
  transition: transform 0.2s ease-in-out;
}
</style>
