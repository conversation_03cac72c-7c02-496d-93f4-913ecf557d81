<template>
  <viewer :images="urlList" @inited="inited" class="viewer" ref="viewer">
    <template #default="{ images }">
      <img v-for="src in images" :src="src" :key="src" />
    </template>
  </viewer>
</template>

<script lang="ts" setup>
import 'viewerjs/dist/viewer.css'
import { component as Viewer } from 'v-viewer'
import type ViewerType from 'viewerjs'

defineOptions({ name: 'SuperImageViewer' })

const props = defineProps<{
  urlList: string[]
}>()
const $viewer = ref<ViewerType | null>(null)
const viewerInited = ref(false)

const inited = async (viewer: ViewerType) => {
  $viewer.value = viewer
  await nextTick()
  viewer.show()
}

// 一旦初始化并挂载成功，立即展示图片
watch(viewerInited, async (val) => {
  if (val) {
    await nextTick()
    $viewer.value?.show()
  }
})
</script>
