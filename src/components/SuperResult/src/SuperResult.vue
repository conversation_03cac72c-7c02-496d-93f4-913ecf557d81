<template>
  <div class="p-10 text-center">
    <!-- 图标区域 -->
    <div class="flex justify-center mb-4">
      <slot name="icon">
        <div
          v-if="status"
          :class="['w-20 h-20 rounded-full flex items-center justify-center', iconBgMap[status]]"
        >
          <Icon :name="iconMap[status]" :size="32" class="text-white" />
        </div>
      </slot>
    </div>

    <!-- 主标题 -->
    <div class="mb-2 text-2xl font-semibold">
      <slot name="title">
        {{ title }}
      </slot>
    </div>

    <!-- 副标题 -->
    <div v-if="$slots['sub-title']" class="text-base text-gray-500">
      <slot name="sub-title" />
    </div>

    <!-- 操作区域 -->
    <div v-if="$slots.extra" class="mt-6">
      <slot name="extra" />
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  status?: 'success' | 'error' | 'warning' | 'info'
  title?: string
}>()

const iconMap: Record<string, string> = {
  success: 'CircleCheck',
  error: 'CircleX',
  warning: 'TriangleAlert',
  info: 'Info'
}

const iconBgMap: Record<string, string> = {
  success: 'bg-green-500',
  error: 'bg-red-500',
  warning: 'bg-yellow-500',
  info: 'bg-gray-400'
}
</script>
