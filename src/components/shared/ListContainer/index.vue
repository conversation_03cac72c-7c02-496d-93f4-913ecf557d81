<template>
  <div class="list-container">
    <!-- 搜索和过滤栏 -->
    <div v-if="showFilters" class="mb-6 space-y-4">
      <!-- 搜索框 -->
      <div v-if="showSearch" class="flex items-center gap-4">
        <div class="flex-1 max-w-md">
          <SuperSearch
            :value="queryParams.name || queryParams.search || ''"
            :placeholder="searchPlaceholder"
            @update:value="handleSearch"
          />
        </div>

        <!-- 自定义过滤器插槽 -->
        <slot name="filters" :queryParams="queryParams" :handleFilter="handleFilter" />
      </div>

      <!-- 快速过滤标签 -->
      <div v-if="quickFilters.length > 0" class="flex items-center gap-2">
        <span class="text-sm text-muted-foreground">Quick filters:</span>
        <div class="flex gap-2">
          <Button
            v-for="filter in quickFilters"
            :key="filter.key"
            :variant="isFilterActive(filter) ? 'default' : 'outline'"
            size="sm"
            @click="toggleFilter(filter)"
          >
            {{ filter.label }}
          </Button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      <div v-for="i in 8" :key="i" class="animate-pulse">
        <div class="bg-gray-200 aspect-square rounded-md mb-2"></div>
        <div class="bg-gray-200 h-4 rounded mb-1"></div>
        <div class="bg-gray-200 h-3 rounded w-2/3"></div>
      </div>
    </div>

    <!-- 内容网格 -->
    <div v-else-if="list.length > 0" :class="gridClasses">
      <slot name="item" v-for="item in list" :key="getItemKey(item)" :item="item" :index="list.indexOf(item)" />
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-12">
      <slot name="empty">
        <div class="text-muted-foreground">
          <div class="text-4xl mb-4">📚</div>
          <h3 class="text-lg font-medium mb-2">{{ emptyTitle }}</h3>
          <p class="text-sm">{{ emptyDescription }}</p>
        </div>
      </slot>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination && total > 0" class="mt-8 flex justify-center">
      <SmartPagination
        :current="queryParams.pageNum || queryParams.pageNo || 1"
        :total="total"
        :page-size="queryParams.pageSize || 10"
        @update:current="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, watch, onMounted } from 'vue'
import { Button } from '@/components/ui/button'
import { SuperSearch } from '@/components/SuperSearch'
import { SmartPagination } from '@/components/SmartPagination'

interface QuickFilter {
  key: string
  label: string
  value: any
  field?: string
}

interface Props {
  // 数据
  list: any[]
  total: number
  loading: boolean

  // 查询参数
  queryParams: Record<string, any>

  // 显示控制
  showFilters?: boolean
  showSearch?: boolean
  showPagination?: boolean

  // 网格配置
  gridCols?: string

  // 搜索配置
  searchPlaceholder?: string
  searchField?: string

  // 快速过滤器
  quickFilters?: QuickFilter[]

  // 空状态
  emptyTitle?: string
  emptyDescription?: string

  // 项目键
  itemKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  showFilters: true,
  showSearch: true,
  showPagination: true,
  gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  searchPlaceholder: 'Search...',
  searchField: 'name',
  quickFilters: () => [],
  emptyTitle: 'No items found',
  emptyDescription: 'Try adjusting your search or filter criteria',
  itemKey: 'id'
})

const emit = defineEmits<{
  'update:queryParams': [params: Record<string, any>]
  'search': [query: string]
  'filter': [filters: Record<string, any>]
  'page-change': [page: number]
  'page-size-change': [size: number]
  'refresh': []
}>()

// 网格样式
const gridClasses = computed(() => `grid ${props.gridCols} gap-4`)

// 获取项目键值
const getItemKey = (item: any) => {
  return item[props.itemKey] || item.id || Math.random()
}

// 搜索处理
const handleSearch = (query: string) => {
  const newParams = { ...props.queryParams }
  if (props.searchField) {
    newParams[props.searchField] = query
  }
  newParams.pageNum = 1
  newParams.pageNo = 1

  emit('update:queryParams', newParams)
  emit('search', query)
}

// 过滤处理
const handleFilter = (filters: Record<string, any>) => {
  const newParams = { ...props.queryParams, ...filters }
  newParams.pageNum = 1
  newParams.pageNo = 1

  emit('update:queryParams', newParams)
  emit('filter', filters)
}

// 快速过滤器
const isFilterActive = (filter: QuickFilter) => {
  const field = filter.field || filter.key
  return props.queryParams[field] === filter.value
}

const toggleFilter = (filter: QuickFilter) => {
  const field = filter.field || filter.key
  const newParams = { ...props.queryParams }

  if (isFilterActive(filter)) {
    delete newParams[field]
  } else {
    newParams[field] = filter.value
  }

  newParams.pageNum = 1
  newParams.pageNo = 1

  emit('update:queryParams', newParams)
  emit('filter', { [field]: newParams[field] })
}

// 分页处理
const handlePageChange = (page: number) => {
  const newParams = { ...props.queryParams }
  newParams.pageNum = page
  newParams.pageNo = page

  emit('update:queryParams', newParams)
  emit('page-change', page)
}

const handlePageSizeChange = (size: number) => {
  const newParams = { ...props.queryParams }
  newParams.pageSize = size
  newParams.pageNum = 1
  newParams.pageNo = 1

  emit('update:queryParams', newParams)
  emit('page-size-change', size)
}
</script>

<style scoped>
.list-container {
  @apply w-full;
}
</style>
