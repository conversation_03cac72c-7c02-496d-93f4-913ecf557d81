<template>
  <div class="tab-container">
    <!-- Tab 导航 -->
    <div class="mb-6">
      <Tabs :value="activeTab" @update:value="handleTabChange" class="w-full">
        <TabsList :class="tabsListClass">
          <TabsTrigger
            v-for="tab in tabs"
            :key="tab.value"
            :value="tab.value"
            :class="getTabTriggerClass(tab)"
          >
            <component v-if="tab.icon" :is="tab.icon" class="w-4 h-4 mr-2" />
            {{ tab.label }}
            <Badge v-if="tab.count !== undefined" variant="secondary" class="ml-2 text-xs">
              {{ tab.count }}
            </Badge>
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>

    <!-- Tab 内容 -->
    <div class="tab-content">
      <slot name="content" :activeTab="activeTab" :currentTab="currentTab" />

      <!-- 默认内容渲染 -->
      <template v-if="!$slots.content">
        <component
          v-if="currentTab?.component"
          :is="currentTab.component"
          v-bind="currentTab.props || {}"
        />
        <div v-else-if="currentTab?.content" v-html="currentTab.content" />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted, ref, readonly } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'

interface TabItem {
  label: string
  value: string | number
  icon?: any
  count?: number
  component?: any
  props?: Record<string, any>
  content?: string
  disabled?: boolean
  hidden?: boolean
}

interface Props {
  tabs: TabItem[]
  defaultTab?: string | number
  tabsListClass?: string
  syncUrl?: boolean
  urlParam?: string
}

const props = withDefaults(defineProps<Props>(), {
  tabsListClass: '',
  syncUrl: false,
  urlParam: 'tab'
})

const emit = defineEmits<{
  'update:activeTab': [tab: string | number]
  'tab-change': [tab: TabItem, index: number]
}>()

const route = useRoute()
const router = useRouter()

// 当前激活的 tab
const activeTab = ref<string | number>(props.defaultTab || props.tabs[0]?.value || '')

// 当前 tab 对象
const currentTab = computed(() => {
  return props.tabs.find(tab => tab.value === activeTab.value)
})

// 可见的 tabs
const visibleTabs = computed(() => {
  return props.tabs.filter(tab => !tab.hidden)
})

// 获取 tab 触发器样式
const getTabTriggerClass = (tab: TabItem) => {
  const classes = []
  if (tab.disabled) classes.push('opacity-50 cursor-not-allowed')
  return classes.join(' ')
}

// Tab 切换处理
const handleTabChange = (value: string | number) => {
  const tab = props.tabs.find(t => t.value === value)
  if (!tab || tab.disabled) return

  activeTab.value = value
  emit('update:activeTab', value)
  emit('tab-change', tab, props.tabs.indexOf(tab))

  // 同步到 URL
  if (props.syncUrl) {
    updateUrl()
  }
}

// 更新 URL
const updateUrl = () => {
  const query = { ...route.query }
  query[props.urlParam] = String(activeTab.value)
  router.replace({ query })
}

// 从 URL 初始化
const initFromUrl = () => {
  if (props.syncUrl && route.query[props.urlParam]) {
    const urlTab = route.query[props.urlParam] as string
    const tab = props.tabs.find(t => String(t.value) === urlTab)
    if (tab && !tab.disabled) {
      activeTab.value = tab.value
    }
  }
}

// 监听 tabs 变化
watch(() => props.tabs, (newTabs) => {
  if (newTabs.length > 0 && !newTabs.find(t => t.value === activeTab.value)) {
    activeTab.value = newTabs[0].value
  }
}, { immediate: true })

// 监听路由变化
watch(() => route.query[props.urlParam], (newValue) => {
  if (props.syncUrl && newValue && String(newValue) !== String(activeTab.value)) {
    const tab = props.tabs.find(t => String(t.value) === String(newValue))
    if (tab && !tab.disabled) {
      activeTab.value = tab.value
    }
  }
})

onMounted(() => {
  initFromUrl()
})

// 暴露方法
defineExpose({
  activeTab: readonly(activeTab),
  currentTab,
  setActiveTab: (value: string | number) => handleTabChange(value)
})
</script>

<style scoped>
.tab-container {
  @apply w-full;
}

.tab-content {
  @apply w-full;
}
</style>
