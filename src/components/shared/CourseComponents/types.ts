// 课程组件通用类型定义

export interface CourseFilters {
  // Content 页面过滤器
  language?: string
  level?: string
  subtitle?: string
  source?: string
  duration?: string
  topicId?: string

  // My Center 页面过滤器
  status?: string
  type?: number
  courseType?: string
}

export interface CourseSearchConfig {
  placeholder?: string
  showClearButton?: boolean
  loading?: boolean
}

export interface CourseStatsConfig {
  showTotal?: boolean
  showBreadcrumb?: boolean
  showSort?: boolean
  breadcrumbItems?: BreadcrumbItem[]
}

export interface BreadcrumbItem {
  id: string
  code: string
  name: string
  icon?: string
  parentId?: string
  children?: BreadcrumbItem[]
}

export interface SortOption {
  value: string
  label: string
}

export interface CourseTableConfig {
  // 显示配置
  showProgress?: boolean
  showStatus?: boolean
  showRating?: boolean
  showBadges?: boolean
  showAuthor?: boolean
  showDate?: boolean
  showTags?: boolean

  // 布局配置
  gridCols?: string
  emptyTitle?: string
  emptyDescription?: string
  routeName?: string

  // 分页配置
  pageSize?: number
}

export interface CoursePageProps {
  // 搜索相关
  searchQuery?: string

  // 过滤器相关
  filters?: CourseFilters

  // 配置相关
  searchConfig?: CourseSearchConfig
  statsConfig?: CourseStatsConfig
  tableConfig?: CourseTableConfig

  // 排序相关
  sortBy?: string
  sortOptions?: SortOption[]
}
