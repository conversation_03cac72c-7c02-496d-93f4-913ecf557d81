<template>
  <div class="flex items-center gap-2">
    <Label class="text-xs text-muted-foreground whitespace-nowrap">{{ label }}:</Label>
    <div class="flex items-center gap-1">
      <Button
        v-for="option in options"
        :key="option.value"
        :variant="value === option.value ? 'default' : 'outline'"
        size="sm"
        class="h-7 px-2 text-xs min-w-[50px]"
        @click="$emit('change', option.value)"
      >
        {{ option.label }}
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'

interface FilterOption {
  value: string
  label: string
}

interface Props {
  label: string
  options: FilterOption[]
  value?: string
}

defineProps<Props>()
defineEmits<{
  change: [value: string]
}>()
</script>
