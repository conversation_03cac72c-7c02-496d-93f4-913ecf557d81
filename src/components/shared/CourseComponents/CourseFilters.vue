<template>
  <div class="flex flex-wrap items-center gap-x-6 gap-y-2">
    <!-- Content 页面过滤器 -->
    <template v-if="mode === 'content'">
      <!-- Language Filter -->
      <FilterGroup
        label="Language"
        :options="languageOptions"
        :value="filters.language"
        @change="handleFilterChange('language', $event)"
      />

      <!-- Level Filter -->
      <FilterGroup
        label="Level"
        :options="levelOptions"
        :value="filters.level"
        @change="handleFilterChange('level', $event)"
      />

      <!-- Subtitle Filter -->
      <FilterGroup
        label="Subtitle"
        :options="subtitleOptions"
        :value="filters.subtitle"
        @change="handleFilterChange('subtitle', $event)"
      />

      <!-- Source Filter -->
      <FilterGroup
        label="Source"
        :options="sourceOptions"
        :value="filters.source"
        @change="handleFilterChange('source', $event)"
      />

      <!-- Duration Filter -->
      <FilterGroup
        label="Duration"
        :options="durationOptions"
        :value="filters.duration"
        @change="handleFilterChange('duration', $event)"
      />
    </template>

    <!-- My Center 页面过滤器 -->
    <template v-else-if="mode === 'mycenter'">
      <!-- Status Filter -->
      <FilterGroup
        label="Status"
        :options="statusOptions"
        :value="filters.status"
        @change="handleFilterChange('status', $event)"
      />
    </template>

    <!-- 自定义过滤器插槽 -->
    <slot name="custom-filters" :filters="filters" :onChange="handleFilterChange" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import FilterGroup from './FilterGroup.vue'
import type { CourseFilters } from './types'

interface Props {
  filters: CourseFilters
  mode?: 'content' | 'mycenter' | 'custom'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'content'
})

const emit = defineEmits<{
  'update:filters': [filters: CourseFilters]
}>()

// 过滤器选项定义
const languageOptions = [
  { value: '0', label: 'All' },
  { value: '1', label: 'English' },
  { value: '2', label: 'Arabic' }
]

const levelOptions = [
  { value: '0', label: 'All' },
  { value: '1', label: 'Beginner' },
  { value: '2', label: 'Intermediate' },
  { value: '3', label: 'Advanced' }
]

const subtitleOptions = [
  { value: '', label: 'All' },
  { value: '0', label: 'Without' },
  { value: '1', label: 'With' }
]

const sourceOptions = [
  { value: '', label: 'All' },
  { value: '1', label: 'Local' },
  { value: '2', label: 'Cloud' }
]

const durationOptions = [
  { value: '', label: 'All' },
  { value: '1', label: '<15min' },
  { value: '2', label: '15-30min' },
  { value: '3', label: '30-60min' },
  { value: '4', label: '>60min' }
]

const statusOptions = [
  { value: '', label: 'All' },
  { value: 'not_started', label: 'Not Started' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' }
]

const handleFilterChange = (key: keyof CourseFilters, value: string) => {
  const newFilters = { ...props.filters, [key]: value }
  emit('update:filters', newFilters)
}
</script>
