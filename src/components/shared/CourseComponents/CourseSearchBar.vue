<template>
  <div class="flex items-center gap-4">
    <!-- 搜索框 -->
    <SuperSearch
      :model-value="searchQuery"
      :loading="config.loading"
      :placeholder="config.placeholder || 'Search courses...'"
      round
      class="flex-1 max-w-md"
      @update:model-value="handleSearchChange"
      @search="handleSearch"
      @keyup="handleSearch"
    />

    <!-- 清除按钮 -->
    <Button
      v-if="config.showClearButton && searchQuery"
      variant="ghost"
      size="sm"
      class="text-muted-foreground h-8"
      @click="handleClear"
    >
      <X class="size-3 mr-1" />
      Clear
    </Button>
  </div>
</template>

<script setup lang="ts">
import { X } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import { Button } from '@/components/ui/button'
import type { CourseSearchConfig } from './types'

interface Props {
  searchQuery?: string
  config?: CourseSearchConfig
}

const props = withDefaults(defineProps<Props>(), {
  searchQuery: '',
  config: () => ({
    placeholder: 'Search courses...',
    showClearButton: true,
    loading: false
  })
})

const emit = defineEmits<{
  'update:searchQuery': [value: string]
  search: [value: string]
  clear: []
}>()

const handleSearchChange = (value: string) => {
  emit('update:searchQuery', value)
}

const handleSearch = () => {
  emit('search', props.searchQuery)
}

const handleClear = () => {
  emit('update:searchQuery', '')
  emit('clear')
}
</script>
