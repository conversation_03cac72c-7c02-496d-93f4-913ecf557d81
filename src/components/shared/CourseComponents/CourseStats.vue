<template>
  <div class="flex items-center justify-between mb-4 px-4">
    <!-- 左侧：面包屑和统计 -->
    <div class="flex items-center gap-4">
      <!-- 面包屑导航 -->
      <nav
        v-if="config.showBreadcrumb && config.breadcrumbItems?.length"
        class="flex items-center gap-2"
      >
        <template v-for="(item, index) in config.breadcrumbItems" :key="item.id">
          <button
            v-if="index < config.breadcrumbItems.length - 1"
            class="text-sm text-muted-foreground hover:text-foreground transition-colors"
            @click="$emit('breadcrumb-click', item)"
          >
            {{ item.icon }} {{ item.name }}
          </button>
          <span v-else class="text-sm text-foreground font-medium">
            {{ item.icon }} {{ item.name }}
          </span>
          <ChevronRight
            v-if="index < config.breadcrumbItems.length - 1"
            class="size-4 text-muted-foreground"
          />
        </template>
      </nav>

      <!-- 默认显示 -->
      <span v-else-if="config.showBreadcrumb" class="text-sm text-foreground font-medium">
        📚 All Courses
      </span>

      <!-- 统计信息 -->
      <div v-if="config.showTotal" class="text-sm text-muted-foreground">
        {{ total }} courses found
      </div>
    </div>

    <!-- 右侧：排序选项 -->
    <div v-if="config.showSort && sortOptions?.length" class="flex items-center gap-2">
      <Label class="text-xs text-muted-foreground whitespace-nowrap">Sort by:</Label>
      <div class="flex items-center gap-1">
        <LinkItem
          v-for="option in sortOptions"
          :key="option.value"
          :item="{ name: option.label }"
          :active="sortBy === option.value"
          class="cursor-pointer text-xs h-6"
          @click="$emit('sort-change', option.value)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next'
import { Label } from '@/components/ui/label'
import { LinkItem } from '@/components/LinkNav'
import type { CourseStatsConfig, SortOption } from './types'

interface Props {
  total?: number
  sortBy?: string
  sortOptions?: SortOption[]
  config?: CourseStatsConfig
}

withDefaults(defineProps<Props>(), {
  total: 0,
  sortBy: '',
  config: () => ({
    showTotal: true,
    showBreadcrumb: false,
    showSort: true
  })
})

defineEmits<{
  'breadcrumb-click': [item: any]
  'sort-change': [value: string]
}>()
</script>
