<template>
  <div class="flex flex-col h-full">
    <!-- 课程网格 - 可滚动区域 -->
    <ScrollArea class="flex-1">
      <div class="p-4 min-h-full">
        <ContentGrid
          :items="transformedCourseList"
          :content-type="ContentType.COURSE"
          :loading="loading"
          :show-rating="config.showRating"
          :show-progress="config.showProgress"
          :show-status="config.showStatus"
          :show-badges="config.showBadges"
          :show-author="config.showAuthor"
          :show-date="config.showDate"
          :show-tags="config.showTags"
          :grid-cols="config.gridCols"
          :empty-title="config.emptyTitle"
          :empty-description="config.emptyDescription"
          :route-name="config.routeName"
          class="min-h-full"
          @item-click="$emit('course-click', $event)"
        />
      </div>
    </ScrollArea>

    <!-- 底部固定分页 -->
    <div
      class="flex-shrink-0 px-4 h-[52px] flex items-center justify-center border-t bg-background"
    >
      <SmartPagination
        v-if="total > 0"
        :total="total"
        :current-page="currentPage"
        :page-size="config.pageSize || 20"
        @current-change="$emit('page-change', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ScrollArea } from '@/components/ui/scroll-area'
import { ContentGrid } from '@/components'
import { SmartPagination } from '@/components/SmartPagination'
import { ContentType } from '@/enums/content'
import { transformCoursesToContent } from '@/utils/courseTransform'
import type { CourseTableConfig } from './types'
import defaultImg from '@/assets/course/content/readBookImg.png'

interface Props {
  courseList: any[]
  total?: number
  currentPage?: number
  loading?: boolean
  config?: CourseTableConfig
}

const props = withDefaults(defineProps<Props>(), {
  total: 0,
  currentPage: 1,
  loading: false,
  config: () => ({
    showProgress: false,
    showStatus: false,
    showRating: true,
    showBadges: true,
    showAuthor: false,
    showDate: false,
    showTags: false,
    gridCols: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    emptyTitle: 'No courses found',
    emptyDescription: 'Try adjusting your search or browse different subjects',
    routeName: 'ContentDetail',
    pageSize: 20
  })
})

defineEmits<{
  'course-click': [course: any]
  'page-change': [page: number]
}>()

// 转换课程数据为通用内容格式
const transformedCourseList = computed(() => {
  return transformCoursesToContent(props.courseList, defaultImg)
})
</script>
