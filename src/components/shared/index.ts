// 统一的共享组件导出
export { default as CourseCard } from './CourseCard/index.vue'
export { default as ListContainer } from './ListContainer/index.vue'
export { default as TabContainer } from './TabContainer/index.vue'

// 类型定义
export interface CourseData {
  id: string | number
  name: string
  cover: string
  isRecommend?: boolean
  isNew?: boolean
  studyStatus?: number
  star?: number
  progress?: number
  duration?: number
  level?: number
  language?: number
}

export interface TabItem {
  label: string
  value: string | number
  icon?: any
  count?: number
  component?: any
  props?: Record<string, any>
  content?: string
  disabled?: boolean
  hidden?: boolean
}

export interface QuickFilter {
  key: string
  label: string
  value: any
  field?: string
}

// 常用的查询参数接口
export interface BaseQueryParams {
  pageNo?: number
  pageNum?: number
  pageSize?: number
  name?: string
  search?: string
  status?: string | number
  type?: string | number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 课程相关的查询参数
export interface CourseQueryParams extends BaseQueryParams {
  level?: number
  language?: number
  category?: string
  isRecommend?: boolean
  isNew?: boolean
}

// 考试相关的查询参数
export interface ExamQueryParams extends BaseQueryParams {
  examType?: number
  difficulty?: number
  subject?: string
}

// 政策相关的查询参数
export interface PolicyQueryParams extends BaseQueryParams {
  policyType?: number
  department?: string
  effectiveDate?: string
}
