<template>
  <div class="cursor-pointer group">
    <!-- 课程图片容器 -->
    <div
      class="relative overflow-hidden rounded-md cursor-pointer transition-transform group-hover:scale-105"
      @click="handleClick"
    >
      <LazyImage :src="data.cover" :alt="data?.name" :aspect-ratio="aspectRatio" />

      <!-- 推荐标签 -->
      <div v-if="data.isRecommend === true" class="absolute -left-px top-[-6px]">
        <SvgIcon icon-class="Recommendcourse" class="text-6xl" />
      </div>

      <!-- 新课程标签 -->
      <div
        v-if="data.isNew === true"
        class="absolute top-[-6px]"
        :class="data.isRecommend ? 'left-[40px]' : '-left-px'"
      >
        <SvgIcon icon-class="Newcourse" class="text-6xl" />
      </div>

      <!-- 学习状态标签 (仅在 todo 模式下显示) -->
      <div
        v-if="mode === 'todo' && data.studyStatus"
        class="w-9 h-9 absolute top-0 right-6 z-10 flex justify-center items-center shadow-[0_4px_4px_0px_rgba(0,0,0,0.25)]"
        :class="getStatusStyle(data.studyStatus).bg"
      >
        <SvgIcon :icon-class="getStatusStyle(data.studyStatus).icon" class="z-10 text-xl" />
        <div class="z-10 triangle-down" :class="getStatusStyle(data.studyStatus).bg" />
      </div>
    </div>

    <!-- 课程信息 -->
    <div class="my-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger as-child>
            <h3
              class="text-lg font-bold text-black line-clamp-2 mb-1 group-hover:text-primary transition-colors"
            >
              {{ data.name }}
            </h3>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <div class="max-w-xs">
              {{ data.name }}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <!-- 评分 (仅在 content 模式下显示) -->
      <Rate v-if="mode === 'content' && data.star" :default-value="data.star" readonly :size="20" />

      <!-- 进度条 (仅在 progress 模式下显示) -->
      <div v-if="mode === 'progress' && data.progress !== undefined" class="mt-2">
        <div class="flex justify-between text-sm text-muted-foreground mb-1">
          <span>Progress</span>
          <span>{{ data.progress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            class="bg-primary h-2 rounded-full transition-all duration-300"
            :style="{ width: `${data.progress}%` }"
          ></div>
        </div>
      </div>

      <!-- 课程标签 -->
      <div
        v-if="showBadges && (mode === 'detailed' || mode === 'content')"
        class="flex flex-wrap gap-1 mt-2"
      >
        <!-- 课程类型 -->
        <Badge v-if="data.type !== undefined" variant="secondary" class="text-xs">
          {{ data.type === 1 ? 'Mandatory' : 'Elective' }}
        </Badge>

        <!-- 证书 -->
        <Badge
          v-if="showCertificate"
          variant="outline"
          class="text-xs text-green-600 border-green-600"
        >
          <SvgIcon icon-class="certificate" class="w-3 h-3 mr-1" />
          Certificate
        </Badge>

        <!-- 考试 -->
        <Badge v-if="showExam" variant="outline" class="text-xs text-blue-600 border-blue-600">
          <SvgIcon icon-class="exam" class="w-3 h-3 mr-1" />
          Exam
        </Badge>

        <!-- 课程来源 -->
        <Badge v-if="data.source" variant="outline" class="text-xs">
          {{ getCourseSourceLabel(data.source) }}
        </Badge>
      </div>

      <!-- 课程统计信息 -->
      <div v-if="showStats && mode === 'detailed'" class="mt-2 space-y-1">
        <div v-if="data.enrollNumber" class="flex items-center gap-1 text-xs text-muted-foreground">
          <SvgIcon icon-class="users" class="w-3 h-3" />
          <span>{{ data.enrollNumber }} enrolled</span>
        </div>

        <div v-if="data.chapterNum" class="flex items-center gap-1 text-xs text-muted-foreground">
          <SvgIcon icon-class="chapters" class="w-3 h-3" />
          <span>{{ data.chapterNum }} chapters</span>
        </div>

        <div v-if="data.effectiveDay" class="flex items-center gap-1 text-xs text-muted-foreground">
          <SvgIcon icon-class="clock" class="w-3 h-3" />
          <span>{{ getCourseEffectiveLabel(data.effectiveDay) }}</span>
        </div>
      </div>

      <!-- 学习状态 (仅在 progress 模式下显示) -->
      <div v-if="mode === 'progress' && data.studyStatus !== undefined" class="mt-2">
        <div class="flex items-center justify-between text-sm">
          <span class="text-muted-foreground">Status</span>
          <Badge :variant="data.studyStatus === 3 ? 'default' : 'secondary'" class="text-xs">
            {{ getStudyStatusLabel(data.studyStatus) }}
          </Badge>
        </div>
      </div>

      <!-- 额外信息 -->
      <div v-if="showMeta" class="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
        <span v-if="data.duration">{{ formatCourseDuration(data.duration) }}</span>
        <span v-if="data.level !== undefined">{{ getCourseLevelLabel(data.level) }}</span>
        <span v-if="data.language">{{ getLanguageLabel(data.language) }}</span>
      </div>

      <!-- 课程描述 (仅在 detailed 模式下显示) -->
      <div v-if="mode === 'detailed' && data.introduction" class="mt-2">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger as-child>
              <p class="text-sm text-muted-foreground line-clamp-2">
                {{ data.introduction }}
              </p>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <div class="max-w-sm">
                {{ data.introduction }}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ChapterStatus } from '@/enums/chapter'
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import LazyImage from '@/components/LazyImage.vue'
import { Rate } from '@/components/Rate'
import { SvgIcon } from '@/components/SvgIcon'
import {
  CourseCardData,
  StudyStatusEnum,
  formatCourseDuration,
  getCourseLevelLabel,
  getLanguageLabel,
  getCourseSourceLabel,
  getStudyStatusLabel,
  getCourseEffectiveLabel,
  hasCertificate,
  hasExam
} from '@/api/learning/course'

interface Props {
  data: CourseCardData
  mode?: 'content' | 'todo' | 'progress' | 'simple' | 'detailed'
  aspectRatio?: 'square' | '16/9' | '4/3'
  showMeta?: boolean
  showBadges?: boolean
  showStats?: boolean
  routeName?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'content',
  aspectRatio: 'square',
  showMeta: false,
  showBadges: true,
  showStats: false,
  routeName: 'ContentDetail'
})

const router = useRouter()

// 学习状态样式映射
const statusStyles = {
  [ChapterStatus.NotStart]: {
    bg: 'bg-primary',
    icon: 'TodoNotStart'
  },
  [ChapterStatus.InProgress]: {
    bg: 'bg-[#F1A003]',
    icon: 'TodoInProgress'
  },
  [StudyStatusEnum.COMPLETED]: {
    bg: 'bg-green-500',
    icon: 'TodoCompleted'
  }
}

// 获取状态样式
const getStatusStyle = (status: number) => {
  return statusStyles[status] || statusStyles[ChapterStatus.NotStart]
}

// 点击处理
const handleClick = () => {
  router.push({ name: props.routeName, params: { id: props.data.id } })
}

// 计算是否显示证书标识
const showCertificate = computed(() => {
  return hasCertificate(props.data)
})

// 计算是否显示考试标识
const showExam = computed(() => {
  return hasExam(props.data)
})
</script>

<style scoped>
.triangle-down {
  width: 0;
  height: 0;
  border-left: 18px solid transparent;
  border-right: 18px solid transparent;
  border-top: 10px solid;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}
</style>
