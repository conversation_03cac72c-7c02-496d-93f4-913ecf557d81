<template>
  <Card
    :class="
      cn(
        'container-wrap w-full mx-auto',
        border ? '' : 'border-0',
        shadow ? '' : 'shadow-none',
        props.class
      )
    "
    v-loading="loading"
  >
    <div class="flex justify-between items-center">
      <CardHeader v-if="title || description">
        <CardTitle v-if="title">{{ title }}</CardTitle>
        <CardDescription v-if="description">{{ description }}</CardDescription>
      </CardHeader>
      <div v-if="useSlot['header-right']" class="flex items-center space-x-2 px-6">
        <slot name="header-right" />
      </div>
    </div>
    <Separator v-if="title || description" class="mb-4" />
    <CardContent :class="{'mt-6': !(title || description)}">
      <div v-if="useSlot.action" class="pb-4">
        <slot name="action" />
      </div>
      <slot />
    </CardContent>
  </Card>
</template>

<script lang="ts" setup>
import { cn } from '@/lib/utils'
import { useSlots } from 'vue'

const useSlot = useSlots()

defineOptions({ name: 'ContainerWrap' })
const props = withDefaults(
  defineProps<{
    loading?: boolean
    border?: boolean
    shadow?: boolean
    title?: string
    description?: string
    class?: string
  }>(),
  {
    loading: false,
    border: false,
    shadow: false,
    title: '',
    description: ''
  }
)
</script>
