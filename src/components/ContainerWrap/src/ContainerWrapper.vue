<script lang="ts" setup>
import { cn } from '@/lib/utils'
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSlots } from 'vue'

defineOptions({
  name: 'ContainerWrapper'
})

interface ListWrapProps {
  defaultCollapsed?: boolean
  navWidth?: number
  detailWidth?: number
  border?: boolean
  rounded?: boolean
  shadow?: boolean
  autoCollapse?: boolean
  divider?: boolean | 'subtle' | 'none'
}

const useSlot = useSlots()

const props = withDefaults(defineProps<ListWrapProps>(), {
  defaultCollapsed: false,
  navWidth: 280,
  detailWidth: 320,
  border: true,
  rounded: true,
  shadow: true,
  autoCollapse: true,
  divider: 'subtle'
})

// Responsive state management
const screenWidth = ref(window?.innerWidth || 1024)
const isManuallyCollapsed = ref(props.defaultCollapsed)

// Computed responsive behavior
const isCollapsed = computed(() => {
  if (!props.autoCollapse) return isManuallyCollapsed.value

  // Auto-collapse based on screen size
  if (screenWidth.value < 768) return true // Always collapsed on mobile
  if (screenWidth.value < 1024) return isManuallyCollapsed.value // Manual control on tablet
  return false // Always expanded on desktop
})

const showNavPanel = computed(() => {
  return useSlot.nav && (screenWidth.value >= 768 || !isCollapsed.value)
})

// Responsive width calculation
const responsiveNavWidth = computed(() => {
  if (screenWidth.value < 1024) return Math.min(props.navWidth, 240) // Smaller on tablets
  if (screenWidth.value < 1440) return Math.min(props.navWidth, 260) // Medium on small desktops
  return props.navWidth // Full width on large screens
})

const responsiveDetailWidth = computed(() => {
  if (screenWidth.value < 1024) return Math.min(props.detailWidth, 280) // Smaller on tablets
  if (screenWidth.value < 1440) return Math.min(props.detailWidth, 300) // Medium on small desktops
  return props.detailWidth // Full width on large screens
})

const navWidthClass = computed(() => {
  if (!showNavPanel.value) return 'w-0'
  return isCollapsed.value ? 'w-16' : `w-[${responsiveNavWidth.value}px]`
})

const detailWidthClass = computed(() => {
  if (!useSlot.detail) return 'w-0'
  return `w-[${responsiveDetailWidth.value}px]`
})

// Divider styles
const navDividerClass = computed(() => {
  if (props.divider === 'none') return ''
  if (props.divider === 'subtle') return 'border-r border-border/50'
  return 'border-r border-border'
})

const detailDividerClass = computed(() => {
  if (props.divider === 'none') return ''
  if (props.divider === 'subtle') return 'border-l border-border/50'
  return 'border-l border-border'
})

// Window resize handler
const handleResize = () => {
  screenWidth.value = window.innerWidth
}

// Manual toggle functions
function toggleCollapse() {
  isManuallyCollapsed.value = !isManuallyCollapsed.value
}

function onCollapse() {
  isManuallyCollapsed.value = true
}

function onExpand() {
  isManuallyCollapsed.value = false
}

// Lifecycle
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div
    class="h-full bg-white overflow-hidden flex"
    :class="{
      border,
      'rounded-lg': rounded,
      shadow
    }"
  >
    <!-- Left Navigation Panel -->
    <div
      v-if="showNavPanel"
      :class="[
        'flex-shrink-0 transition-all duration-300 ease-in-out overflow-hidden',
        navWidthClass,
        navDividerClass,
        'hidden md:block',
        {
          'md:hidden': screenWidth < 1024 && isCollapsed,
          'md:block': screenWidth >= 1024 || !isCollapsed
        }
      ]"
    >
      <div
        class="h-full flex flex-col"
        :class="isCollapsed ? 'w-16' : `w-[${responsiveNavWidth}px]`"
      >
        <slot
          name="nav"
          :is-collapsed="isCollapsed"
          :toggle-collapse="toggleCollapse"
          :on-expand="onExpand"
          :on-collapse="onCollapse"
        />
      </div>
    </div>

    <!-- Mobile Navigation Overlay -->
    <div
      v-if="useSlot.nav && screenWidth < 768 && !isCollapsed"
      class="fixed inset-0 z-50 md:hidden"
    >
      <!-- Backdrop -->
      <div class="absolute inset-0 bg-black/50 transition-opacity" @click="onCollapse"></div>

      <!-- Mobile Nav Panel -->
      <div
        class="absolute left-0 top-0 h-full bg-white shadow-xl transition-transform"
        :class="`w-[${responsiveNavWidth}px]`"
      >
        <slot
          name="nav"
          :is-collapsed="false"
          :toggle-collapse="toggleCollapse"
          :on-expand="onExpand"
          :on-collapse="onCollapse"
        />
      </div>
    </div>

    <!-- Main Content Area -->
    <div v-if="useSlot.content" class="flex-1 min-w-0 overflow-hidden">
      <slot name="content" />
    </div>

    <!-- Right Detail Panel -->
    <div
      v-if="useSlot.detail"
      :class="[
        'flex-shrink-0 transition-all duration-300 ease-in-out overflow-hidden',
        detailWidthClass,
        detailDividerClass,
        'hidden lg:block'
      ]"
    >
      <div class="h-full flex flex-col" :class="`w-[${responsiveDetailWidth}px]`">
        <slot name="detail" />
      </div>
    </div>
  </div>
</template>
