<script lang="ts" setup>
import { cn } from '@/lib/utils'
import { LinkProp } from '@/components/ContainerWrap'
import { MessageMyBizTypeRespVO, BizTypeEnum } from '@/api/message'

interface NavProps {
  isCollapsed: boolean
  links: any[] // 支持不同的数据结构
  activeItem?: any // 当前激活的菜单项
}

interface BizTypeItem {
  title: string
  type: number
}

const bizTypes = ref<BizTypeItem[]>([
  {
    title: 'Certificate Notification',
    type: BizTypeEnum.CERTIFICATE_USER
  },
  {
    title: ' Waiting List Notification',
    type: BizTypeEnum.WAITING_LIST_PREFER_DATE
  },
  {
    title: 'Class End Notification',
    type: BizTypeEnum.CLASS_END
  },
  {
    title: 'Class Cancel Notification',
    type: BizTypeEnum.CLASS_CANCEL
  },
  {
    title: 'Class Postpone Notification',
    type: BizTypeEnum.CLASS_POSTPONE
  },
  {
    title: 'Class Publish Notification',
    type: BizTypeEnum.CLASS_PUBLISH
  },
  {
    title: 'Task Approved Notification',
    type: BizTypeEnum.LEANING_TASK_APPROVED
  },
  {
    title: 'Task Rejected Notification',
    type: BizTypeEnum.LEANING_TASK_REJECTED
  }
])
const selectedBizType = defineModel<string>('selectedBizType', { required: false })

const props = defineProps<NavProps>()
const route = useRoute()

const emit = defineEmits(['change-nav', 'changeNav'])

const selectedNav = ref<LinkProp | null>(null)

// 辅助函数：获取按钮变体
const getButtonVariant = (link: any) => {
  // My Center 页面使用 variant 属性
  if (link.variant) {
    return link.variant
  }
  // Message 页面使用 bizType 和 selectedBizType
  if (link.bizType !== undefined) {
    return selectedBizType.value === link.bizType ? 'default' : 'ghost'
  }
  // Live 页面：检查是否是当前激活的菜单项
  if (link.type !== undefined) {
    // 通过 activeItem prop 检查
    if (props.activeItem && props.activeItem.type === link.type) {
      return 'default'
    }
    // 通过路由参数检查
    const routeType = route.query.type as string
    if (routeType === link.type) {
      return 'default'
    }
    // 如果没有路由参数且是第一个菜单项，则激活
    if (!routeType && link.type === 'stream-central') {
      return 'default'
    }
  }
  return 'ghost'
}

// 辅助函数：获取按钮标题
const getButtonTitle = (link: any) => {
  // My Center 页面直接使用 title 属性
  if (link.title) {
    return link.title
  }
  // Live 页面使用 name 属性
  if (link.name) {
    return link.name
  }
  // Message 页面从 bizTypes 中查找
  if (link.bizType !== undefined) {
    return bizTypes.value.find((bizType) => bizType.type === link.bizType)?.title || ''
  }
  return ''
}

// 处理导航变化
const handleChangeNav = (linkOrBizType: any) => {
  // 如果是对象（My Center 页面），发送 key
  if (typeof linkOrBizType === 'object' && linkOrBizType.key) {
    emit('changeNav', { key: linkOrBizType.key })
  }
  // 如果是 bizType（Message 页面），发送 bizType
  else if (typeof linkOrBizType === 'object' && linkOrBizType.bizType) {
    selectedBizType.value = linkOrBizType.bizType
    emit('change-nav', selectedBizType.value)
  }
}
</script>

<template>
  <div
    :data-collapsed="isCollapsed"
    class="group flex flex-col gap-4 py-2 data-[collapsed=true]:py-2"
  >
    <nav
      class="grid gap-1 px-2 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2"
    >
      <template v-for="(link, index) of links">
        <Tooltip v-if="isCollapsed" :key="`1-${index}`" :delay-duration="0">
          <TooltipTrigger as-child>
            <Button
              size="icon"
              :variant="getButtonVariant(link)"
              :class="cn('h-9 w-9')"
              @click="handleChangeNav(link)"
            >
              <component v-if="link.icon" :is="link.icon" class="size-4" />
              <span class="sr-only">{{ getButtonTitle(link) }}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right" class="flex items-center gap-4">
            {{ getButtonTitle(link) }}
            <span v-if="link.unreadCount || link.count" class="ml-auto text-muted-foreground">
              {{ link.unreadCount || link.count }}
            </span>
          </TooltipContent>
        </Tooltip>

        <Button
          v-else
          :key="`2-${index}`"
          size="sm"
          :variant="getButtonVariant(link)"
          :class="cn('justify-start')"
          @click="handleChangeNav(link)"
        >
          <component v-if="link.icon" :is="link.icon" class="mr-2 size-4" />
          {{ getButtonTitle(link) }}
          <span v-if="link.unreadCount || link.count" :class="cn('ml-auto')">
            {{ link.unreadCount || link.count }}
          </span>
        </Button>
      </template>
    </nav>
  </div>
</template>
