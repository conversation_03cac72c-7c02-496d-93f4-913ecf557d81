<template>
  <div class="h-full flex flex-col">
    <div v-if="useSlot.header" class="w-full h-full" :style="{ height: `${headerHeight}px` }">
      <slot name="header" />
    </div>
    <Separator />
    <!-- Expanded Filters -->
    <slot name="filter" />
    <div v-if="useSlot.statistics">
      <slot name="statistics" />
    </div>
    <ScrollArea class="flex-1 flex flex-col">
      <slot />
    </ScrollArea>
    <Separator v-if="useSlot.footer" />
    <div v-if="useSlot.footer" :style="{ height: `${footerHeight}px` }">
      <slot name="footer" />
    </div>
  </div>
</template>
<script setup lang="ts">
const useSlot = useSlots()

withDefaults(
  defineProps<{
    headerHeight?: number
    footerHeight?: number
  }>(),
  {
    headerHeight: 52,
    footerHeight: 52
  }
)
</script>
