<template>
  <div class="relative">
    <Select v-model="selectedValue" @update:model-value="handleChange">
      <SelectTrigger class="w-[200px]">
        <SelectValue :placeholder="placeholder">
          {{ selectedSubjectName }}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <!-- Root: All Subjects -->
        <SelectItem value="all" class="font-medium">All Subjects</SelectItem>

        <!-- Level 1: Main Subjects -->
        <template v-for="subject in subjects" :key="subject.id">
          <SelectItem :value="subject.id.toString()" class="pl-4">
            {{ subject.name }}
          </SelectItem>

          <!-- Level 2: Sub Subjects -->
          <template v-if="subject.children && subject.children.length > 0">
            <SelectItem
              v-for="child in subject.children"
              :key="child.id"
              :value="child.id.toString()"
              class="pl-8 text-sm"
            >
              {{ child.name }}
            </SelectItem>

            <!-- Level 3: Sub-Sub Subjects -->
            <template v-for="child in subject.children" :key="`${child.id}-children`">
              <SelectItem
                v-for="grandchild in child.children || []"
                :key="grandchild.id"
                :value="grandchild.id.toString()"
                class="pl-12 text-xs text-muted-foreground"
              >
                {{ grandchild.name }}
              </SelectItem>
            </template>
          </template>
        </template>
      </SelectContent>
    </Select>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CourseApi, type SubjectVO } from '@/api/learning/course'

interface Props {
  modelValue?: string | number
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 'all',
  placeholder: 'Select Subject'
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  'change': [value: string | number]
}>()

// State
const subjects = ref<SubjectVO[]>([])
const loading = ref(false)

// Computed
const selectedValue = computed({
  get: () => props.modelValue?.toString() || 'all',
  set: (value: string) => {
    const numValue = value === 'all' ? 'all' : parseInt(value)
    emit('update:modelValue', numValue)
    emit('change', numValue)
  }
})

// Get selected subject name for display
const selectedSubjectName = computed(() => {
  if (selectedValue.value === 'all') {
    return 'All Subjects'
  }

  // Find the subject name by ID (including nested subjects)
  const findSubjectById = (subjects: SubjectVO[], id: string): string | null => {
    for (const subject of subjects) {
      if (subject.id.toString() === id) {
        return subject.name
      }
      if (subject.children && subject.children.length > 0) {
        const found = findSubjectById(subject.children, id)
        if (found) return found
      }
    }
    return null
  }

  const name = findSubjectById(subjects.value, selectedValue.value)
  return name || `Subject ${selectedValue.value}`
})

// Methods
const fetchSubjects = async () => {
  loading.value = true
  try {
    // For now, use the existing topic API until the new endpoint is available
    const response = await CourseApi.getAllTopics()
    // Transform the data to match SubjectVO structure
    subjects.value = transformTopicsToSubjects(response || [])
  } catch (error) {
    console.error('Failed to fetch subjects:', error)
    subjects.value = []
  } finally {
    loading.value = false
  }
}

// Transform CourseTopicVO to SubjectVO and build hierarchy
const transformTopicsToSubjects = (topics: any[]): SubjectVO[] => {
  const topicMap = new Map()
  const rootSubjects: SubjectVO[] = []

  // First pass: create all subjects
  topics.forEach(topic => {
    const subject: SubjectVO = {
      id: parseInt(topic.id),
      parentId: topic.parentId,
      name: topic.name,
      sort: topic.sort,
      cover: topic.cover || '',
      children: []
    }
    topicMap.set(subject.id, subject)
  })

  // Second pass: build hierarchy
  topics.forEach(topic => {
    const subject = topicMap.get(parseInt(topic.id))
    if (subject) {
      if (subject.parentId === 0) {
        rootSubjects.push(subject)
      } else {
        const parent = topicMap.get(subject.parentId)
        if (parent) {
          if (!parent.children) parent.children = []
          parent.children.push(subject)
        }
      }
    }
  })

  return rootSubjects
}

const handleChange = (value: string) => {
  selectedValue.value = value
}

// Lifecycle
onMounted(() => {
  fetchSubjects()
})
</script>
