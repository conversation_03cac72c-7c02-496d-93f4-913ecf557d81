<script setup lang="ts">
import { TreeItem, TreeRoot } from 'reka-ui'
import { CourseApi, CourseTopicVO } from '@/api/course/content/index'
import { handleTree } from '@/utils/tool'
import { cn } from '@/lib/utils'
interface TreeNode extends CourseTopicVO {
  children?: TreeNode[]
}

const items = ref<TreeNode[]>([])

const open = ref(false)
const selected = ref<TreeNode | null>(null)
const defaultExpanded = ref<string[]>([])
const props = defineProps<{
  hasNoSubject?: boolean
  modelValue: string
  selecteTreeStyle?: string
}>()
const emit = defineEmits(['update:modelValue'])

const selectedName = computed(() => selected.value?.name || 'Please Select Subject')
const selectedId = computed(() => selected.value?.id || '')
function findTitleById(tree, targetId, path = []) {
  for (const node of tree) {
    const currentPath = [...path, node]
    if (node.id === targetId) return currentPath
    if (node.children) {
      const result = findTitleById(node.children, targetId, currentPath)
      if (result) return result
    }
  }
  return null
}
// 监听 selectedId，当 selectedId 发生变化时，将其更新到父组件
watch(
  () => selectedId.value,
  (newId) => {
    console.log('selectedId changed:', newId)
    emit('update:modelValue', newId)
  }
)
watch(
  () => props.modelValue,
  (newVal) => {
    if (!newVal) {
      selected.value = null
    } else {
      const path = findTitleById(items.value, newVal)
      if (path) {
        selected.value = path[path.length - 1]
        defaultExpanded.value = path.slice(0, -1).map((node) => node.id)
      }
    }
  }
)

function handleSelect(item) {
  selected.value = item
  const path = findTitleById(items.value, item.id)
  if (path) {
    // 提取祖先节点 key（包括当前选中项的父们）
    defaultExpanded.value = path.slice(0, -1).map((node) => node.id)
  }
  open.value = false
}
const getList = () => {
  CourseApi.listTopic().then((res) => {
    items.value = handleTree(res, 'id')
    if (props.hasNoSubject) {
      items.value.unshift({
        id: '',
        name: 'No Subject'
      })
    }
  })
}
onMounted(() => {
  getList()
})
watch(open, (val) => {
  if (val && selected.value?.id) {
    const path = findTitleById(items.value, selected.value.id)
    if (path) {
      defaultExpanded.value = path.slice(0, -1).map((node) => node.id)
    }
  }
})
</script>

<template>
  <Popover v-model:open="open">
    <PopoverTrigger as-child>
      <Button
        :class="cn('w-[320px] flex justify-between h-[40px]', !selected && 'text-muted-foreground')"
        role="combobox"
        variant="outline"
        :aria-expanded="open"
      >
        <span class="truncate">{{ selectedName }}</span>
        <Icon name="ChevronDown" class="text-gray-500 opacity-50 shrink-0" />
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-[320px] p-0 shadow-sm border rounded bg-white">
      <TreeRoot
        v-slot="{ flattenItems }"
        :items="items"
        :get-key="(item) => item.id"
        :default-expanded="defaultExpanded"
        class="text-sm font-medium"
      >
        <ScrollArea class="h-[300px]">
          <TreeItem
            v-for="item in flattenItems"
            v-slot="{ isExpanded, handleToggle }"
            :key="item._id"
            v-bind="item.bind"
            :style="{ paddingLeft: `${item.level - 0.5}rem` }"
            class="flex items-center px-2 py-1.5 rounded cursor-pointer hover:bg-gray-100 hover:text-primary"
            :class="{ 'bg-blue-100 text-primary': selected?.id === item.value.id }"
            @click="handleSelect(item.value)"
          >
            <template v-if="item.hasChildren">
              <Icon
                name="ChevronRight"
                :size="12"
                class="transition-transform duration-200"
                :class="{ 'rotate-90': isExpanded }"
                @click.stop="handleToggle"
              />
            </template>
            <div class="truncate ps-2">{{ item.value.name }}</div>
          </TreeItem>
        </ScrollArea>
      </TreeRoot>
    </PopoverContent>
  </Popover>
</template>
