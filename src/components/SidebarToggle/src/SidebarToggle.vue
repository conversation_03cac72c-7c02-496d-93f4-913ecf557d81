<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { useAppStoreWithOut } from '@/store/modules/app'

const appStore = useAppStoreWithOut()

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <Button variant="ghost" :class="cn('h-7 w-7', props.class)" @click="appStore.toggleMaximize">
    <Icon name="Maximize" v-if="!appStore.getMaximize" />
    <Icon name="PanelLeftOpen" v-else />
  </Button>
</template>
