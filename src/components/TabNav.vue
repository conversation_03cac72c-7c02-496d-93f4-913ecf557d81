<template>
  <div class="flex items-center gap-6">
    <button
      v-for="tab in tabs"
      :key="tab.key"
      @click="handleTabClick(tab.key)"
      :class="
        cn(
          'relative px-3 py-2 text-sm font-medium transition-colors cursor-pointer group',
          activeTab === tab.key
            ? 'text-primary'
            : 'text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200'
        )
      "
    >
      {{ tab.label }}
      <!-- 激活状态下划线 -->
      <span
        v-if="activeTab === tab.key"
        class="absolute bottom-0 left-1/2 transform -translate-x-1/2 h-1 bg-primary rounded-full transition-all duration-200"
        :style="{ width: 'calc(80% - 8px)' }"
      ></span>
      <!-- hover状态下划线 -->
      <span
        v-else
        class="absolute bottom-0 left-1/2 transform -translate-x-1/2 h-1 bg-slate-300 dark:bg-slate-600 rounded-full transition-all duration-200 opacity-0 group-hover:opacity-100"
        :style="{ width: 'calc(80% - 8px)' }"
      ></span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'

interface Tab {
  key: string
  label: string
}

interface Props {
  tabs: Tab[]
  activeTab: string
}

interface Emits {
  (e: 'update:activeTab', value: string): void
  (e: 'tab-change', value: string): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

function handleTabClick(key: string) {
  emit('update:activeTab', key)
  emit('tab-change', key)
}
</script>
