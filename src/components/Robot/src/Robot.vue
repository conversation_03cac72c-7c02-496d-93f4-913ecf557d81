<script setup lang="ts">
import { cn } from '@/lib/utils'
import { Send } from 'lucide-vue-next'
import { computed, ref } from 'vue'

interface Props {
  open: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:open1']) // 新增 emit 定义

const input = ref('')
const inputLength = computed(() => input.value.trim().length)
const users = ref([
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/01.png',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/03.png',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/05.png',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/02.png',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatars/04.png',
  },
])

type User = (typeof users.value)[number]

const messages = ref([
  { role: 'agent', content: 'Hi, how can I help you today?' },
  { role: 'user', content: 'Hey, I\'m having trouble with my account.' },
  { role: 'agent', content: 'What seems to be the problem?' },
  { role: 'user', content: 'I can\'t log in.' },
])

// const open = ref(false)
const selectedUsers = ref<User[]>([])

const isOpen = ref(false)

// 修复监听逻辑
watch(() => props.open, (newVal) => {
  isOpen.value = newVal;
});

// 监听 isOpen 变化并同步到父组件
watch(isOpen, (newVal) => {
  emit('update:open', newVal);
});
</script>

<template>
  <Sheet v-model:open="isOpen">
    <SheetContent class="flex flex-col">
      <SheetHeader>
        <SheetTitle>Learning Assistant</SheetTitle>
        <SheetDescription>
          Helps students overcome language and tool barriers.
        </SheetDescription>
      </SheetHeader>
      <Separator/>
      <Card class="grow flex flex-col border-0" >
        <CardHeader class="flex flex-row items-center justify-between px-0">
          <div class="flex items-center space-x-4">
            <Avatar>
              <AvatarImage src="/avatars/01.png" alt="Image" />
              <AvatarFallback>OM</AvatarFallback>
            </Avatar>
            <div>
              <p class="text-sm font-medium leading-none"> Sofia Davis </p>
              <p class="text-sm text-muted-foreground"> <EMAIL> </p>
            </div>
          </div>
<!--          <TooltipProvider>-->
<!--            <Tooltip :delay-duration="200">-->
<!--              <TooltipTrigger as-child>-->
<!--                <Button-->
<!--                  variant="outline"-->
<!--                  class="rounded-full p-2.5 flex items-center justify-center"-->
<!--                  @click="open = true"-->
<!--                >-->
<!--                  <Plus class="w-4 h-4" />-->
<!--                </Button>-->
<!--              </TooltipTrigger>-->
<!--              <TooltipContent :side-offset="10"> New message </TooltipContent>-->
<!--            </Tooltip>-->
<!--          </TooltipProvider>-->
        </CardHeader>
        <CardContent class="grow px-0">
          <div class="space-y-4">
            <div
              v-for="(message, index) in messages"
              :key="index"
              :class="
                cn(
                  'flex w-max max-w-[75%] flex-col gap-2 rounded-lg px-3 py-2 text-sm',
                  message.role === 'user'
                    ? 'ml-auto bg-primary text-primary-foreground'
                    : 'bg-muted'
                )
              "
            >
              {{ message.content }}
            </div>
          </div>
        </CardContent>
        <CardFooter class="px-0">
          <form
            class="flex w-full items-center space-x-2"
            @submit.prevent="
              () => {
                if (inputLength === 0) return
                messages.push({
                  role: 'user',
                  content: input
                })
                input = ''
              }
            "
          >
            <Input v-model="input" placeholder="Type a message..." class="flex-1" />
            <Button class="p-2.5 flex items-center justify-center" :disabled="inputLength === 0">
              <Send class="w-4 h-4" />
              <span class="sr-only">Send</span>
            </Button>
          </form>
        </CardFooter>
      </Card>
<!--      <SheetFooter>-->
<!--        <SheetClose as-child>-->
<!--          <Button type="submit"> Save changes </Button>-->
<!--        </SheetClose>-->
<!--      </SheetFooter>-->
    </SheetContent>
  </Sheet>
</template>
