import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Home } from 'lucide-vue-next'
import type { BreadcrumbItem, BreadcrumbConfig } from './types'

const defaultConfig: BreadcrumbConfig = {
  homePath: '/home',
  homeTitle: 'Home',
  excludeRoutes: ['Layout', 'RedirectLayout', 'ParentLayout']
}

export function useBreadcrumb(config: Partial<BreadcrumbConfig> = {}) {
  const route = useRoute()
  const router = useRouter()
  const finalConfig = { ...defaultConfig, ...config }

  // 当前页面自定义标题
  const currentTitle = ref<string>('')

  /**
   * 从路由自动生成面包屑
   */
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = []

    // 添加 Home
    breadcrumbs.push({
      title: finalConfig.homeTitle,
      path: finalConfig.homePath,
      icon: Home
    })

    // 过滤有效的路由记录
    const matched = route.matched.filter(record => {
      return (
        record.meta?.breadcrumb !== false &&
        !finalConfig.excludeRoutes.includes(record.name as string) &&
        record.meta?.title &&
        record.path !== '/'
      )
    })

    matched.forEach((record, index) => {
      const isLast = index === matched.length - 1
      const title = isLast && currentTitle.value
        ? currentTitle.value
        : (record.meta.title as string)

      breadcrumbs.push({
        title,
        path: isLast ? undefined : record.path,
        current: isLast,
        disabled: isLast
      })
    })

    return breadcrumbs
  }

  /**
   * 响应式面包屑数据
   */
  const breadcrumbs = computed(() => generateBreadcrumbs())

  /**
   * 处理面包屑点击
   */
  const handleBreadcrumbClick = (item: BreadcrumbItem) => {
    if (item.path && !item.disabled) {
      router.push(item.path)
    }
  }

  /**
   * 更新当前页面标题
   */
  const updateCurrentTitle = (title: string) => {
    currentTitle.value = title
  }

  /**
   * 获取父级路由路径
   */
  const getParentPath = (): string | undefined => {
    const matched = route.matched.filter(record =>
      !finalConfig.excludeRoutes.includes(record.name as string)
    )

    if (matched.length > 1) {
      return matched[matched.length - 2].path
    }

    return finalConfig.homePath
  }

  /**
   * 返回上一级
   */
  const goBack = () => {
    const parentPath = getParentPath()
    if (parentPath) {
      router.push(parentPath)
    }
  }

  // 监听路由变化，清空自定义标题
  watch(() => route.fullPath, () => {
    currentTitle.value = ''
  })

  return {
    breadcrumbs,
    currentTitle: computed(() => currentTitle.value),
    handleBreadcrumbClick,
    updateCurrentTitle,
    goBack,
    getParentPath
  }
}
