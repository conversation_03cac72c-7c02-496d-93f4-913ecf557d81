import type { Component } from 'vue'

export interface BreadcrumbItem {
  /** 显示标题 */
  title: string
  /** 路由路径，如果为空则不可点击 */
  path?: string
  /** 图标组件 */
  icon?: Component
  /** 是否禁用点击 */
  disabled?: boolean
  /** 是否为当前页面 */
  current?: boolean
}

export interface SuperBreadcrumbProps {
  /** 自定义面包屑项，会覆盖自动生成的 */
  items?: BreadcrumbItem[]
  /** 是否显示 Home 链接 */
  showHome?: boolean
  /** 最大显示项数，超出显示省略号 */
  maxItems?: number
  /** 自定义当前页面标题 */
  currentTitle?: string
  /** 是否自动从路由生成 */
  autoGenerate?: boolean
  /** 是否显示返回按钮 */
  showBackButton?: boolean
}

export interface BreadcrumbConfig {
  /** Home 页面路径 */
  homePath: string
  /** Home 页面标题 */
  homeTitle: string
  /** 需要过滤的路由名称 */
  excludeRoutes: string[]
}
