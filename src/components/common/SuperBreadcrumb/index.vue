<template>
  <div class="flex items-center py-2">
    <!-- 返回按钮 -->
    <Button
      v-if="showBackButton && finalBreadcrumbs.length > 1"
      variant="ghost"
      size="icon"
      class="shrink-0 w-8 h-8 mr-2"
      @click="goBack"
    >
      <ArrowLeft class="w-4 h-4" />
    </Button>

    <!-- 面包屑导航 -->
    <Breadcrumb>
      <BreadcrumbList>
        <template v-for="(item, index) in displayBreadcrumbs" :key="`${item.path || item.title}-${index}`">
          <!-- 省略号处理 -->
          <template v-if="item.title === '...'">
            <BreadcrumbItem>
              <DropdownMenu>
                <DropdownMenuTrigger class="flex items-center gap-1">
                  <BreadcrumbEllipsis class="h-4 w-4" />
                  <span class="sr-only">Toggle menu</span>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuItem
                    v-for="hiddenItem in hiddenBreadcrumbs"
                    :key="hiddenItem.path || hiddenItem.title"
                    @click="handleClick(hiddenItem)"
                  >
                    {{ hiddenItem.title }}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </BreadcrumbItem>
          </template>

          <!-- 正常面包屑项 -->
          <template v-else>
            <BreadcrumbItem>
              <!-- 可点击的链接 -->
              <BreadcrumbLink
                v-if="item.path && !item.disabled"
                as="button"
                class="flex items-center gap-1 text-sm hover:text-foreground transition-colors"
                @click="handleClick(item)"
              >
                <component v-if="item.icon" :is="item.icon" class="w-4 h-4" />
                {{ item.title }}
              </BreadcrumbLink>

              <!-- 当前页面（不可点击） -->
              <BreadcrumbPage v-else class="flex items-center gap-1 text-sm">
                <component v-if="item.icon" :is="item.icon" class="w-4 h-4" />
                {{ item.title }}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </template>

          <!-- 分隔符 -->
          <BreadcrumbSeparator v-if="index !== displayBreadcrumbs.length - 1" />
        </template>
      </BreadcrumbList>
    </Breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { ArrowLeft } from 'lucide-vue-next'
import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { useBreadcrumb } from './useBreadcrumb'
import type { SuperBreadcrumbProps, BreadcrumbItem as BreadcrumbItemType } from './types'

defineOptions({ name: 'SuperBreadcrumb' })

const props = withDefaults(defineProps<SuperBreadcrumbProps>(), {
  showHome: true,
  maxItems: 5,
  autoGenerate: true,
  showBackButton: false
})

const emit = defineEmits<{
  'breadcrumb-click': [item: BreadcrumbItemType]
  'title-update': [title: string]
}>()

// 使用面包屑逻辑
const { breadcrumbs, handleBreadcrumbClick, updateCurrentTitle, goBack } = useBreadcrumb()

// 最终的面包屑数据
const finalBreadcrumbs = computed(() => {
  if (props.items) {
    return props.items
  }

  if (!props.autoGenerate) {
    return []
  }

  return breadcrumbs.value
})

// 处理省略号显示
const displayBreadcrumbs = computed(() => {
  const items = finalBreadcrumbs.value

  if (!props.maxItems || items.length <= props.maxItems) {
    return items
  }

  // 保留第一个和最后一个，中间用省略号
  const result = [items[0]]

  if (items.length > 2) {
    result.push({ title: '...', disabled: true })
    result.push(items[items.length - 1])
  }

  return result
})

// 隐藏的面包屑项（在省略号中显示）
const hiddenBreadcrumbs = computed(() => {
  const items = finalBreadcrumbs.value

  if (!props.maxItems || items.length <= props.maxItems) {
    return []
  }

  return items.slice(1, -1)
})

// 监听自定义标题变化
watch(() => props.currentTitle, (newTitle) => {
  if (newTitle) {
    updateCurrentTitle(newTitle)
    emit('title-update', newTitle)
  }
}, { immediate: true })

// 处理面包屑点击事件
const handleClick = (item: BreadcrumbItemType) => {
  handleBreadcrumbClick(item)
  emit('breadcrumb-click', item)
}

// 暴露方法给父组件
defineExpose({
  updateCurrentTitle,
  goBack
})
</script>
