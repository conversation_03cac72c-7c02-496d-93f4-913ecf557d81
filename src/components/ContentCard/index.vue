<template>
  <div class="space-y-3 group" :class="cardClass">
    <!-- 内容图片容器 -->
    <div
      class="relative overflow-hidden rounded-md cursor-pointer transition-transform"
      :class="{ 'hover:scale-105': hoverable }"
      @click="handleClick"
    >
      <slot name="image">
        <img
          :src="data.cover || data.image || data.thumbnail"
          :alt="data.name || data.title"
          :class="imageClass"
          class="w-full h-auto object-cover"
          :style="getAspectRatioStyle"
        />
      </slot>

      <!-- 状态标签 -->
      <slot name="badges">
        <div v-if="showBadges" class="absolute inset-0">
          <!-- 推荐/特色标签 -->
          <div v-if="data.isRecommended || data.isFeatured" class="absolute -left-px top-[-6px]">
            <div class="text-6xl">🏆</div>
          </div>

          <!-- 新内容标签 -->
          <div
            v-if="data.isNew"
            class="absolute top-[-6px]"
            :class="data.isRecommended || data.isFeatured ? 'left-[40px]' : '-left-px'"
          >
            <div class="text-6xl">✨</div>
          </div>

          <!-- 状态标签 -->
          <div v-if="data.status !== undefined && showStatus" class="absolute top-2 right-2 z-10">
            <div
              class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium shadow-lg"
              :class="`bg-${getStatusColor(data.status)}-100 text-${getStatusColor(data.status)}-800 border border-${getStatusColor(data.status)}-200`"
              :title="getStatusLabel(data.status)"
            >
              <span class="text-sm">{{ getStatusIcon(data.status) }}</span>
              <span class="hidden sm:inline">{{ getStatusLabel(data.status) }}</span>
            </div>
          </div>
        </div>
      </slot>

      <!-- 底部覆盖层信息 -->
      <slot name="overlay">
        <div v-if="showOverlay" class="absolute bottom-2 left-0 right-0 px-2 flex justify-between">
          <!-- 左侧信息 -->
          <div v-if="leftBadgeText" class="bg-transparent text-white px-2 py-1 rounded text-xs">
            {{ leftBadgeText }}
          </div>

          <!-- 右侧信息 -->
          <div v-if="rightBadgeText" class="bg-black/30 text-white px-2 py-1 rounded text-xs">
            {{ rightBadgeText }}
          </div>
        </div>
      </slot>
    </div>

    <!-- 内容信息区域 -->
    <div class="space-y-1">
      <slot name="content">
        <!-- 标题 -->
        <div>
          <h3
            class="font-medium leading-none line-clamp-2 cursor-pointer"
            :title="data.name || data.title"
          >
            {{ data.name || data.title }}
          </h3>
        </div>

        <!-- 副标题/描述 -->
        <div
          v-if="showSubtitle && (data.subtitle || data.description)"
          class="text-sm text-gray-600"
        >
          <slot name="subtitle">
            {{ data.subtitle || data.description }}
          </slot>
        </div>

        <!-- 评分 -->
        <div v-if="showRating && (data.rating || data.star)" class="flex items-center">
          <div class="flex">
            <span v-for="i in 5" :key="i" class="text-yellow-400">
              {{ i <= (data.rating || data.star || 0) ? '★' : '☆' }}
            </span>
          </div>
          <span class="ml-1 text-sm text-gray-600">{{ data.rating || data.star }}</span>
        </div>

        <!-- 进度条 -->
        <div v-if="showProgress && data.progress !== undefined">
          <div class="flex justify-between text-sm text-gray-600 mb-1">
            <span>Progress</span>
            <span>{{ data.progress }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${data.progress}%` }"
            ></div>
          </div>
        </div>

        <!-- 作者信息 -->
        <div v-if="showAuthor && data.author" class="text-sm text-gray-600">
          <span class="font-medium">{{ data.author }}</span>
        </div>

        <!-- 分类信息 -->
        <div v-if="data.category" class="text-sm text-gray-600">
          <span class="bg-gray-100 px-2 py-1 rounded text-xs">{{ data.category }}</span>
        </div>

        <!-- 标签 -->
        <div v-if="showTags && data.tags && data.tags.length > 0" class="flex flex-wrap gap-1">
          <span
            v-for="tag in data.tags.slice(0, 3)"
            :key="tag"
            class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
          >
            {{ tag }}
          </span>
        </div>

        <!-- 日期信息 -->
        <div v-if="showDate && (data.createdAt || data.updatedAt)" class="text-xs text-gray-500">
          {{ formatDate(data.createdAt || data.updatedAt) }}
        </div>

        <!-- 自定义元数据 -->
        <div v-if="showMeta && metaFields.length > 0" class="space-y-1">
          <div class="flex items-center gap-4 text-xs text-gray-600">
            <span v-for="field in metaFields" :key="field" class="flex items-center gap-1">
              <span class="font-medium">{{ field }}:</span>
              <span>{{ data[field] }}</span>
            </span>
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  ContentStatus,
  ContentDisplayMode,
  ContentVariant,
  AspectRatio,
  ContentStatusIcons,
  ContentStatusLabels,
  ContentStatusColors,
  AspectRatioValues,
  getContentStatusFromChapter
} from '../../enums/content'

// 通用内容数据接口
interface ContentData {
  id: string | number
  name?: string
  title?: string
  cover?: string
  image?: string
  thumbnail?: string
  subtitle?: string
  description?: string
  isRecommended?: boolean
  isNew?: boolean
  isFeatured?: boolean
  status?: number
  rating?: number
  star?: number
  progress?: number
  duration?: number
  author?: string
  category?: string
  tags?: string[]
  createdAt?: string
  updatedAt?: string
  [key: string]: any
}

interface Props {
  data: ContentData

  // 显示控制
  mode?: ContentDisplayMode
  variant?: ContentVariant
  showBadges?: boolean
  showStatus?: boolean
  showOverlay?: boolean
  showSubtitle?: boolean
  showRating?: boolean
  showProgress?: boolean
  showMeta?: boolean
  showAuthor?: boolean
  showDate?: boolean
  showTags?: boolean

  // 样式控制
  cardClass?: string
  imageClass?: string
  aspectRatio?: AspectRatio

  // 功能配置
  routeName?: string
  routeParams?: Record<string, any>
  clickable?: boolean
  hoverable?: boolean

  // 覆盖层信息
  leftBadgeText?: string
  rightBadgeText?: string

  // 元数据字段
  metaFields?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  mode: ContentDisplayMode.CARD,
  variant: ContentVariant.DEFAULT,
  showBadges: true,
  showStatus: false,
  showOverlay: false,
  showSubtitle: false,
  showRating: false,
  showProgress: false,
  showMeta: false,
  showAuthor: false,
  showDate: false,
  showTags: false,
  aspectRatio: AspectRatio.SQUARE,
  clickable: true,
  hoverable: true,
  routeName: '',
  metaFields: () => []
})

const emit = defineEmits<{
  click: [data: ContentData]
  action: [action: string, data: ContentData]
}>()

const router = useRouter()

// 获取状态图标 - 支持数字和枚举类型
const getStatusIcon = (status: ContentStatus | number) => {
  const contentStatus = typeof status === 'number' ? getContentStatusFromChapter(status) : status
  return ContentStatusIcons[contentStatus] || ContentStatusIcons[ContentStatus.DEFAULT]
}

// 获取状态标签 - 支持数字和枚举类型
const getStatusLabel = (status: ContentStatus | number) => {
  const contentStatus = typeof status === 'number' ? getContentStatusFromChapter(status) : status
  return ContentStatusLabels[contentStatus] || ContentStatusLabels[ContentStatus.DEFAULT]
}

// 获取状态颜色 - 支持数字和枚举类型
const getStatusColor = (status: ContentStatus | number) => {
  const contentStatus = typeof status === 'number' ? getContentStatusFromChapter(status) : status
  return ContentStatusColors[contentStatus] || ContentStatusColors[ContentStatus.DEFAULT]
}

// 格式化日期
const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}

// 获取宽高比样式
const getAspectRatioStyle = computed(() => {
  if (props.aspectRatio === AspectRatio.AUTO) {
    return {}
  }

  return {
    aspectRatio: AspectRatioValues[props.aspectRatio] || AspectRatioValues[AspectRatio.SQUARE]
  }
})

// 点击处理
const handleClick = () => {
  if (!props.clickable) return

  emit('click', props.data)

  if (props.routeName) {
    const params = props.routeParams || { id: props.data.id }
    router.push({ name: props.routeName, params })
  }
}

// 动作处理
const handleAction = (action: string) => {
  emit('action', action, props.data)
}
</script>
