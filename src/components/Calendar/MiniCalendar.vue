<template>
  <div class="mini-calendar">
    <!-- 月份导航 -->
    <div class="mini-calendar-header">
      <button
        @click="previousMonth"
        class="calendar-nav-button"
      >
        <ChevronLeft class="w-4 h-4" />
      </button>

      <h3 class="calendar-title">
        {{ currentMonth.format('YYYY年M月') }}
      </h3>

      <button
        @click="nextMonth"
        class="calendar-nav-button"
      >
        <ChevronRight class="w-4 h-4" />
      </button>
    </div>

    <!-- 星期标题 -->
    <div class="mini-calendar-weekdays">
      <div
        v-for="day in weekdays"
        :key="day"
        class="weekday-label"
      >
        {{ day }}
      </div>
    </div>

    <!-- 日期网格 -->
    <div class="mini-calendar-grid">
      <button
        v-for="day in calendarDays"
        :key="day.format('YYYY-MM-DD')"
        class="mini-calendar-day"
        :class="{
          'today': day.isSame(dayjs(), 'day'),
          'selected': day.isSame(selectedDate, 'day'),
          'other-month': !day.isSame(currentMonth, 'month'),
          'has-events': hasEventsOnDay(day)
        }"
        @click="selectDate(day)"
      >
        <span class="day-number">{{ day.format('D') }}</span>
        <div v-if="hasEventsOnDay(day)" class="event-dots">
          <div
            v-for="(event, index) in getEventsForDay(day).slice(0, 3)"
            :key="index"
            class="event-dot"
            :style="{ backgroundColor: getEventColor(event) }"
          ></div>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'

interface Event {
  id: string
  startTime: string
  color?: string
  type?: string
}

interface Props {
  selectedDate: Dayjs
  events?: Event[]
}

const props = withDefaults(defineProps<Props>(), {
  events: () => []
})

const emit = defineEmits<{
  'update:selectedDate': [date: Dayjs]
  'date-select': [date: Dayjs]
}>()

// 状态
const currentMonth = ref<Dayjs>(props.selectedDate.startOf('month'))

// 计算属性
const weekdays = computed(() => ['日', '一', '二', '三', '四', '五', '六'])

const calendarDays = computed(() => {
  const startOfMonth = currentMonth.value.startOf('month')
  const endOfMonth = currentMonth.value.endOf('month')
  const startOfCalendar = startOfMonth.startOf('week')
  const endOfCalendar = endOfMonth.endOf('week')

  const days = []
  let current = startOfCalendar

  while (current.isBefore(endOfCalendar) || current.isSame(endOfCalendar, 'day')) {
    days.push(current)
    current = current.add(1, 'day')
  }

  return days
})

// 方法
const previousMonth = () => {
  currentMonth.value = currentMonth.value.subtract(1, 'month')
}

const nextMonth = () => {
  currentMonth.value = currentMonth.value.add(1, 'month')
}

const selectDate = (date: Dayjs) => {
  emit('update:selectedDate', date)
  emit('date-select', date)
}

const hasEventsOnDay = (day: Dayjs) => {
  return props.events.some(event => {
    const eventDate = dayjs(event.startTime)
    return eventDate.isSame(day, 'day')
  })
}

const getEventsForDay = (day: Dayjs) => {
  return props.events.filter(event => {
    const eventDate = dayjs(event.startTime)
    return eventDate.isSame(day, 'day')
  })
}

const getEventColor = (event: Event) => {
  if (event.color) return event.color

  // 根据事件类型返回默认颜色
  const typeColors = {
    course: 'var(--calendar-event-blue)',
    assignment: 'var(--calendar-event-orange)',
    exam: 'var(--calendar-event-red)',
    meeting: 'var(--calendar-event-green)'
  }

  return typeColors[event.type as keyof typeof typeColors] || 'var(--calendar-event-blue)'
}
</script>

<style scoped>
@reference "tailwindcss";

.mini-calendar {
  @apply w-full;
}

.mini-calendar-header {
  @apply flex items-center justify-between mb-3;
}

.calendar-nav-button {
  @apply p-1 rounded hover:bg-gray-100 transition-colors;
  color: #8E8E93;
}

.calendar-title {
  @apply font-medium;
  font-size: var(--calendar-text-base);
  color: var(--calendar-text-primary);
}

.mini-calendar-weekdays {
  @apply grid grid-cols-7 mb-2;
}

.weekday-label {
  @apply text-center py-1;
  font-size: var(--calendar-text-xs);
  color: var(--calendar-text-secondary);
  font-weight: 500;
}

.mini-calendar-grid {
  @apply grid grid-cols-7 gap-1;
}

.mini-calendar-day {
  @apply relative w-8 h-8 flex flex-col items-center justify-center rounded-full transition-all;
  font-size: var(--calendar-text-xs);
  color: var(--calendar-text-primary);
}

.mini-calendar-day:hover {
  @apply bg-gray-100;
}

.mini-calendar-day.today {
  @apply bg-blue-500 text-white font-medium;
}

.mini-calendar-day.selected {
  @apply bg-blue-50;
}

.mini-calendar-day.selected.today {
  @apply bg-blue-500 text-white;
}

.mini-calendar-day.other-month {
  color: var(--calendar-text-tertiary);
}

.mini-calendar-day.has-events {
  @apply font-medium;
}

.day-number {
  @apply leading-none;
}

.event-dots {
  @apply absolute bottom-0 flex gap-0.5;
}

.event-dot {
  @apply w-1 h-1 rounded-full;
}
</style>
