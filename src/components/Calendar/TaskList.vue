<template>
  <div class="task-list h-full flex flex-col">
    <!-- 标题栏 -->
    <div class="task-list-header">
      <h3 class="task-list-title">
        {{ selectedDate.format('M月D日') }} 的任务
      </h3>
      <div class="task-count">
        {{ tasks.length }} 个任务
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-content flex-1 overflow-auto">
      <div v-if="tasks.length === 0" class="empty-state">
        <div class="empty-icon">📅</div>
        <p class="empty-text">今天没有安排任务</p>
        <p class="empty-subtext">享受轻松的一天吧</p>
      </div>

      <div v-else class="task-cards space-y-3">
        <div
          v-for="task in sortedTasks"
          :key="task.id"
          class="task-card"
          :class="{
            'task-completed': task.status === 'completed',
            'task-in-progress': task.status === 'in-progress',
            'task-high-priority': task.priority === 'high'
          }"
          @click="selectTask(task)"
        >
          <!-- 任务状态指示器 -->
          <div class="task-status-indicator" :class="getStatusClass(task.status)"></div>

          <!-- 任务内容 -->
          <div class="task-content">
            <!-- 任务标题和时间 -->
            <div class="task-header">
              <h4 class="task-title">{{ task.title }}</h4>
              <div class="task-time">
                {{ formatTaskTime(task) }}
              </div>
            </div>

            <!-- 任务描述 -->
            <p v-if="task.description" class="task-description">
              {{ task.description }}
            </p>

            <!-- 任务标签 -->
            <div class="task-meta">
              <span class="task-type-badge" :class="getTypeBadgeClass(task.type)">
                {{ getTypeLabel(task.type) }}
              </span>

              <span
                v-if="task.priority === 'high'"
                class="task-priority-badge"
              >
                高优先级
              </span>

              <span class="task-duration">
                {{ calculateDuration(task.startTime, task.endTime) }}
              </span>
            </div>
          </div>

          <!-- 任务操作 -->
          <div class="task-actions">
            <button
              v-if="task.status !== 'completed'"
              class="task-action-btn complete-btn"
              @click.stop="markAsCompleted(task)"
            >
              ✓
            </button>

            <button
              class="task-action-btn detail-btn"
              @click.stop="selectTask(task)"
            >
              →
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'

interface Task {
  id: string
  title: string
  description?: string
  startTime: string
  endTime: string
  status: 'pending' | 'in-progress' | 'completed'
  type: 'course' | 'assignment' | 'exam' | 'meeting'
  priority: 'low' | 'medium' | 'high'
  color?: string
}

interface Props {
  tasks: Task[]
  selectedDate: Dayjs
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'task-select': [task: Task]
  'task-update': [task: Task]
}>()

// 计算属性
const sortedTasks = computed(() => {
  return [...props.tasks].sort((a, b) => {
    // 首先按状态排序：进行中 > 待处理 > 已完成
    const statusOrder = { 'in-progress': 0, 'pending': 1, 'completed': 2 }
    const statusDiff = statusOrder[a.status] - statusOrder[b.status]
    if (statusDiff !== 0) return statusDiff

    // 然后按优先级排序：高 > 中 > 低
    const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 }
    const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority]
    if (priorityDiff !== 0) return priorityDiff

    // 最后按开始时间排序
    return dayjs(a.startTime).diff(dayjs(b.startTime))
  })
})

// 方法
const selectTask = (task: Task) => {
  emit('task-select', task)
}

const markAsCompleted = (task: Task) => {
  const updatedTask = { ...task, status: 'completed' as const }
  emit('task-update', updatedTask)
}

const formatTaskTime = (task: Task) => {
  const start = dayjs(task.startTime)
  const end = dayjs(task.endTime)

  if (start.isSame(end, 'day')) {
    return `${start.format('HH:mm')} - ${end.format('HH:mm')}`
  } else {
    return `${start.format('M/D HH:mm')} - ${end.format('M/D HH:mm')}`
  }
}

const calculateDuration = (startTime: string, endTime: string) => {
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const duration = end.diff(start, 'minute')

  const hours = Math.floor(duration / 60)
  const minutes = duration % 60

  if (hours > 0 && minutes > 0) {
    return `${hours}h ${minutes}m`
  } else if (hours > 0) {
    return `${hours}h`
  } else {
    return `${minutes}m`
  }
}

const getStatusClass = (status: string) => {
  const classes = {
    'pending': 'status-pending',
    'in-progress': 'status-in-progress',
    'completed': 'status-completed'
  }
  return classes[status as keyof typeof classes] || 'status-pending'
}

const getTypeBadgeClass = (type: string) => {
  const classes = {
    'course': 'type-course',
    'assignment': 'type-assignment',
    'exam': 'type-exam',
    'meeting': 'type-meeting'
  }
  return classes[type as keyof typeof classes] || 'type-course'
}

const getTypeLabel = (type: string) => {
  const labels = {
    'course': '课程',
    'assignment': '作业',
    'exam': '考试',
    'meeting': '会议'
  }
  return labels[type as keyof typeof labels] || '任务'
}
</script>

<style scoped>
@reference "tailwindcss";

.task-list {
  @apply h-full;
}

.task-list-header {
  @apply flex items-center justify-between mb-4 pb-2 border-b border-gray-200;
}

.task-list-title {
  @apply font-medium;
  font-size: var(--calendar-text-base);
  color: var(--calendar-text-primary);
}

.task-count {
  @apply text-sm;
  color: var(--calendar-text-secondary);
}

.task-list-content {
  @apply flex-1 overflow-auto;
}

/* 空状态 */
.empty-state {
  @apply flex flex-col items-center justify-center h-full text-center py-8;
}

.empty-icon {
  @apply text-4xl mb-3;
}

.empty-text {
  @apply font-medium mb-1;
  color: var(--calendar-text-primary);
}

.empty-subtext {
  @apply text-sm;
  color: var(--calendar-text-secondary);
}

/* 任务卡片 */
.task-card {
  @apply relative bg-white border border-gray-200 rounded-lg p-3 cursor-pointer transition-all;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.task-card:hover {
  @apply border-gray-300;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.task-card.task-completed {
  @apply opacity-60;
}

.task-card.task-high-priority {
  @apply border-l-4 border-l-red-500;
}

.task-status-indicator {
  @apply absolute left-0 top-0 bottom-0 w-1 rounded-l-lg;
}

.status-pending {
  @apply bg-gray-400;
}

.status-in-progress {
  @apply bg-blue-500;
}

.status-completed {
  @apply bg-green-500;
}

.task-content {
  @apply flex-1 pr-2;
}

.task-header {
  @apply flex items-start justify-between mb-2;
}

.task-title {
  @apply font-medium flex-1 mr-2;
  font-size: var(--calendar-text-base);
  color: var(--calendar-text-primary);
}

.task-time {
  @apply text-sm font-mono;
  color: var(--calendar-text-secondary);
}

.task-description {
  @apply text-sm mb-3;
  color: var(--calendar-text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.task-meta {
  @apply flex items-center gap-2 flex-wrap;
}

.task-type-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.type-course {
  @apply bg-blue-100 text-blue-700;
}

.type-assignment {
  @apply bg-orange-100 text-orange-700;
}

.type-exam {
  @apply bg-red-100 text-red-700;
}

.type-meeting {
  @apply bg-green-100 text-green-700;
}

.task-priority-badge {
  @apply px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium;
}

.task-duration {
  @apply text-xs;
  color: var(--calendar-text-tertiary);
}

.task-actions {
  @apply absolute top-3 right-3 flex gap-1;
}

.task-action-btn {
  @apply w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold transition-all;
}

.complete-btn {
  @apply bg-green-100 text-green-700 hover:bg-green-200;
}

.detail-btn {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

/* 滚动条样式 */
.task-list-content {
  scrollbar-width: thin;
  scrollbar-color: var(--calendar-border) transparent;
}

.task-list-content::-webkit-scrollbar {
  width: 4px;
}

.task-list-content::-webkit-scrollbar-track {
  background: transparent;
}

.task-list-content::-webkit-scrollbar-thumb {
  background-color: var(--calendar-border);
  border-radius: 2px;
}
</style>
