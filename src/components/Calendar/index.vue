<template>
  <div class="right-calendar-container h-full flex flex-col bg-white">
    <!-- 迷你日历 -->
    <div class="calendar-section flex-shrink-0">
      <MiniCalendar
        v-model:selected-date="selectedDate"
        :events="todayTasks"
        @date-select="handleDateSelect"
      />
    </div>

    <!-- 分隔线 -->
    <div class="border-t border-gray-200 my-2"></div>

    <!-- 任务列表/详情区域 -->
    <div class="task-section flex-1 flex flex-col min-h-0">
      <!-- 仅日历模式 -->
      <div v-if="props.mode === 'calendar_only'" class="empty-state flex-1 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <div class="text-4xl mb-2">📅</div>
          <p class="text-sm">选择日期查看任务</p>
        </div>
      </div>

      <!-- 任务列表视图 -->
      <TaskList
        v-else-if="props.mode === 'task_list'"
        :tasks="selectedDateTasks"
        :selected-date="selectedDate"
        @task-select="handleTaskSelect"
        @task-update="handleTaskUpdate"
      />

      <!-- 任务详情视图 -->
      <TaskDetail
        v-else-if="props.mode === 'event_detail' && selectedTask"
        :task="selectedTask"
        @back="handleBackToList"
        @task-update="handleTaskUpdate"
        @task-action="handleEventAction"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'
import MiniCalendar from './MiniCalendar.vue'
import TaskList from './TaskList.vue'
import TaskDetail from './TaskDetail.vue'
import { useRightCalendarSync } from '@/views/calendar/hooks/useCalendarSync'

interface Task {
  id: string
  title: string
  description?: string
  startTime: string
  endTime: string
  status: 'pending' | 'in-progress' | 'completed'
  type: 'course' | 'assignment' | 'exam' | 'meeting'
  priority: 'low' | 'medium' | 'high'
  color?: string
}

interface Props {
  events?: Task[]
  selectedDate?: Dayjs
  selectedEvent?: Task | null
  mode?: 'calendar_only' | 'task_list' | 'event_detail'
  showMiniCalendar?: boolean
  compactMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  events: () => [],
  selectedDate: () => dayjs(),
  selectedEvent: null,
  mode: 'calendar_only',
  showMiniCalendar: true,
  compactMode: false
})

const emit = defineEmits<{
  'date-select': [date: Dayjs]
  'event-select': [event: Task]
  'event-update': [event: Task]
  'event-action': [action: string, event: Task]
  'back-to-list': []
}>()

// 状态管理
const selectedDate = ref<Dayjs>(props.selectedDate)
const selectedTask = ref<Task | null>(props.selectedEvent)

// 使用日历同步 hook
const { syncWithMainCalendar, onMainCalendarChange } = useRightCalendarSync()

// 计算属性
const selectedDateTasks = computed(() => {
  return props.events.filter(event => {
    const eventDate = dayjs(event.startTime)
    return eventDate.isSame(selectedDate.value, 'day')
  })
})

const todayTasks = computed(() => {
  return props.events.filter(event => {
    const eventDate = dayjs(event.startTime)
    return eventDate.isSame(dayjs(), 'day')
  })
})

// 事件处理
const handleDateSelect = (date: Dayjs) => {
  selectedDate.value = date
  selectedTask.value = null // 切换日期时清除选中的任务
  emit('date-select', date)

  // 同步到主日历
  syncWithMainCalendar('date-change', { date })
}

const handleTaskSelect = (task: Task) => {
  selectedTask.value = task
  emit('event-select', task)

  // 同步到主日历
  syncWithMainCalendar('event-select', { event: task })
}

const handleBackToList = () => {
  selectedTask.value = null
  emit('back-to-list')
}

const handleTaskUpdate = (updatedTask: Task) => {
  selectedTask.value = updatedTask
  emit('event-update', updatedTask)

  // 同步到主日历
  syncWithMainCalendar('event-update', { event: updatedTask })
}

const handleEventAction = (action: string, event: Task) => {
  emit('event-action', action, event)
}

// 监听 props 变化
watch(() => props.selectedDate, (newDate) => {
  if (newDate && !newDate.isSame(selectedDate.value, 'day')) {
    selectedDate.value = newDate
  }
}, { immediate: true })

watch(() => props.selectedEvent, (newEvent) => {
  selectedTask.value = newEvent
}, { immediate: true })

// 监听主日历的变化
onMainCalendarChange((event, data) => {
  switch (event) {
    case 'date-change':
      if (data.date && !data.date.isSame(selectedDate.value, 'day')) {
        selectedDate.value = data.date
        selectedTask.value = null
      }
      break
    case 'activity-select':
      // 如果主日历选中了活动，尝试找到对应的任务
      if (data.activity) {
        const correspondingTask = props.events.find(t => t.id === data.activity.id)
        if (correspondingTask) {
          selectedTask.value = correspondingTask
          selectedDate.value = dayjs(correspondingTask.startTime)
        }
      }
      break
    case 'event-select':
      if (data.event) {
        selectedTask.value = data.event
        selectedDate.value = dayjs(data.event.startTime)
      }
      break
  }
})

// 暴露方法给父组件
defineExpose({
  selectTask: (taskId: string) => {
    const task = props.tasks.find(t => t.id === taskId)
    if (task) {
      handleTaskSelect(task)
    }
  },
  selectDate: (date: Dayjs) => {
    handleDateSelect(date)
  },
  clearSelection: () => {
    selectedTask.value = null
  }
})
</script>

<style scoped>
.right-calendar-container {
  min-width: 280px;
  max-width: 320px;
}

.calendar-section {
  padding: 16px;
}

.task-section {
  padding: 0 16px 16px;
}

/* 确保任务区域可以滚动 */
.task-section {
  overflow: hidden;
}
</style>
