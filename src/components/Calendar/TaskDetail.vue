<template>
  <div class="task-detail h-full flex flex-col">
    <!-- 返回按钮 -->
    <div class="task-detail-header">
      <button
        @click="$emit('back')"
        class="back-button"
      >
        <ArrowLeft class="w-4 h-4 mr-2" />
        返回列表
      </button>
    </div>

    <!-- 任务详情内容 -->
    <div class="task-detail-content flex-1 overflow-auto">
      <!-- 任务标题和状态 -->
      <div class="task-header-section">
        <div class="task-status-bar" :class="getStatusClass(task.status)"></div>
        <div class="task-header-content">
          <h2 class="task-title">{{ task.title }}</h2>
          <div class="task-status-badge" :class="getStatusBadgeClass(task.status)">
            {{ getStatusLabel(task.status) }}
          </div>
        </div>
      </div>

      <!-- 任务时间信息 -->
      <div class="task-section">
        <h3 class="section-title">
          <Clock class="w-4 h-4 mr-2" />
          时间安排
        </h3>
        <div class="time-info">
          <div class="time-row">
            <span class="time-label">开始时间:</span>
            <span class="time-value">{{ formatDateTime(task.startTime) }}</span>
          </div>
          <div class="time-row">
            <span class="time-label">结束时间:</span>
            <span class="time-value">{{ formatDateTime(task.endTime) }}</span>
          </div>
          <div class="time-row">
            <span class="time-label">持续时间:</span>
            <span class="time-value duration">{{ calculateDuration(task.startTime, task.endTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 任务详情 -->
      <div class="task-section">
        <h3 class="section-title">
          <FileText class="w-4 h-4 mr-2" />
          任务详情
        </h3>
        <div class="task-meta-grid">
          <div class="meta-item">
            <span class="meta-label">类型:</span>
            <span class="meta-value">
              <span class="task-type-badge" :class="getTypeBadgeClass(task.type)">
                {{ getTypeLabel(task.type) }}
              </span>
            </span>
          </div>
          <div class="meta-item">
            <span class="meta-label">优先级:</span>
            <span class="meta-value">
              <span class="priority-badge" :class="getPriorityBadgeClass(task.priority)">
                {{ getPriorityLabel(task.priority) }}
              </span>
            </span>
          </div>
          <div class="meta-item">
            <span class="meta-label">任务ID:</span>
            <span class="meta-value task-id">{{ task.id }}</span>
          </div>
        </div>
      </div>

      <!-- 任务描述 -->
      <div v-if="task.description" class="task-section">
        <h3 class="section-title">
          <AlignLeft class="w-4 h-4 mr-2" />
          描述
        </h3>
        <div class="task-description">
          {{ task.description }}
        </div>
      </div>

      <!-- 任务操作 -->
      <div class="task-section">
        <h3 class="section-title">
          <Settings class="w-4 h-4 mr-2" />
          操作
        </h3>
        <div class="task-actions">
          <button
            v-if="task.status === 'pending'"
            @click="handleTaskAction('start')"
            class="action-button start-button"
          >
            <Play class="w-4 h-4 mr-2" />
            开始任务
          </button>

          <button
            v-if="task.status === 'in-progress'"
            @click="handleTaskAction('complete')"
            class="action-button complete-button"
          >
            <CheckCircle class="w-4 h-4 mr-2" />
            完成任务
          </button>

          <button
            v-if="task.status === 'completed'"
            @click="updateTaskStatus('pending')"
            class="action-button reset-button"
          >
            <RotateCcw class="w-4 h-4 mr-2" />
            重置状态
          </button>

          <button
            @click="handleTaskAction('edit')"
            class="action-button edit-button"
          >
            <ExternalLink class="w-4 h-4 mr-2" />
            编辑任务
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import {
  ArrowLeft,
  Clock,
  FileText,
  AlignLeft,
  Settings,
  Play,
  CheckCircle,
  RotateCcw,
  ExternalLink
} from 'lucide-vue-next'

interface Task {
  id: string
  title: string
  description?: string
  startTime: string
  endTime: string
  status: 'pending' | 'in-progress' | 'completed'
  type: 'course' | 'assignment' | 'exam' | 'meeting'
  priority: 'low' | 'medium' | 'high'
  color?: string
}

interface Props {
  task: Task
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'back': []
  'task-update': [task: Task]
  'task-action': [action: string, task: Task]
}>()

// 方法
const updateTaskStatus = (newStatus: Task['status']) => {
  const updatedTask = { ...props.task, status: newStatus }
  emit('task-update', updatedTask)
}

const handleTaskAction = (action: string) => {
  emit('task-action', action, props.task)

  // 同时更新本地状态
  if (action === 'start') {
    updateTaskStatus('in-progress')
  } else if (action === 'complete') {
    updateTaskStatus('completed')
  }
}

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY年M月D日 HH:mm')
}

const calculateDuration = (startTime: string, endTime: string) => {
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const duration = end.diff(start, 'minute')

  const hours = Math.floor(duration / 60)
  const minutes = duration % 60

  if (hours > 0 && minutes > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时`
  } else {
    return `${minutes}分钟`
  }
}

const getStatusClass = (status: string) => {
  const classes = {
    'pending': 'status-pending',
    'in-progress': 'status-in-progress',
    'completed': 'status-completed'
  }
  return classes[status as keyof typeof classes] || 'status-pending'
}

const getStatusBadgeClass = (status: string) => {
  const classes = {
    'pending': 'badge-pending',
    'in-progress': 'badge-in-progress',
    'completed': 'badge-completed'
  }
  return classes[status as keyof typeof classes] || 'badge-pending'
}

const getStatusLabel = (status: string) => {
  const labels = {
    'pending': '待处理',
    'in-progress': '进行中',
    'completed': '已完成'
  }
  return labels[status as keyof typeof labels] || '未知'
}

const getTypeBadgeClass = (type: string) => {
  const classes = {
    'course': 'type-course',
    'assignment': 'type-assignment',
    'exam': 'type-exam',
    'meeting': 'type-meeting'
  }
  return classes[type as keyof typeof classes] || 'type-course'
}

const getTypeLabel = (type: string) => {
  const labels = {
    'course': '课程',
    'assignment': '作业',
    'exam': '考试',
    'meeting': '会议'
  }
  return labels[type as keyof typeof labels] || '任务'
}

const getPriorityBadgeClass = (priority: string) => {
  const classes = {
    'low': 'priority-low',
    'medium': 'priority-medium',
    'high': 'priority-high'
  }
  return classes[priority as keyof typeof classes] || 'priority-medium'
}

const getPriorityLabel = (priority: string) => {
  const labels = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return labels[priority as keyof typeof labels] || '中'
}
</script>

<style scoped>
@reference "tailwindcss";

.task-detail {
  @apply h-full;
}

.task-detail-header {
  @apply mb-4 pb-3 border-b border-gray-200;
}

.back-button {
  @apply flex items-center text-sm font-medium transition-colors;
  color: var(--calendar-text-secondary);
}

.back-button:hover {
  color: var(--calendar-text-primary);
}

.task-detail-content {
  @apply flex-1 overflow-auto space-y-6;
}

/* 任务标题区域 */
.task-header-section {
  @apply relative bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100;
}

.task-status-bar {
  @apply absolute left-0 top-0 bottom-0 w-1 rounded-l-lg;
}

.status-pending {
  @apply bg-gray-400;
}

.status-in-progress {
  @apply bg-blue-500;
}

.status-completed {
  @apply bg-green-500;
}

.task-header-content {
  @apply flex items-start justify-between;
}

.task-title {
  @apply text-lg font-semibold flex-1 mr-3;
  color: var(--calendar-text-primary);
}

.task-status-badge {
  @apply px-3 py-1 rounded-full text-sm font-medium;
}

.badge-pending {
  @apply bg-gray-100 text-gray-700;
}

.badge-in-progress {
  @apply bg-blue-100 text-blue-700;
}

.badge-completed {
  @apply bg-green-100 text-green-700;
}

/* 任务区块 */
.task-section {
  @apply bg-white rounded-lg p-4 border border-gray-200;
}

.section-title {
  @apply flex items-center font-medium mb-3;
  color: var(--calendar-text-primary);
}

/* 时间信息 */
.time-info {
  @apply space-y-2;
}

.time-row {
  @apply flex justify-between items-center;
}

.time-label {
  @apply text-sm;
  color: var(--calendar-text-secondary);
}

.time-value {
  @apply text-sm font-medium;
  color: var(--calendar-text-primary);
}

.time-value.duration {
  @apply bg-blue-50 text-blue-700 px-2 py-1 rounded;
}

/* 元数据网格 */
.task-meta-grid {
  @apply space-y-3;
}

.meta-item {
  @apply flex justify-between items-center;
}

.meta-label {
  @apply text-sm;
  color: var(--calendar-text-secondary);
}

.meta-value {
  @apply text-sm font-medium;
}

.task-id {
  @apply font-mono text-xs bg-gray-100 px-2 py-1 rounded;
}

/* 徽章样式 */
.task-type-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.type-course {
  @apply bg-blue-100 text-blue-700;
}

.type-assignment {
  @apply bg-orange-100 text-orange-700;
}

.type-exam {
  @apply bg-red-100 text-red-700;
}

.type-meeting {
  @apply bg-green-100 text-green-700;
}

.priority-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.priority-low {
  @apply bg-gray-100 text-gray-700;
}

.priority-medium {
  @apply bg-yellow-100 text-yellow-700;
}

.priority-high {
  @apply bg-red-100 text-red-700;
}

/* 任务描述 */
.task-description {
  @apply text-sm leading-relaxed;
  color: var(--calendar-text-secondary);
}

/* 操作按钮 */
.task-actions {
  @apply space-y-2;
}

.action-button {
  @apply w-full flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all;
}

.start-button {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.complete-button {
  @apply bg-green-500 text-white hover:bg-green-600;
}

.reset-button {
  @apply bg-gray-500 text-white hover:bg-gray-600;
}

.edit-button {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}
</style>
