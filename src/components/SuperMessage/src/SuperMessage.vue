<template>
  <!--确认弹框-->
  <AlertDialog :open="confirmState.isOpen" @open-change="handleConfirmChange">
    <AlertDialogTrigger as-child>
      <slot name="trigger"></slot>
    </AlertDialogTrigger>
    <AlertDialogContent class="z-[100]">
      <AlertDialogHeader>
        <AlertDialogTitle v-if="confirmState.title">
          {{ confirmState.title }}
        </AlertDialogTitle>
        <AlertDialogDescription style="word-break: break-word;">
          {{ confirmState.description }}
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="handleCancel">{{ confirmState.cancelButtonText }}</AlertDialogCancel>
        <AlertDialogAction @click="handleConfirm">{{ confirmState.confirmButtonText }}</AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>

  <!--消息弹框-->
  <AlertDialog :open="alertState.isOpen" @open-change="handleAlertChange">
    <AlertDialogTrigger as-child>
      <slot name="trigger"></slot>
    </AlertDialogTrigger>
    <AlertDialogContent class="lg:max-w-[20rem] z-[100]">
      <AlertDialogHeader>
        <AlertDialogTitle v-if="alertState.title">
          {{ alertState.title }}
        </AlertDialogTitle>
        <AlertDialogDescription style="word-break: break-word;">
          {{ alertState.description }}
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogAction class="justify-center" @click="handleAlert">{{ alertState.okButtonText }}</AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>

  <!--消息-->
  <ToastProvider>
    <Toast v-for="toast in toasts" :key="toast.id" v-bind="toast">
      <div class="grid gap-1">
        <ToastTitle v-if="toast.title">
          {{ toast.title }}
        </ToastTitle>
        <template v-if="toast.description">
          <ToastDescription v-if="isVNode(toast.description)" style="word-break: break-word;">
            <component :is="toast.description" />
          </ToastDescription>
          <ToastDescription v-else style="word-break: break-word;" class="m-1!">
            {{ toast.description }}
          </ToastDescription>
        </template>
        <ToastClose />
      </div>
      <component :is="toast.action" />
    </Toast>
    <ToastViewport />
  </ToastProvider>
</template>


<script setup lang="ts">
import { useMessage } from '@/hooks/web/useMessage'
import { useToast } from '@/components/ui/toast/use-toast'

import {isVNode} from "vue";

const { toasts } = useToast()
const { confirmState, alertState, closeConfirmDialog, closeAlertDialog, handleConfirm, handleCancel, handleAlert } = useMessage()
const handleConfirmChange = (open: boolean) => {
  if (!open) {
    closeConfirmDialog()
  }
}
const handleAlertChange = (open: boolean) => {
  if (!open) {
    closeAlertDialog()
  }
}
</script>
