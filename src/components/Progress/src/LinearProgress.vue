<script setup lang="ts">
import { computed } from 'vue'
import { ProgressIndicator, ProgressRoot } from 'reka-ui'

const props = defineProps<{
  progress: number // 进度值（直接通过 props 传入）
  height?: string // 进度条高度（默认 4px）
  color?: string // 进度条颜色（默认绿色）
}>()

const progressStyle = computed(() => ({
  transform: `translateX(-${100 - props.progress}%)`,
  backgroundColor: props.color || 'hsl(var(--primary))', // 默认绿色
  height: props.height || '6px'
}))
</script>

<template>
  <div class="relative flex items-center w-full">
    <!-- 进度条 -->
    <ProgressRoot
      :value="progress"
      class="relative flex-1 bg-gray-200 border rounded-full dark:bg-stone-950 border-muted"
      :style="{ height: height || '6px' }"
    >
      <ProgressIndicator
        class="relative block transition-all duration-500 ease-in-out rounded-full indicator"
        :style="progressStyle"
      />
    </ProgressRoot>

    <!-- 进度文本（显示在进度条后面）-->
    <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"> {{ progress }}% </span>
  </div>
</template>
