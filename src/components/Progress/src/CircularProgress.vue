<script setup lang="ts">
import { computed, defineProps } from 'vue'

const radius = 40
const circumference = 2 * Math.PI * radius

const props = defineProps({
  progress: { type: Number, required: true } // 进度（0-100）
})

const dashOffset = computed(() => circumference * (1 - props.progress / 100))
</script>

<template>
  <div class="relative w-16 h-16">
    <svg class="w-full h-full -rotate-90" viewBox="0 0 100 100">
      <!-- Background circle (light gray) -->
      <circle cx="50" cy="50" r="40" fill="transparent" stroke="#e5e7eb" stroke-width="6" />
      <!-- Progress circle (primary color) -->
      <circle
        cx="50"
        cy="50"
        r="40"
        fill="transparent"
        stroke="hsl(var(--primary))"
        stroke-width="6"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="dashOffset"
        class="transition-all duration-500 ease-out"
      />
    </svg>
    <!-- Progress text -->
    <div class="absolute inset-0 flex items-center justify-center">
      <span class="text-xs font-medium">{{ progress }}%</span>
    </div>
  </div>
</template>
