<template>
  <div class="flex flex-col h-full">
    <!-- Orientation List -->
    <div v-if="orientationList.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
      <OrientationCard
        v-for="orientation in orientationList"
        :key="orientation.id"
        :orientation="orientation"
        @click="handleOrientationClick"
      />
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="min-h-[60vh] flex flex-col items-center justify-center text-center">
      <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
        <BookOpen class="w-12 h-12 text-slate-400" />
      </div>
      <h3 class="text-lg font-semibold text-slate-900 mb-2">No Orientations Found</h3>
      <p class="text-slate-500 max-w-md">
        You don't have any {{ currentTabLabel.toLowerCase() }} orientations yet
      </p>
    </div>

    <!-- Loading State -->
    <div v-else class="min-h-[60vh] flex items-center justify-center">
      <div class="flex flex-col items-center gap-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p class="text-sm text-muted-foreground">Loading orientations...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import OrientationCard from './OrientationCard.vue'
import { BookOpen } from 'lucide-vue-next'
import type { OrientationNavListVO } from '@/api/orientation'

interface Props {
  orientationList: OrientationNavListVO[]
  loading?: boolean
  currentTabLabel?: string
}

interface Emits {
  (e: 'orientation-click', orientation: OrientationNavListVO): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  currentTabLabel: 'orientations'
})

const emit = defineEmits<Emits>()

// Handle orientation click
const handleOrientationClick = (orientation: OrientationNavListVO) => {
  emit('orientation-click', orientation)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
