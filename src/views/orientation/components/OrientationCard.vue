<template>
  <div
    class="group relative overflow-hidden rounded-lg border bg-card hover:shadow-lg transition-all duration-200"
    :class="[isClickable ? 'cursor-pointer' : 'cursor-not-allowed opacity-60']"
    @click="handleClick"
  >
    <!-- Image Area -->
    <div class="h-32 relative overflow-hidden bg-gradient-to-br from-slate-100 to-slate-200">
      <LazyImage
        :src="orientation.cover"
        :alt="orientation?.title || orientation?.name"
        :aspect-ratio="'16/9'"
        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
      />

      <!-- Status Badge -->
      <div class="absolute top-2 right-2">
        <Badge
          :variant="isClickable ? 'default' : 'secondary'"
          class="text-xs font-medium px-2 py-1"
        >
          {{ isClickable ? 'Available' : 'Coming Soon' }}
        </Badge>
      </div>

      <!-- Category Badge -->
      <div v-if="orientation.categoryName" class="absolute top-2 left-2">
        <Badge variant="outline" class="text-xs font-medium px-2 py-1 bg-white/90">
          {{ orientation.categoryName }}
        </Badge>
      </div>
    </div>

    <!-- Content Area -->
    <div class="p-4">
      <!-- Title -->
      <h3 class="font-medium text-sm mb-2 line-clamp-2 group-hover:text-primary transition-colors">
        {{ orientation.title || orientation.name || 'Untitled' }}
      </h3>

      <!-- Description -->
      <p v-if="orientation.description" class="text-xs text-muted-foreground mb-3 line-clamp-2">
        {{ orientation.description }}
      </p>

      <!-- Metadata -->
      <div class="flex items-center justify-between text-xs text-muted-foreground">
        <div class="flex items-center gap-2">
          <div v-if="orientation.updateTime" class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDate(orientation.updateTime) }}</span>
          </div>
          <span v-if="orientation.updateTime && orientation.creatorName">•</span>
          <div v-if="orientation.creatorName" class="flex items-center gap-1">
            <User class="w-3 h-3" />
            <span>{{ orientation.creatorName }}</span>
          </div>
        </div>
        <div v-if="isClickable" class="flex items-center gap-1">
          <Eye class="w-3 h-3" />
          <span>View</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Calendar, User, Eye } from 'lucide-vue-next'
import type { OrientationNavListVO } from '@/api/orientation'

interface Props {
  orientation: OrientationNavListVO
}

interface Emits {
  (e: 'click', orientation: OrientationNavListVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isClickable = computed(() => {
  return props.orientation.fileTypeList || props.orientation.attachmentList
})

const handleClick = () => {
  if (isClickable.value) {
    emit('click', props.orientation)
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
