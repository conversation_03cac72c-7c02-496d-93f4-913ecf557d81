<template>
  <div class="p-6 space-y-6">
    <h1 class="text-2xl font-bold">SuperBreadcrumb 测试页面</h1>

    <!-- 基本用法 -->
    <Card>
      <CardHeader>
        <CardTitle>基本用法 - 自动生成</CardTitle>
      </CardHeader>
      <CardContent>
        <SuperBreadcrumb />
      </CardContent>
    </Card>

    <!-- 带返回按钮 -->
    <Card>
      <CardHeader>
        <CardTitle>带返回按钮</CardTitle>
      </CardHeader>
      <CardContent>
        <SuperBreadcrumb :show-back-button="true" />
      </CardContent>
    </Card>

    <!-- 自定义标题 -->
    <Card>
      <CardHeader>
        <CardTitle>自定义当前页面标题</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <SuperBreadcrumb :current-title="customTitle" />
          <div class="flex gap-2">
            <Button @click="customTitle = '自定义标题 1'">设置标题 1</Button>
            <Button @click="customTitle = '自定义标题 2'">设置标题 2</Button>
            <Button @click="customTitle = ''">清空标题</Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 自定义面包屑项 -->
    <Card>
      <CardHeader>
        <CardTitle>自定义面包屑项</CardTitle>
      </CardHeader>
      <CardContent>
        <SuperBreadcrumb :items="customBreadcrumbs" />
      </CardContent>
    </Card>

    <!-- 限制最大项数 -->
    <Card>
      <CardHeader>
        <CardTitle>限制最大项数（maxItems=3）</CardTitle>
      </CardHeader>
      <CardContent>
        <SuperBreadcrumb :items="longBreadcrumbs" :max-items="3" />
      </CardContent>
    </Card>

    <!-- 事件监听 -->
    <Card>
      <CardHeader>
        <CardTitle>事件监听</CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <SuperBreadcrumb @breadcrumb-click="handleBreadcrumbClick" />
          <div v-if="lastClickedItem" class="p-3 bg-muted rounded">
            <p class="text-sm">最后点击的面包屑项：</p>
            <pre class="text-xs mt-1">{{ JSON.stringify(lastClickedItem, null, 2) }}</pre>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Home, BookOpen, Play, Settings } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { SuperBreadcrumb } from '@/components/common/SuperBreadcrumb'
import type { BreadcrumbItem } from '@/components/common/SuperBreadcrumb/types'

// 自定义标题
const customTitle = ref('')

// 最后点击的面包屑项
const lastClickedItem = ref<BreadcrumbItem | null>(null)

// 自定义面包屑项
const customBreadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Home',
    path: '/home',
    icon: Home
  },
  {
    title: 'Content',
    path: '/content',
    icon: BookOpen
  },
  {
    title: 'Course Detail',
    path: '/content/detail/123',
    icon: Play
  },
  {
    title: 'Settings',
    current: true,
    icon: Settings
  }
]

// 长面包屑项（用于测试省略号）
const longBreadcrumbs: BreadcrumbItem[] = [
  { title: 'Home', path: '/home', icon: Home },
  { title: 'Level 1', path: '/level1' },
  { title: 'Level 2', path: '/level1/level2' },
  { title: 'Level 3', path: '/level1/level2/level3' },
  { title: 'Level 4', path: '/level1/level2/level3/level4' },
  { title: 'Current Page', current: true }
]

// 处理面包屑点击事件
const handleBreadcrumbClick = (item: BreadcrumbItem) => {
  lastClickedItem.value = item
  console.log('面包屑点击:', item)
}
</script>
