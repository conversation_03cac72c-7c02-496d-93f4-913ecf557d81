<template>
  <div class="h-full flex flex-col">
    <!-- 标题区域 -->
    <div
      :class="cn('flex h-[52px] items-center justify-center', isCollapsed ? 'h-[52px]' : 'px-2')"
    >
      <BookOpen class="size-4" />
      <span v-if="!isCollapsed" class="ms-1">Courses</span>
    </div>

    <Separator />

    <!-- 导航菜单 -->
    <ScrollArea class="flex-1">
      <div class="p-2 space-y-1">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex items-center justify-center py-4">
          <div class="text-sm text-muted-foreground">Loading...</div>
        </div>

        <!-- 菜单项 -->
        <template v-else>
          <div
            v-for="item in menuItems"
            :key="item.id"
            :class="
              cn(
                'flex items-center gap-2 rounded-md px-2 py-2 text-sm font-medium cursor-pointer transition-colors',
                'hover:bg-accent hover:text-accent-foreground',
                activeTopicId === item.code
                  ? 'bg-accent text-accent-foreground'
                  : 'text-muted-foreground'
              )
            "
            @click="handleTopicClick(item.code)"
          >
            <!-- 图标 -->
            <div class="flex items-center justify-center w-4 h-4 text-base">
              {{ item.icon }}
            </div>

            <!-- 名称 -->
            <span v-if="!isCollapsed" class="flex-1 truncate">
              {{ item.name }}
            </span>

            <!-- 数量徽章 -->
            <div
              v-if="!isCollapsed && item.count !== undefined && item.count > 0"
              class="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-muted text-xs"
            >
              {{ item.count > 99 ? '99+' : item.count }}
            </div>
          </div>
        </template>
      </div>
    </ScrollArea>

    <!-- 底部操作区域 -->
    <div v-if="!isCollapsed" class="h-[52px] flex items-center justify-center">
      <div class="text-xs text-muted-foreground">
        {{ menuItems.length - 1 }} subjects available
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { BookOpen } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import type { CourseMenuItem } from '../hooks/useCourseMenu'

interface Props {
  isCollapsed: boolean
  menuItems: CourseMenuItem[]
  activeTopicId: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  'topic-click': [topicId: string]
}>()

// 处理主题点击
function handleTopicClick(topicId: string) {
  emit('topic-click', topicId)
}
</script>
