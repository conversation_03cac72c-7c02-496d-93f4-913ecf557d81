<template>
  <div class="h-full">
    <!-- Loading State -->
    <div
      v-if="loading"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4"
    >
      <div
        v-for="i in 12"
        :key="i"
        class="bg-white rounded-lg border overflow-hidden animate-pulse"
      >
        <div class="h-32 bg-slate-200"></div>
        <div class="p-4 space-y-3">
          <div class="h-4 bg-slate-200 rounded w-3/4"></div>
          <div class="h-3 bg-slate-200 rounded w-1/2"></div>
          <div class="h-3 bg-slate-200 rounded w-2/3"></div>
        </div>
      </div>
    </div>

    <!-- Course Grid -->
    <div
      v-else-if="courses.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4"
    >
      <CourseCard
        v-for="course in courses"
        :key="course.id"
        :course="course"
        @click="handleCourseClick"
      />
    </div>

    <!-- Empty State -->
    <div v-else class="flex items-center justify-center h-full min-h-[400px]">
      <div class="text-center text-gray-500">
        <div
          class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4 mx-auto"
        >
          <BookOpen class="w-12 h-12 text-slate-400" />
        </div>
        <h3 class="text-lg font-semibold text-slate-900 mb-2">{{ emptyTitle }}</h3>
        <p class="text-slate-500 max-w-md mb-4">
          {{ emptyDescription }}
        </p>
        <Button v-if="showClearButton" variant="outline" @click="$emit('clear-filters')">
          Clear Filters
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '@/components/ui/button'
import { BookOpen } from 'lucide-vue-next'
import CourseCard from './CourseCard.vue'
import {
  CourseRespVO,
  CourseTypeEnum,
  StudyStatusEnum,
  CourseLevelEnum,
  LanguageEnum
} from '@/api/learning/course'

// Course interface for content module
interface ContentCourse extends Partial<CourseRespVO> {
  id: string | number
  name: string
  cover?: string
  category?: string
  topicName?: string
  introduction?: string
  rating?: number
  isNew?: boolean
  isRecommend?: boolean
  enrollNumber?: number
}

interface Props {
  courses: ContentCourse[]
  loading?: boolean
  emptyTitle?: string
  emptyDescription?: string
  showClearButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  courses: () => [],
  loading: false,
  emptyTitle: 'No courses found',
  emptyDescription: 'Try adjusting your search or filter criteria',
  showClearButton: true
})

const emit = defineEmits<{
  'course-click': [course: ContentCourse]
  'clear-filters': []
}>()

const handleCourseClick = (course: ContentCourse) => {
  emit('course-click', course)
}
</script>

<style scoped>
/* Responsive grid styles are handled by Tailwind classes */
</style>
