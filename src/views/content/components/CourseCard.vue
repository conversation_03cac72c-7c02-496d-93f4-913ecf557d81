<template>
  <div
    class="group relative overflow-hidden rounded-lg border bg-card hover:shadow-lg transition-all duration-200 cursor-pointer"
    @click="handleClick"
  >
    <!-- Course Image/Icon Area -->
    <div
      :class="[
        'h-32 flex items-center justify-center text-white relative overflow-hidden',
        getCourseColor(course.category || course.topicName)
      ]"
    >
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="w-full h-full bg-gradient-to-br from-white/20 to-transparent"></div>
      </div>

      <!-- Course Image or Icon -->
      <div v-if="course.cover" class="relative z-10 w-full h-full">
        <img
          :src="course.cover"
          :alt="course.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </div>
      <div v-else class="relative z-10 text-center">
        <div class="w-12 h-12 mx-auto mb-2 rounded-lg bg-white/20 flex items-center justify-center">
          <component :is="getCourseIcon(course.category || course.topicName)" class="w-6 h-6" />
        </div>
        <div class="text-xs font-medium opacity-90">{{
          course.category || course.topicName || 'Course'
        }}</div>
      </div>

      <!-- Status and Type Badges -->
      <div class="absolute top-2 left-2 flex gap-1">
        <Badge v-if="course.isNew" variant="destructive" class="text-xs font-medium px-2 py-1">
          New
        </Badge>
        <Badge v-if="course.isRecommend" variant="secondary" class="text-xs font-medium px-2 py-1">
          Hot
        </Badge>
        <Badge
          v-if="getCourseTypeLabel(course.type)"
          :variant="course.type === CourseTypeEnum.MANDATORY ? 'default' : 'outline'"
          class="text-xs font-medium px-2 py-1"
        >
          {{ getCourseTypeLabel(course.type) }}
        </Badge>
      </div>

      <!-- Study Status Badge -->
      <div class="absolute top-2 right-2">
        <Badge
          v-if="course.studyStatus !== undefined"
          :variant="getStudyStatusVariant(course.studyStatus)"
          class="text-xs font-medium px-2 py-1"
        >
          {{ getStudyStatusLabel(course.studyStatus) }}
        </Badge>
      </div>

      <!-- Certificate & Exam Indicators -->
      <div class="absolute bottom-2 right-2 flex gap-1">
        <div
          v-if="hasCertificate(course)"
          class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center"
        >
          <Award class="w-3 h-3 text-white" />
        </div>
        <div
          v-if="hasExam(course)"
          class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"
        >
          <FileText class="w-3 h-3 text-white" />
        </div>
      </div>
    </div>

    <!-- Course Information -->
    <div class="p-4">
      <!-- Course Title -->
      <h3 class="font-medium text-sm mb-2 line-clamp-2 group-hover:text-primary transition-colors">
        {{ course.name }}
      </h3>

      <!-- Course Description -->
      <p v-if="course.introduction" class="text-xs text-muted-foreground mb-3 line-clamp-2">
        {{ course.introduction }}
      </p>

      <!-- Progress Bar (if available) -->
      <div v-if="course.progress !== undefined && course.progress > 0" class="mb-3">
        <div class="flex items-center justify-between text-xs text-muted-foreground mb-1">
          <span>Progress</span>
          <span>{{ course.progress }}%</span>
        </div>
        <div class="w-full bg-slate-200 rounded-full h-1.5">
          <div
            class="bg-primary h-1.5 rounded-full transition-all duration-300"
            :style="{ width: `${course.progress}%` }"
          ></div>
        </div>
      </div>

      <!-- Course Metadata -->
      <div class="flex items-center justify-between text-xs text-muted-foreground">
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ formatCourseDuration(course.duration) }}</span>
          </div>
          <span>•</span>
          <div class="flex items-center gap-1">
            <Target class="w-3 h-3" />
            <span>{{ getCourseLevelLabel(course.level) }}</span>
          </div>
        </div>
        <div class="flex items-center gap-1">
          <Star class="w-3 h-3 fill-yellow-400 text-yellow-400" />
          <span>{{ course.star || course.rating || 0 }}</span>
        </div>
      </div>

      <!-- Additional Stats -->
      <div class="flex items-center gap-4 text-xs text-slate-400 mt-2">
        <div class="flex items-center gap-1">
          <Users class="w-3 h-3" />
          <span>{{ course.studyCount || course.enrollNumber || 0 }}</span>
        </div>
        <div v-if="course.language" class="flex items-center gap-1">
          <Globe class="w-3 h-3" />
          <span>{{ getLanguageLabel(course.language) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import {
  Star,
  Code,
  Database,
  Palette,
  Brain,
  Globe,
  Smartphone,
  Shield,
  Clock,
  Users,
  Target,
  Award,
  FileText
} from 'lucide-vue-next'
import {
  CourseRespVO,
  CourseTypeEnum,
  StudyStatusEnum,
  CourseLevelEnum,
  LanguageEnum,
  formatCourseDuration,
  getCourseLevelLabel,
  getLanguageLabel,
  getCourseTypeLabel,
  getStudyStatusLabel,
  hasCertificate,
  hasExam
} from '@/api/learning/course'

// Course interface for content module
interface ContentCourse extends Partial<CourseRespVO> {
  id: string | number
  name: string
  cover?: string
  category?: string
  topicName?: string
  introduction?: string
  rating?: number
  isNew?: boolean
  isRecommend?: boolean
  enrollNumber?: number
}

interface Props {
  course: ContentCourse
}

const props = defineProps<Props>()

const emit = defineEmits<{
  click: [course: ContentCourse]
}>()

// Methods
const getCourseColor = (category: string) => {
  const colors = {
    'Computer Science': 'bg-gradient-to-br from-blue-500 to-blue-600',
    'AI/ML': 'bg-gradient-to-br from-purple-500 to-purple-600',
    'Full Stack': 'bg-gradient-to-br from-green-500 to-green-600',
    Frontend: 'bg-gradient-to-br from-orange-500 to-orange-600',
    Backend: 'bg-gradient-to-br from-red-500 to-red-600',
    Mobile: 'bg-gradient-to-br from-pink-500 to-pink-600',
    DevOps: 'bg-gradient-to-br from-gray-500 to-gray-600',
    'Data Science': 'bg-gradient-to-br from-indigo-500 to-indigo-600',
    'Machine Learning': 'bg-gradient-to-br from-purple-500 to-purple-600',
    'Web Development': 'bg-gradient-to-br from-green-500 to-green-600'
  }
  return colors[category as keyof typeof colors] || 'bg-gradient-to-br from-slate-500 to-slate-600'
}

const getCourseIcon = (category: string) => {
  const icons = {
    'Computer Science': Code,
    'AI/ML': Brain,
    'Full Stack': Globe,
    Frontend: Palette,
    Backend: Database,
    Mobile: Smartphone,
    DevOps: Shield,
    'Data Science': Database,
    'Machine Learning': Brain,
    'Web Development': Globe
  }
  return icons[category as keyof typeof icons] || Code
}

const getStudyStatusVariant = (status: StudyStatusEnum) => {
  switch (status) {
    case StudyStatusEnum.COMPLETED:
      return 'default'
    case StudyStatusEnum.IN_PROGRESS:
      return 'secondary'
    case StudyStatusEnum.PENDING:
    default:
      return 'outline'
  }
}

const handleClick = () => {
  emit('click', props.course)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
