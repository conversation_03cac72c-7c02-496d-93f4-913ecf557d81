<template>
  <div class="flex flex-wrap gap-x-8 gap-y-3 items-start">
    <!-- Language Filter -->
    <div class="flex items-center gap-3">
      <Label class="text-sm font-medium text-muted-foreground whitespace-nowrap">Language:</Label>
      <div class="flex flex-wrap gap-1">
        <button
          v-for="option in languageOptions"
          :key="option.value"
          @click="handleLanguageChange(option.value as LanguageEnum)"
          :class="[
            'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-colors border',
            currentFilters.language === option.value
              ? 'bg-primary text-primary-foreground border-primary shadow-sm'
              : 'bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground hover:border-muted-foreground'
          ]"
        >
          {{ option.label }}
        </button>
      </div>
    </div>

    <!-- Level Filter -->
    <div class="flex items-center gap-3">
      <Label class="text-sm font-medium text-muted-foreground whitespace-nowrap">Level:</Label>
      <div class="flex flex-wrap gap-1">
        <button
          v-for="option in levelOptions"
          :key="option.value"
          @click="handleLevelChange(option.value as CourseLevelEnum)"
          :class="[
            'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-colors border',
            currentFilters.level === option.value
              ? 'bg-primary text-primary-foreground border-primary shadow-sm'
              : 'bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground hover:border-muted-foreground'
          ]"
        >
          {{ option.label }}
        </button>
      </div>
    </div>

    <!-- Source Filter -->
    <div class="flex items-center gap-3">
      <Label class="text-sm font-medium text-muted-foreground whitespace-nowrap">Source:</Label>
      <div class="flex flex-wrap gap-1">
        <button
          v-for="option in sourceOptions"
          :key="option.value"
          @click="handleSourceChange(option.value as CourseSourceEnum)"
          :class="[
            'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-colors border',
            currentFilters.source === option.value
              ? 'bg-primary text-primary-foreground border-primary shadow-sm'
              : 'bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground hover:border-muted-foreground'
          ]"
        >
          {{ option.label }}
        </button>
      </div>
    </div>

    <!-- Duration Filter -->
    <div class="flex items-center gap-3">
      <Label class="text-sm font-medium text-muted-foreground whitespace-nowrap">Duration:</Label>
      <div class="flex flex-wrap gap-1">
        <button
          v-for="option in durationOptions"
          :key="option.value"
          @click="handleDurationChange(option.value as DurationTypeEnum)"
          :class="[
            'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-colors border',
            currentFilters.duration === option.value
              ? 'bg-primary text-primary-foreground border-primary shadow-sm'
              : 'bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground hover:border-muted-foreground'
          ]"
        >
          {{ option.label }}
        </button>
      </div>
    </div>

    <!-- Study Status Filter -->
    <div class="flex items-center gap-3">
      <Label class="text-sm font-medium text-muted-foreground whitespace-nowrap">Status:</Label>
      <div class="flex flex-wrap gap-1">
        <button
          v-for="option in studyStatusOptions"
          :key="option.value"
          @click="handleStudyStatusChange(option.value as StudyStatusEnum)"
          :class="[
            'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-colors border',
            currentFilters.studyStatus === option.value
              ? 'bg-primary text-primary-foreground border-primary shadow-sm'
              : 'bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground hover:border-muted-foreground'
          ]"
        >
          {{ option.label }}
        </button>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Label } from '@/components/ui/label'
import {
  LanguageEnum,
  CourseLevelEnum,
  CourseSourceEnum,
  DurationTypeEnum,
  StudyStatusEnum,
  CourseTypeEnum
} from '@/api/learning/course'

interface FilterOption {
  value: number | string
  label: string
}

interface CourseFilters {
  language: LanguageEnum
  level: CourseLevelEnum
  subtitle: string
  source: CourseSourceEnum
  duration: DurationTypeEnum
  studyStatus?: StudyStatusEnum
  courseType?: CourseTypeEnum
}

interface Props {
  filters: CourseFilters
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'filter-change': [filters: CourseFilters]
}>()

// Filter options using proper enums
const languageOptions: FilterOption[] = [
  { value: LanguageEnum.ENGLISH, label: 'English' },
  { value: LanguageEnum.ARABIC, label: 'Arabic' }
]

const levelOptions: FilterOption[] = [
  { value: CourseLevelEnum.SUITABLE_FOR_ALL, label: 'All Levels' },
  { value: CourseLevelEnum.BEGINNER, label: 'Beginner' },
  { value: CourseLevelEnum.INTERMEDIATE, label: 'Intermediate' },
  { value: CourseLevelEnum.ADVANCED, label: 'Advanced' }
]

const subtitleOptions: FilterOption[] = [
  { value: '', label: 'All' },
  { value: '0', label: 'Without' },
  { value: '1', label: 'With' }
]

const sourceOptions: FilterOption[] = [
  { value: CourseSourceEnum.LOCAL, label: 'Local' },
  { value: CourseSourceEnum.CLOUD, label: 'Cloud' }
]

const durationOptions: FilterOption[] = [
  { value: DurationTypeEnum.LESS_THAN_15, label: '<15min' },
  { value: DurationTypeEnum.BETWEEN_15_30, label: '15-30min' },
  { value: DurationTypeEnum.BETWEEN_30_60, label: '30-60min' },
  { value: DurationTypeEnum.MORE_THAN_60, label: '>60min' }
]

const studyStatusOptions: FilterOption[] = [
  { value: StudyStatusEnum.PENDING, label: 'Not Started' },
  { value: StudyStatusEnum.IN_PROGRESS, label: 'In Progress' },
  { value: StudyStatusEnum.COMPLETED, label: 'Completed' }
]

// Current filter state
const currentFilters = computed(() => props.filters)

// Event handlers
const handleLanguageChange = (value: LanguageEnum) => {
  emit('filter-change', {
    ...currentFilters.value,
    language: value
  })
}

const handleLevelChange = (value: CourseLevelEnum) => {
  emit('filter-change', {
    ...currentFilters.value,
    level: value
  })
}

const handleSubtitleChange = (value: string) => {
  emit('filter-change', {
    ...currentFilters.value,
    subtitle: value
  })
}

const handleSourceChange = (value: CourseSourceEnum) => {
  emit('filter-change', {
    ...currentFilters.value,
    source: value
  })
}

const handleDurationChange = (value: DurationTypeEnum) => {
  emit('filter-change', {
    ...currentFilters.value,
    duration: value
  })
}

const handleStudyStatusChange = (value: StudyStatusEnum) => {
  emit('filter-change', {
    ...currentFilters.value,
    studyStatus: value
  })
}

const handleCourseTypeChange = (value: CourseTypeEnum) => {
  emit('filter-change', {
    ...currentFilters.value,
    courseType: value
  })
}
</script>
