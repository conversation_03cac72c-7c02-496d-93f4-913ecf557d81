import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { CourseApi, type CourseTopicVO } from '@/api/course/content'

export interface CourseMenuItem {
  id: string | number
  name: string
  code: string
  href: string
  icon?: string
  count?: number
  children?: CourseMenuItem[]
}

export function useCourseMenu() {
  const route = useRoute()
  const router = useRouter()

  // 主题列表
  const topics = ref<CourseTopicVO[]>([])
  const loading = ref(false)

  // 当前选中的主题ID
  const activeTopicId = computed(() => {
    return (route.query.topicId as string) || 'all'
  })

  // 构建菜单项
  const menuItems = computed<CourseMenuItem[]>(() => {
    const items: CourseMenuItem[] = [
      {
        id: 'all',
        name: 'All Courses',
        code: 'all',
        href: '/content?topicId=all',
        icon: '📚',
        count: 0
      }
    ]

    // 添加主题菜单项
    topics.value.forEach((topic) => {
      items.push({
        id: topic.id,
        name: topic.name,
        code: topic.id.toString(),
        href: `/content?topicId=${topic.id}`,
        icon: getTopicIcon(topic.name),
        count: 0
      })
    })

    return items
  })

  // 当前激活的菜单项
  const activeMenuItem = computed(() => {
    return menuItems.value.find((item) => item.code === activeTopicId.value) || menuItems.value[0]
  })

  // 获取主题图标
  function getTopicIcon(topicName: string): string {
    const iconMap: Record<string, string> = {
      Technology: '💻',
      Business: '💼',
      Design: '🎨',
      Marketing: '📈',
      Development: '⚡',
      'Data Science': '📊',
      'AI & Machine Learning': '🤖',
      Cybersecurity: '🔒',
      'Cloud Computing': '☁️',
      'Mobile Development': '📱',
      'Web Development': '🌐',
      DevOps: '🔧',
      'Project Management': '📋',
      Leadership: '👑',
      Communication: '💬',
      Finance: '💰',
      Healthcare: '🏥',
      Education: '🎓',
      Language: '🗣️',
      Arts: '🎭'
    }

    // 尝试匹配关键词
    for (const [key, icon] of Object.entries(iconMap)) {
      if (topicName.toLowerCase().includes(key.toLowerCase())) {
        return icon
      }
    }

    return '📖' // 默认图标
  }

  // 获取主题列表
  async function fetchTopics() {
    try {
      loading.value = true
      const response = await CourseApi.topicListAll()
      topics.value = response || []
    } catch (error) {
      console.error('Failed to fetch topics:', error)
      topics.value = []
    } finally {
      loading.value = false
    }
  }

  // 切换主题
  function switchTopic(topicId: string) {
    const query = { ...route.query }

    if (topicId === 'all') {
      delete query.topicId
    } else {
      query.topicId = topicId
    }

    // 重置页码
    delete query.pageNum

    router.push({
      path: route.path,
      query
    })
  }

  // 搜索课程
  function searchCourses(searchQuery: string) {
    const query = { ...route.query }

    if (searchQuery.trim()) {
      query.search = searchQuery.trim()
    } else {
      delete query.search
    }

    // 重置页码
    delete query.pageNum

    router.push({
      path: route.path,
      query
    })
  }

  // 清除搜索
  function clearSearch() {
    const query = { ...route.query }
    delete query.search
    delete query.pageNum

    router.push({
      path: route.path,
      query
    })
  }

  // 获取当前搜索关键词
  const searchQuery = computed(() => {
    return (route.query.search as string) || ''
  })

  // 初始化
  onMounted(() => {
    fetchTopics()
  })

  return {
    // 数据
    topics,
    menuItems,
    activeMenuItem,
    activeTopicId,
    searchQuery,
    loading,

    // 方法
    fetchTopics,
    switchTopic,
    searchCourses,
    clearSearch
  }
}
