<template>
  <ContainerWrapper>
    <template #content>
      <div class="h-full flex">
        <!-- 主日历区域 -->
        <div class="flex-1 flex flex-col">
          <!-- Calendar Header -->
          <div
            class="calendar-header bg-white border-b px-4 flex items-center justify-between"
            :style="{ height: `${LAYOUT_HEIGHTS.PAGE_HEADER}px` }"
          >
            <!-- Left: Navigation Controls -->
            <div class="flex items-center gap-4">
              <!-- Today Button -->
              <Button variant="outline" @click="goToToday"> Today </Button>

              <!-- Previous/Next Navigation -->
              <div class="flex items-center gap-1">
                <Button variant="ghost" size="sm" @click="navigatePrevious">
                  <ChevronLeft class="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" @click="navigateNext">
                  <ChevronRight class="w-4 h-4" />
                </Button>
              </div>

              <!-- Current Date Display -->
              <h1 class="text-xl font-semibold text-gray-900">
                {{ currentDateDisplay }}
              </h1>
            </div>

            <!-- Right: View Toggle -->
            <div class="flex items-center gap-2">
              <!-- Legend -->
              <div class="flex items-center gap-4 mr-6">
                <div
                  v-for="legend in activeLegends"
                  :key="legend.type"
                  class="flex items-center gap-2"
                >
                  <div
                    class="w-3 h-3 rounded-full"
                    :style="{ backgroundColor: legend.color }"
                  ></div>
                  <span class="text-sm text-gray-600">{{ legend.name }}</span>
                </div>
              </div>

              <!-- View Toggle -->
              <ToggleGroup v-model="currentView" type="single" class="border rounded-lg">
                <ToggleGroupItem value="day" class="text-sm"> Day </ToggleGroupItem>
                <ToggleGroupItem value="week" class="text-sm"> Week </ToggleGroupItem>
                <ToggleGroupItem value="month" class="text-sm"> Month </ToggleGroupItem>
                <ToggleGroupItem value="year" class="text-sm"> Year </ToggleGroupItem>
              </ToggleGroup>
            </div>
          </div>

          <!-- Calendar Content with ScrollArea -->
          <ScrollArea class="flex-1">
            <!-- Day View -->
            <DayView
              v-if="currentView === 'day'"
              :current-date="currentDate"
              :activities="filteredActivitiesComputed"
              :selected-event="selectedActivity"
              @activity-click="handleActivityClick"
            />

            <!-- Week View -->
            <WeekView
              v-if="currentView === 'week'"
              :current-date="currentDate"
              :activities="filteredActivitiesComputed"
              :selected-event="selectedActivity"
              @activity-click="handleActivityClick"
            />

            <!-- Month View -->
            <MonthView
              v-if="currentView === 'month'"
              :current-date="currentDate"
              :activities="filteredActivitiesComputed"
              :selected-event="selectedActivity"
              @activity-click="handleActivityClick"
            />

            <!-- Year View -->
            <YearView
              v-if="currentView === 'year'"
              :current-date="currentDate"
              :activities="filteredActivitiesComputed"
              :selected-event="selectedActivity"
              @activity-click="handleActivityClick"
            />
          </ScrollArea>
        </div>
      </div>
    </template>
  </ContainerWrapper>

  <!-- Activity Detail Modal -->
  <ActivityDetailModal
    :open="showCourseDetail"
    @update:open="showCourseDetail = $event"
    :activity="selectedActivity"
    :user-role="userRole"
    @action="handleActivityAction"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { ScrollArea } from '@/components/ui/scroll-area'
import ContainerWrapper from '@/components/ContainerWrap/src/ContainerWrapper.vue'
import DayView from './components/DayView.vue'
import WeekView from './components/WeekView.vue'
import MonthView from './components/MonthView.vue'
import YearView from './components/YearView.vue'
import ActivityDetailModal from './components/ActivityDetailModal.vue'

import {
  CalendarApi,
  getUserRole,
  type CalendarActivity,
  type CalendarActivityBO,
  type CalendarParams,
  ActivityType,
  getActivityTypeColor,
  getActivityTypeName
} from '@/api/learning/calendar'
import { useCalendarSync } from './hooks/useCalendarSync'
import { LAYOUT_HEIGHTS } from '@/layout/config'
import dayjs, { type Dayjs } from 'dayjs'

// 导入类型定义
import { CalendarViewType } from './types/enums'

// Types are imported from API

// Router
const router = useRouter()
const loading = ref(false)

// Calendar Sync
const { syncWithRightCalendar, onRightCalendarChange, cleanup } = useCalendarSync()

// State
const currentView = ref<CalendarViewType>(CalendarViewType.WEEK)
const currentDate = ref<Dayjs>(dayjs())
const activities = ref<CalendarActivityBO[]>([])
const selectedActivity = ref<CalendarActivityBO | null>(null)
const showCourseDetail = ref(false)
const userRole = ref<'student' | 'approver'>(getUserRole())
const selectedTypes = ref<string[]>(['MLC Training', 'Live']) // 默认全选所有类型

// Computed
const currentDateDisplay = computed(() => {
  switch (currentView.value) {
    case CalendarViewType.DAY:
      return currentDate.value.format('YYYY年M月D日')
    case CalendarViewType.WEEK:
      const startOfWeek = currentDate.value.startOf('week')
      const endOfWeek = currentDate.value.endOf('week')
      if (startOfWeek.month() === endOfWeek.month()) {
        return `${startOfWeek.format('YYYY年M月')}`
      } else {
        return `${startOfWeek.format('YYYY年M月')} - ${endOfWeek.format('M月')}`
      }
    case CalendarViewType.MONTH:
      return currentDate.value.format('YYYY年M月')
    case CalendarViewType.YEAR:
      return currentDate.value.format('YYYY年')
    default:
      return currentDate.value.format('YYYY年M月')
  }
})

// 筛选后的活动列表
const filteredActivitiesComputed = computed(() => {
  if (selectedTypes.value.length === 0) {
    return activities.value
  }

  return activities.value.filter(activity => {
    const typeName = getActivityTypeName(activity.activityType)
    return selectedTypes.value.includes(typeName)
  })
})

// 动态生成当前时间范围内存在的事件类型图例
const activeLegends = computed(() => {
  // 获取当前显示的活动中的所有类型
  const activeTypes = new Set<number>()

  filteredActivitiesComputed.value.forEach(activity => {
    activeTypes.add(activity.activityType)
  })

  // 转换为图例数据
  return Array.from(activeTypes).map(type => ({
    type,
    name: getActivityTypeName(type),
    color: getActivityTypeColor(type)
  }))
})

// Methods
// API 数据获取任务列表
const getTasks = async () => {
  if (loading.value) return

  loading.value = true

  try {
    let startDate: dayjs.Dayjs
    let endDate: dayjs.Dayjs

    switch (currentView.value) {
      case CalendarViewType.DAY:
        startDate = currentDate.value.startOf('day')
        endDate = currentDate.value.endOf('day')
        break
      case CalendarViewType.WEEK:
        startDate = currentDate.value.startOf('week')
        endDate = currentDate.value.endOf('week')
        break
      case CalendarViewType.MONTH:
        startDate = currentDate.value.startOf('month').startOf('week')
        endDate = currentDate.value.endOf('month').endOf('week')
        break
      case CalendarViewType.YEAR:
        startDate = currentDate.value.startOf('year')
        endDate = currentDate.value.endOf('year')
        break
      default:
        startDate = currentDate.value.startOf('week')
        endDate = currentDate.value.endOf('week')
    }

    const params: CalendarParams = {
      startTime: startDate.format('YYYY-MM-DDTHH:mm:ss'),
      endTime: endDate.format('YYYY-MM-DDTHH:mm:ss')
    }

    const response = await CalendarApi.getCalendarInfo(params)
    console.log('API响应:', response)

    if (response) {
      // 扁平化处理activities数据
      const flattenedTasks: CalendarActivityBO[] = []

      // 遍历每个日期的数据
      response.forEach(dateItem => {
        const activities = dateItem.activities || {}
        console.log('当前日期的activities:', activities)

        // 遍历activities对象，将所有活动扁平化到一个数组中
        Object.entries(activities).forEach(([activityType, activityList]) => {
          console.log(`活动类型 ${activityType}:`, activityList)
          activityList.forEach((activity) => {
            flattenedTasks.push(activity)
          })
        })
      })

      activities.value = flattenedTasks
      console.log('扁平化后的 tasks:', activities.value)
    } else {
      activities.value = []
      console.log('没有数据，清空 tasks')
    }
  } catch (err) {
    console.error('Failed to load calendar data:', err)
    activities.value = []
  } finally {
    loading.value = false
  }
}

const goToToday = () => {
  currentDate.value = dayjs()
  syncWithRightCalendar('date-change', { date: currentDate.value })
}

const navigatePrevious = () => {
  switch (currentView.value) {
    case CalendarViewType.DAY:
      currentDate.value = currentDate.value.subtract(1, 'day')
      break
    case CalendarViewType.WEEK:
      currentDate.value = currentDate.value.subtract(1, 'week')
      break
    case CalendarViewType.MONTH:
      currentDate.value = currentDate.value.subtract(1, 'month')
      break
    case CalendarViewType.YEAR:
      currentDate.value = currentDate.value.subtract(1, 'year')
      break
  }
  syncWithRightCalendar('date-change', { date: currentDate.value })
}

const navigateNext = () => {
  switch (currentView.value) {
    case CalendarViewType.DAY:
      currentDate.value = currentDate.value.add(1, 'day')
      break
    case CalendarViewType.WEEK:
      currentDate.value = currentDate.value.add(1, 'week')
      break
    case CalendarViewType.MONTH:
      currentDate.value = currentDate.value.add(1, 'month')
      break
    case CalendarViewType.YEAR:
      currentDate.value = currentDate.value.add(1, 'year')
      break
  }
  syncWithRightCalendar('date-change', { date: currentDate.value })
}

const handleActivityClick = (activity: CalendarActivityBO) => {
  selectedActivity.value = activity
  showCourseDetail.value = true

  // 同步到全局右侧日历
  syncWithRightCalendar('activity-select', { activity })
}

// Watch for view or date changes to reload data
watch([currentView, currentDate], () => {
  getTasks()
})

// Watch for selectedTypes changes to log the update
watch(selectedTypes, (newTypes) => {
  console.log('主日历筛选类型已更新:', newTypes)
}, { immediate: true })

// Handle activity actions
const handleActivityAction = async (action: string, activity: CalendarActivity) => {
  try {
    switch (action) {
      case 'view_course':
        // 跳转到课程详情页面
        if (activity.type === ActivityType.COURSE) {
          // 跳转到学习中心的课程页面
          router.push({
            path: '/my-center',
            query: {
              tab: 'courses',
              courseId: activity.id
            }
          })
        }
        showCourseDetail.value = false
        break

      case 'book':
        // 预订功能 (如果需要保留)
        console.log('Booking activity:', activity.id)
        showCourseDetail.value = false
        break

      case 'cancel':
        // 取消预订功能 (如果需要保留)
        console.log('Cancelling booking:', activity.id)
        showCourseDetail.value = false
        break
    }
  } catch (error) {
    console.error(`Failed to ${action} activity:`, error)
  }
}

onMounted(async () => {
  await getTasks()

  // 监听右侧日历的变化
  onRightCalendarChange((event: any, data: any) => {
    switch (event) {
      case 'task-select':
        console.log('Task selected from right calendar:', data.task)
        break
      case 'date-change':
        if (data.date && !data.date.isSame(currentDate.value, 'day')) {
          currentDate.value = data.date
        }
        break
      case 'filter-change':
        // 处理筛选变化
        selectedTypes.value = data.selectedTypes
        console.log('主日历筛选已更新:', selectedTypes.value)
        break
    }
  })
})

// Watch for view or date changes to reload data
watch(
  [currentView, currentDate],
  async () => {
    await getTasks()

    // 同步视图变化到右侧日历
    syncWithRightCalendar('view-change', { view: currentView.value })
  },
  { deep: true }
)

onUnmounted(() => {
  cleanup()
})
</script>

<style scoped>
.calendar-header {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>