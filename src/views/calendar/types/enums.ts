/**
 * Calendar 系统枚举定义
 */

// 日历视图类型
export enum CalendarViewType {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year'
}

// 事件类型
export enum EventType {
  COURSE = 'course',
  ASSIGNMENT = 'assignment',
  EXAM = 'exam',
  MEETING = 'meeting',
  DEADLINE = 'deadline',
  HOLIDAY = 'holiday'
}

// 事件状态
export enum EventStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 事件优先级
export enum EventPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 右侧日历模式
export enum RightCalendarMode {
  CALENDAR_ONLY = 'calendar_only',      // 仅显示迷你日历
  TASK_LIST = 'task_list',              // 日历 + 任务列表
  EVENT_DETAIL = 'event_detail'         // 日历 + 事件详情
}

// 事件操作类型
export enum EventAction {
  START = 'start',
  COMPLETE = 'complete',
  EDIT = 'edit',
  DELETE = 'delete',
  DUPLICATE = 'duplicate',
  SHARE = 'share'
}

// 事件块状态
export enum EventBlockState {
  DEFAULT = 'default',
  SELECTED = 'selected',
  HIGHLIGHTED = 'highlighted'  // 来自右侧日历的高亮
}

// 任务视图模式
export enum TaskViewMode {
  LIST = 'list',     // 任务列表模式
  DETAIL = 'detail'  // 任务详情模式
}

// 任务排序选项
export enum TaskSortOption {
  TIME = 'time',
  PRIORITY = 'priority',
  STATUS = 'status',
  TYPE = 'type'
}

// 任务筛选选项
export enum TaskFilterOption {
  ALL = 'all',
  TODAY = 'today',
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed'
}

// 日历同步事件类型
export enum CalendarSyncEventType {
  DATE_CHANGE = 'date_change',
  EVENT_SELECT = 'event_select',
  EVENT_UPDATE = 'event_update',
  VIEW_CHANGE = 'view_change',
  FILTER_CHANGE = 'filter_change',
  NAVIGATION = 'navigation'
}

// 加载状态
export enum LoadingState {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 响应式断点
export enum BreakpointSize {
  XS = 'xs',    // < 480px
  SM = 'sm',    // 480px - 768px
  MD = 'md',    // 768px - 1024px
  LG = 'lg',    // 1024px - 1440px
  XL = 'xl'     // > 1440px
}

// 日历颜色方案
export enum CalendarColorScheme {
  LIGHT = 'light',
  DARK = 'dark',
  AUTO = 'auto'
}

// 键盘快捷键
export enum KeyboardShortcut {
  PREV_PERIOD = 'ArrowLeft',
  NEXT_PERIOD = 'ArrowRight',
  TODAY = 'KeyT',
  DAY_VIEW = 'Digit1',
  WEEK_VIEW = 'Digit2',
  MONTH_VIEW = 'Digit3',
  YEAR_VIEW = 'Digit4',
  CREATE_EVENT = 'KeyC',
  SEARCH = 'KeyF'
}

// 状态更新触发器
export enum StateUpdateTrigger {
  USER_INTERACTION = 'user_interaction',
  DATA_SYNC = 'data_sync',
  EXTERNAL_EVENT = 'external_event',
  SYSTEM_UPDATE = 'system_update'
}
