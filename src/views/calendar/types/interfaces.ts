/**
 * Calendar 系统接口定义
 */

import type { Dayjs } from 'dayjs'
import {
  CalendarViewType,
  EventType,
  EventStatus,
  EventPriority,
  RightCalendarMode,
  EventAction,
  EventBlockState,
  TaskSortOption,
  TaskFilterOption,
  CalendarSyncEventType,
  LoadingState
} from './enums'

// 基础事件接口
export interface CalendarEvent {
  id: string
  title: string
  description?: string
  startTime: string // ISO 8601
  endTime: string   // ISO 8601
  type: EventType
  status: EventStatus
  priority: EventPriority
  color?: string
  isAllDay: boolean
  isRecurring: boolean
  recurringRule?: RecurringRule
  location?: string
  attendees?: string[]
  reminders?: Reminder[]
  metadata?: Record<string, unknown>
}

// 重复规则
export interface RecurringRule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
  interval: number
  endDate?: string
  count?: number
}

// 提醒设置
export interface Reminder {
  type: 'popup' | 'email' | 'sms'
  minutesBefore: number
}

// 视图状态
export interface CalendarViewState {
  currentView: CalendarViewType
  currentDate: Dayjs
  selectedDate: Dayjs | null
  selectedEvent: CalendarEvent | null
  visibleDateRange: {
    start: Dayjs
    end: Dayjs
  }
  filters: EventFilters
  settings: CalendarSettings
}

// 事件筛选
export interface EventFilters {
  eventTypes: EventType[]
  statuses: EventStatus[]
  priorities: EventPriority[]
}

// 日历设置
export interface CalendarSettings {
  timeFormat: '12h' | '24h'
  weekStartsOn: 0 | 1 // 0=Sunday, 1=Monday
  showWeekends: boolean
  defaultView: CalendarViewType
}

// 右侧日历组件属性
export interface RightCalendarProps {
  events: CalendarEvent[]
  selectedDate: Dayjs
  selectedEvent: CalendarEvent | null
  onDateSelect: (date: Dayjs) => void
  onEventSelect: (event: CalendarEvent) => void
  onEventUpdate: (event: CalendarEvent) => void
  onEventAction: (action: EventAction, event: CalendarEvent) => void
  mode: RightCalendarMode
  showMiniCalendar: boolean
  compactMode: boolean
}

// 右侧日历状态
export interface RightCalendarState {
  mode: RightCalendarMode
  selectedDate: Dayjs
  selectedEvent: CalendarEvent | null
  taskList: CalendarEvent[]
  showBackButton: boolean
}

// 事件详情配置
export interface EventDetailConfig {
  showDescription: boolean
  showLocation: boolean
  showAttendees: boolean
  showReminders: boolean
  showRecurrence: boolean
  allowEdit: boolean
  allowDelete: boolean
}

// 日历同步事件载荷
export interface CalendarSyncEventPayload {
  [CalendarSyncEventType.DATE_CHANGE]: { date: Dayjs }
  [CalendarSyncEventType.EVENT_SELECT]: { event: CalendarEvent }
  [CalendarSyncEventType.EVENT_UPDATE]: { event: CalendarEvent }
  [CalendarSyncEventType.VIEW_CHANGE]: { view: CalendarViewType }
  [CalendarSyncEventType.FILTER_CHANGE]: { filters: EventFilters }
  [CalendarSyncEventType.NAVIGATION]: { direction: 'prev' | 'next' | 'today' }
}

// 全局状态
export interface CalendarGlobalState {
  mainCalendar: {
    currentView: CalendarViewType
    currentDate: Dayjs
    selectedEvent: CalendarEvent | null
    visibleEvents: CalendarEvent[]
    loading: boolean
  }
  rightCalendar: {
    mode: RightCalendarMode
    selectedDate: Dayjs
    selectedEvent: CalendarEvent | null
    taskList: CalendarEvent[]
    showBackButton: boolean
    compactMode: boolean
  }
  sync: {
    isMainActive: boolean
    isRightActive: boolean
    lastSyncTime: number
    syncInProgress: boolean
  }
  ui: {
    sidebarCollapsed: boolean
    rightPanelWidth: number
    showEventModal: boolean
    showEditModal: boolean
  }
}

// 视图配置
export interface DayViewConfig {
  timeSlotHeight: number // 60px per hour
  startHour: number      // 0 (00:00)
  endHour: number        // 24 (24:00)
  timeAxisWidth: number  // 60px
  showAllDay: boolean    // true
}

export interface WeekViewConfig {
  columns: number        // 7 days
  timeSlotHeight: number // 60px per hour
  dayHeaderHeight: number // 44px
  showWeekends: boolean  // true
}

export interface MonthViewConfig {
  weeksToShow: number    // 6 weeks max
  dayMinHeight: number   // 100px
  maxEventsPerDay: number // 3
  showMoreIndicator: boolean // true
}

export interface YearViewConfig {
  monthsPerRow: number   // 4
  monthsPerColumn: number // 3
  miniCalendarSize: 'small' | 'medium'
  showEventDots: boolean // true
}

// 事件块样式
export interface EventBlockStyle {
  [EventBlockState.DEFAULT]: {
    transform: string
    boxShadow: string
    borderWidth: string
    opacity: string
  }
  [EventBlockState.SELECTED]: {
    transform: string
    boxShadow: string
    borderWidth: string
    borderColor: string
    opacity: string
  }
  [EventBlockState.HIGHLIGHTED]: {
    transform: string
    boxShadow: string
    borderWidth: string
    borderColor: string
    opacity: string
  }
}

// 任务组件属性
export interface TaskListProps {
  tasks: CalendarEvent[]
  selectedDate: Dayjs
  sortBy: TaskSortOption
  filterBy: TaskFilterOption
  onTaskSelect: (task: CalendarEvent) => void
  onTaskUpdate: (task: CalendarEvent) => void
}

export interface TaskDetailProps {
  task: CalendarEvent
  onBack: () => void
  onTaskUpdate: (task: CalendarEvent) => void
  onTaskAction: (action: EventAction, task: CalendarEvent) => void
  config: EventDetailConfig
}
