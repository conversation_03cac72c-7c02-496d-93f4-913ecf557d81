/**
 * Calendar 系统常量定义
 */

import { EventType, EventStatus, EventPriority, CalendarViewType } from './enums'

// 默认配置
export const DEFAULT_CONFIG = {
  // 时间相关
  TIME_SLOT_HEIGHT: 60,        // 每小时高度 (px)
  TIME_AXIS_WIDTH: 60,         // 时间轴宽度 (px)
  DAY_HEADER_HEIGHT: 44,       // 日期头部高度 (px)
  HOUR_START: 0,               // 开始小时
  HOUR_END: 24,                // 结束小时

  // 布局相关
  SIDEBAR_WIDTH: 240,          // 侧边栏宽度 (px)
  RIGHT_PANEL_WIDTH: 320,      // 右侧面板宽度 (px)
  MIN_EVENT_HEIGHT: 20,        // 最小事件高度 (px)
  EVENT_MARGIN: 2,             // 事件间距 (px)

  // 月视图相关
  MONTH_WEEKS_MAX: 6,          // 月视图最大周数
  MONTH_DAY_MIN_HEIGHT: 100,   // 月视图日期最小高度 (px)
  MONTH_MAX_EVENTS_PER_DAY: 3, // 月视图每日最大显示事件数

  // 年视图相关
  YEAR_MONTHS_PER_ROW: 4,      // 年视图每行月数
  YEAR_MONTHS_PER_COLUMN: 3,   // 年视图每列月数

  // 动画相关
  ANIMATION_DURATION: 200,     // 默认动画时长 (ms)
  HOVER_SCALE: 1.01,          // 悬停缩放比例
  TRANSITION_EASING: 'ease-out', // 过渡缓动函数

  // 缓存相关
  CACHE_MAX_SIZE: 1000,        // 最大缓存数量
  CACHE_TTL: 300000,          // 缓存时间 (5分钟)

  // 性能相关
  VIRTUAL_SCROLL_BUFFER: 5,    // 虚拟滚动缓冲区
  DEBOUNCE_DELAY: 300,        // 防抖延迟 (ms)
  THROTTLE_DELAY: 100         // 节流延迟 (ms)
} as const

// 事件类型配置
export const EVENT_TYPE_CONFIG = {
  [EventType.COURSE]: {
    label: '课程',
    color: '#007AFF',
    icon: '📚',
    priority: EventPriority.MEDIUM
  },
  [EventType.ASSIGNMENT]: {
    label: '作业',
    color: '#FF9500',
    icon: '📝',
    priority: EventPriority.HIGH
  },
  [EventType.EXAM]: {
    label: '考试',
    color: '#FF3B30',
    icon: '📋',
    priority: EventPriority.URGENT
  },
  [EventType.MEETING]: {
    label: '会议',
    color: '#34C759',
    icon: '👥',
    priority: EventPriority.MEDIUM
  },
  [EventType.DEADLINE]: {
    label: '截止日期',
    color: '#AF52DE',
    icon: '⏰',
    priority: EventPriority.HIGH
  },
  [EventType.HOLIDAY]: {
    label: '节假日',
    color: '#FF2D92',
    icon: '🎉',
    priority: EventPriority.LOW
  }
} as const

// 事件状态配置
export const EVENT_STATUS_CONFIG = {
  [EventStatus.PENDING]: {
    label: '待处理',
    color: '#8E8E93',
    bgColor: '#F2F2F7'
  },
  [EventStatus.IN_PROGRESS]: {
    label: '进行中',
    color: '#007AFF',
    bgColor: '#E5F3FF'
  },
  [EventStatus.COMPLETED]: {
    label: '已完成',
    color: '#34C759',
    bgColor: '#E8F5E8'
  },
  [EventStatus.CANCELLED]: {
    label: '已取消',
    color: '#FF3B30',
    bgColor: '#FFE5E5'
  }
} as const

// 优先级配置
export const EVENT_PRIORITY_CONFIG = {
  [EventPriority.LOW]: {
    label: '低',
    color: '#8E8E93',
    weight: 1
  },
  [EventPriority.MEDIUM]: {
    label: '中',
    color: '#FF9500',
    weight: 2
  },
  [EventPriority.HIGH]: {
    label: '高',
    color: '#FF3B30',
    weight: 3
  },
  [EventPriority.URGENT]: {
    label: '紧急',
    color: '#AF52DE',
    weight: 4
  }
} as const

// 视图配置
export const VIEW_CONFIG = {
  [CalendarViewType.DAY]: {
    label: '日',
    shortcut: '1',
    icon: '📅'
  },
  [CalendarViewType.WEEK]: {
    label: '周',
    shortcut: '2',
    icon: '📆'
  },
  [CalendarViewType.MONTH]: {
    label: '月',
    shortcut: '3',
    icon: '🗓️'
  },
  [CalendarViewType.YEAR]: {
    label: '年',
    shortcut: '4',
    icon: '📊'
  }
} as const

// 时间格式
export const TIME_FORMATS = {
  HOUR_24: 'HH:mm',
  HOUR_12: 'h:mm A',
  DATE_SHORT: 'M/D',
  DATE_LONG: 'YYYY年M月D日',
  DATE_TIME: 'YYYY-M-D HH:mm',
  ISO_DATE: 'YYYY-MM-DD',
  ISO_DATETIME: 'YYYY-MM-DDTHH:mm:ss'
} as const

// 响应式断点
export const BREAKPOINTS = {
  XS: 480,
  SM: 768,
  MD: 1024,
  LG: 1440,
  XL: 1920
} as const

// 键盘快捷键映射
export const KEYBOARD_SHORTCUTS = {
  'ArrowLeft': 'navigatePrevious',
  'ArrowRight': 'navigateNext',
  'KeyT': 'goToToday',
  'Digit1': 'switchToDayView',
  'Digit2': 'switchToWeekView',
  'Digit3': 'switchToMonthView',
  'Digit4': 'switchToYearView',
  'KeyC': 'createEvent',
  'KeyF': 'focusSearch',
  'Escape': 'clearSelection'
} as const

// 动画配置
export const ANIMATION_CONFIG = {
  // 模式切换动画
  MODE_TRANSITION: {
    duration: 300,
    easing: 'ease-in-out',
    stagger: 50
  },

  // 事件选择动画
  EVENT_SELECTION: {
    duration: 200,
    scale: 1.02,
    shadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
  },

  // 日期切换动画
  DATE_TRANSITION: {
    duration: 250,
    direction: 'slide' as const,
    easing: 'ease-out'
  },

  // 详情面板动画
  DETAIL_PANEL: {
    slideIn: {
      duration: 300,
      from: 'right' as const,
      easing: 'ease-out'
    },
    slideOut: {
      duration: 200,
      to: 'right' as const,
      easing: 'ease-in'
    }
  }
} as const

// 错误消息
export const ERROR_MESSAGES = {
  LOAD_EVENTS_FAILED: '加载事件失败',
  SAVE_EVENT_FAILED: '保存事件失败',
  DELETE_EVENT_FAILED: '删除事件失败',
  NETWORK_ERROR: '网络连接错误',
  INVALID_DATE: '无效的日期',
  INVALID_EVENT: '无效的事件数据',
  PERMISSION_DENIED: '权限不足'
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  EVENT_CREATED: '事件创建成功',
  EVENT_UPDATED: '事件更新成功',
  EVENT_DELETED: '事件删除成功',
  EVENT_COMPLETED: '事件标记为完成',
  SYNC_SUCCESS: '同步成功'
} as const

// 默认事件数据
export const DEFAULT_EVENT = {
  title: '新事件',
  description: '',
  type: EventType.COURSE,
  status: EventStatus.PENDING,
  priority: EventPriority.MEDIUM,
  isAllDay: false,
  isRecurring: false,
  color: EVENT_TYPE_CONFIG[EventType.COURSE].color
} as const
