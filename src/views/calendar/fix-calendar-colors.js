// 修复 Calendar 颜色类名的脚本
// 将自定义 calendar 颜色类替换为标准 Tailwind 类名

const replacements = {
  // 背景色
  'bg-calendar-bg': 'bg-white',
  'bg-calendar-bg-secondary': 'bg-gray-50',
  'bg-calendar-bg-tertiary': 'bg-gray-25',
  
  // 边框色
  'border-calendar-border': 'border-gray-200',
  'border-calendar-border-light': 'border-gray-100',
  'border-calendar-border-dark': 'border-gray-300',
  
  // 文字色
  'text-calendar-text-primary': 'text-gray-900',
  'text-calendar-text-secondary': 'text-gray-600',
  'text-calendar-text-tertiary': 'text-gray-400',
  
  // 状态色
  'bg-calendar-today': 'bg-blue-500',
  'bg-calendar-selected': 'bg-blue-50',
  'bg-calendar-hover': 'hover:bg-gray-50',
  
  // 主色调
  'bg-calendar-primary': 'bg-blue-500',
  'text-calendar-primary': 'text-blue-500',
  
  // 事件色
  'bg-calendar-event-blue': 'bg-blue-500',
  'bg-calendar-event-green': 'bg-green-500',
  'bg-calendar-event-orange': 'bg-orange-500',
  'bg-calendar-event-red': 'bg-red-500',
  'bg-calendar-event-purple': 'bg-purple-500',
  'bg-calendar-event-pink': 'bg-pink-500',
  'bg-calendar-event-yellow': 'bg-yellow-500',
  'bg-calendar-event-gray': 'bg-gray-500'
}

console.log('Calendar 颜色类名替换映射：')
console.log(JSON.stringify(replacements, null, 2))

// 这个脚本提供了替换映射，需要手动应用到相关文件中
