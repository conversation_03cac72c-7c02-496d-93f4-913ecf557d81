import { ref, type Ref } from 'vue'
import { type Dayjs } from 'dayjs'

// 全局事件总线类型定义
interface CalendarSyncEvent {
  'date-change': { date: Dayjs }
  'activity-select': { activity: any }
  'event-select': { event: any }
  'task-select': { task: any }
  'task-update': { task: any }
  'event-update': { event: any }
  'view-change': { view: 'day' | 'week' | 'month' | 'year' }
  'calendar-navigate': { direction: 'prev' | 'next' | 'today' }
  'filter-change': { selectedTypes: string[] }
}

type CalendarSyncEventType = keyof CalendarSyncEvent
type CalendarSyncEventData<T extends CalendarSyncEventType> = CalendarSyncEvent[T]
type CalendarSyncCallback<T extends CalendarSyncEventType> = (
  event: T,
  data: CalendarSyncEventData<T>
) => void

// 全局事件总线
class CalendarEventBus {
  private listeners: Map<CalendarSyncEventType, Set<Function>> = new Map()

  on<T extends CalendarSyncEventType>(
    event: T,
    callback: CalendarSyncCallback<T>
  ) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(callback)

    return () => {
      this.listeners.get(event)?.delete(callback)
    }
  }

  emit<T extends CalendarSyncEventType>(
    event: T,
    data: CalendarSyncEventData<T>
  ) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(event, data)
        } catch (error) {
          console.error(`Error in calendar sync callback for ${event}:`, error)
        }
      })
    }
  }

  off(event?: CalendarSyncEventType) {
    if (event) {
      this.listeners.delete(event)
    } else {
      this.listeners.clear()
    }
  }
}

// 全局事件总线实例
const calendarEventBus = new CalendarEventBus()

// 日历同步状态
interface CalendarSyncState {
  selectedDate: Ref<Dayjs | null>
  selectedActivity: Ref<any>
  selectedTask: Ref<any>
  currentView: Ref<'day' | 'week' | 'month' | 'year'>
  selectedTypes: Ref<string[]>
}

const syncState: CalendarSyncState = {
  selectedDate: ref(null),
  selectedActivity: ref(null),
  selectedTask: ref(null),
  currentView: ref('week'),
  selectedTypes: ref([])
}

/**
 * 主日历同步 Hook (左侧学习日历)
 */
export function useCalendarSync() {
  const unsubscribers: Function[] = []

  const syncWithRightCalendar = <T extends CalendarSyncEventType>(
    event: T,
    data: CalendarSyncEventData<T>
  ) => {
    switch (event) {
      case 'date-change':
        syncState.selectedDate.value = (data as any).date
        break
      case 'activity-select':
        syncState.selectedActivity.value = (data as any).activity
        break
      case 'view-change':
        syncState.currentView.value = (data as any).view
        break
    }
    calendarEventBus.emit(event, data)
  }

  const onRightCalendarChange = <T extends CalendarSyncEventType>(
    callback: CalendarSyncCallback<T>
  ) => {
    const events: CalendarSyncEventType[] = [
      'task-select',
      'task-update',
      'date-change',
      'filter-change'
    ]

    events.forEach(event => {
      const unsubscribe = calendarEventBus.on(event as T, callback)
      unsubscribers.push(unsubscribe)
    })
  }

  const cleanup = () => {
    unsubscribers.forEach(unsubscribe => unsubscribe())
    unsubscribers.length = 0
  }

  return {
    syncWithRightCalendar,
    onRightCalendarChange,
    syncState,
    cleanup
  }
}

/**
 * 右侧日历同步 Hook
 */
export function useRightCalendarSync() {
  const unsubscribers: Function[] = []

  const syncWithMainCalendar = <T extends CalendarSyncEventType>(
    event: T,
    data: CalendarSyncEventData<T>
  ) => {
    switch (event) {
      case 'task-select':
        syncState.selectedTask.value = (data as any).task
        break
      case 'task-update':
        syncState.selectedTask.value = (data as any).task
        break
      case 'date-change':
        syncState.selectedDate.value = (data as any).date
        break
      case 'filter-change':
        syncState.selectedTypes.value = (data as any).selectedTypes
        break
    }
    calendarEventBus.emit(event, data)
  }

  const onMainCalendarChange = <T extends CalendarSyncEventType>(
    callback: CalendarSyncCallback<T>
  ) => {
    const events: CalendarSyncEventType[] = [
      'date-change',
      'activity-select',
      'event-select',
      'view-change',
      'calendar-navigate'
    ]

    events.forEach(event => {
      const unsubscribe = calendarEventBus.on(event as T, callback)
      unsubscribers.push(unsubscribe)
    })
  }

  const cleanup = () => {
    unsubscribers.forEach(unsubscribe => unsubscribe())
    unsubscribers.length = 0
  }

  return {
    syncWithMainCalendar,
    onMainCalendarChange,
    syncState,
    cleanup
  }
}

// 导出事件总线和类型
export { calendarEventBus }
export type {
  CalendarSyncEvent,
  CalendarSyncEventType,
  CalendarSyncEventData,
  CalendarSyncCallback,
  CalendarSyncState
}
