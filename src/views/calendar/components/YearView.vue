<template>
  <div class="year-view h-full overflow-auto p-6">
    <!-- 年份标题 -->
    <div class="year-header text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900">
        {{ currentDate.format('YYYY年') }}
      </h1>
      <p class="text-gray-500 mt-2"> 共 {{ totalEvents }} 个事件 </p>
    </div>

    <!-- 月份网格 -->
    <div class="year-grid grid grid-cols-4 gap-6">
      <div
        v-for="month in months"
        :key="month.month"
        class="month-card bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
        @click="navigateToMonth(month.date)"
      >
        <!-- 月份标题 -->
        <div class="month-header text-center mb-3">
          <h3 class="font-semibold text-gray-900">
            {{ month.date.format('M月') }}
          </h3>
          <div class="text-xs text-gray-500 mt-1"> {{ month.eventCount }} 个事件 </div>
        </div>

        <!-- 迷你月历 -->
        <div class="mini-month">
          <!-- 星期标题 -->
          <div class="weekdays grid grid-cols-7 gap-1 mb-2">
            <div
              v-for="day in weekdays"
              :key="day"
              class="weekday text-xs text-gray-500 text-center"
            >
              {{ day }}
            </div>
          </div>

          <!-- 日期网格 -->
          <div class="days grid grid-cols-7 gap-1">
            <button
              v-for="day in month.days"
              :key="day.format('YYYY-MM-DD')"
              class="day-cell w-6 h-6 text-xs rounded-full flex items-center justify-center transition-all"
              :class="getDayClass(day, month.date)"
              @click.stop="navigateToDay(day)"
            >
              <span>{{ day.format('D') }}</span>
              <!-- 事件指示点 -->
              <div
                v-if="getDayEvents(day).length > 0"
                class="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex gap-0.5"
              >
                <div
                  v-for="(event, index) in getDayEvents(day).slice(0, 3)"
                  :key="index"
                  class="w-1 h-1 rounded-full"
                  :style="{ backgroundColor: event.color || '#3B82F6' }"
                ></div>
              </div>
            </button>
          </div>
        </div>

        <!-- 月份事件预览 -->
        <div
          v-if="month.topEvents.length > 0"
          class="month-events mt-3 pt-3 border-t border-gray-100"
        >
          <div class="space-y-1">
            <div
              v-for="event in month.topEvents"
              :key="event.id"
              class="event-preview flex items-center gap-2 text-xs"
            >
              <div
                class="w-2 h-2 rounded-full flex-shrink-0"
                :style="{ backgroundColor: event.color || '#3B82F6' }"
              ></div>
              <span class="truncate text-gray-700">{{ event.name }}</span>
            </div>
            <div v-if="month.eventCount > 3" class="text-xs text-gray-500">
              还有 {{ month.eventCount - 3 }} 个事件...
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'
import type { CalendarActivityBO } from '@/api/learning/calendar'

interface Props {
  currentDate: Dayjs
  activities: CalendarActivityBO[]
  selectedEvent?: CalendarActivityBO | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'activity-click': [activity: CalendarActivityBO]
  'navigate-to-month': [date: Dayjs]
  'navigate-to-day': [date: Dayjs]
}>()

// 星期标题
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// 总事件数
const totalEvents = computed(() => {
  return props.activities.filter((activity) => {
    const activityDate = dayjs(activity.startTime)
    return activityDate.year() === props.currentDate.year()
  }).length
})

// 月份数据
const months = computed(() => {
  const year = props.currentDate.year()
  const monthsData = []

  for (let month = 0; month < 12; month++) {
    const monthDate = dayjs().year(year).month(month).startOf('month')
    const monthEvents = getMonthEvents(monthDate)

    monthsData.push({
      month: month + 1,
      date: monthDate,
      days: getMonthDays(monthDate),
      eventCount: monthEvents.length,
      topEvents: monthEvents.slice(0, 3)
    })
  }

  return monthsData
})

// 获取月份的所有日期
const getMonthDays = (monthDate: Dayjs) => {
  const startOfMonth = monthDate.startOf('month')
  const endOfMonth = monthDate.endOf('month')
  const startOfCalendar = startOfMonth.startOf('week')
  const endOfCalendar = endOfMonth.endOf('week')

  const days = []
  let current = startOfCalendar

  while (current.isBefore(endOfCalendar) || current.isSame(endOfCalendar, 'day')) {
    days.push(current)
    current = current.add(1, 'day')
  }

  return days
}

// 获取月份的事件
const getMonthEvents = (monthDate: Dayjs) => {
  return props.activities.filter((activity) => {
    const activityDate = dayjs(activity.startTime)
    return activityDate.isSame(monthDate, 'month')
  })
}

// 获取某天的事件
const getDayEvents = (day: Dayjs) => {
  return props.activities.filter((activity) => {
    const activityDate = dayjs(activity.startTime)
    return activityDate.isSame(day, 'day')
  })
}

// 获取日期样式类
const getDayClass = (day: Dayjs, monthDate: Dayjs) => {
  const classes = []

  // 今天
  if (day.isSame(dayjs(), 'day')) {
    classes.push('bg-blue-500 text-white font-semibold')
  }
  // 选中日期
  else if (day.isSame(props.currentDate, 'day')) {
    classes.push('bg-blue-100 text-blue-700 font-medium')
  }
  // 其他月份的日期
  else if (!day.isSame(monthDate, 'month')) {
    classes.push('text-gray-300')
  }
  // 当前月份的日期
  else {
    classes.push('text-gray-700 hover:bg-gray-100')
  }

  // 有事件的日期
  if (getDayEvents(day).length > 0) {
    classes.push('relative')
  }

  return classes
}

// 导航到月份
const navigateToMonth = (date: Dayjs) => {
  emit('navigate-to-month', date)
}

// 导航到日期
const navigateToDay = (date: Dayjs) => {
  emit('navigate-to-day', date)
}
</script>

<style scoped>
.year-view {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.month-card {
  min-height: 280px;
}

.month-card:hover {
  transform: translateY(-2px);
}

.day-cell {
  position: relative;
}

.day-cell:hover {
  transform: scale(1.1);
}

.event-preview {
  line-height: 1.2;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .year-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .year-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .month-card {
    min-height: 240px;
  }
}

@media (max-width: 480px) {
  .year-grid {
    grid-template-columns: 1fr;
  }

  .year-view {
    padding: 1rem;
  }
}

/* 滚动条样式 */
.year-view {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
}

.year-view::-webkit-scrollbar {
  width: 6px;
}

.year-view::-webkit-scrollbar-track {
  background: transparent;
}

.year-view::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

.year-view::-webkit-scrollbar-thumb:hover {
  background-color: #a0aec0;
}
</style>
