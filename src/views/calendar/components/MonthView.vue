<template>
  <div class="month-view h-full flex flex-col">
    <!-- Month Header -->
    <div class="month-header bg-white border-b">
      <div class="grid grid-cols-7">
        <div
          v-for="day in weekDayNames"
          :key="day"
          class="p-4 text-center border-r font-medium text-gray-700 bg-gray-50"
        >
          {{ day }}
        </div>
      </div>
    </div>

    <!-- Month Body -->
    <div class="month-body flex-1 grid grid-cols-7 grid-rows-6">
      <div
        v-for="day in monthDays"
        :key="day.format('YYYY-MM-DD')"
        class="day-cell border-r border-b p-2 relative overflow-hidden hover:bg-gray-50 transition-colors"
        :class="{
          'bg-gray-100 text-gray-400': !day.isSame(currentDate, 'month'),
          'cursor-pointer': getActivitiesForDay(day).length > 0
        }"
        :style="{
          backgroundColor: day.isSame(dayjs(), 'day') ? '#017B3D0D' :
                          !day.isSame(currentDate, 'month') ? '#f3f4f6' : 'transparent'
        }"
        @mouseenter="handleDayHover(day, $event)"
        @mouseleave="handleDayLeave"
      >
        <!-- Day Number -->
        <div class="day-number mb-1">
          <span
            class="text-sm font-medium"
            :class="{
              'text-gray-900': day.isSame(currentDate, 'month') && !day.isSame(dayjs(), 'day'),
              'text-gray-400': !day.isSame(currentDate, 'month')
            }"
            :style="{
              color: day.isSame(dayjs(), 'day') ? '#017B3D' : undefined
            }"
          >
            {{ day.format('D') }}
          </span>
        </div>

        <!-- macOS 风格的事件指示器 -->
        <div class="activity-indicators space-y-1">
          <!-- 显示前3个事件 -->
          <div
            v-for="activity in getActivitiesForDay(day).slice(0, 3)"
            :key="`${activity.activityName}-${activity.startTime}`"
            class="macos-month-event"
            :class="{ selected: selectedEvent?.activityName === activity.activityName && selectedEvent?.startTime === activity.startTime }"
            @click="$emit('activity-click', activity)"
          >
            <!-- 左侧颜色条 -->
            <div class="event-color-dot" :style="{ backgroundColor: getActivityTypeColor(activity.activityType) }"></div>

            <!-- 事件内容 -->
            <div class="event-content">
              <span class="event-time">{{ formatActivityTime(activity) }}</span>
              <span class="event-title">{{ activity.activityName }}</span>
            </div>
          </div>

          <!-- 更多事件指示器 -->
          <div
            v-if="getActivitiesForDay(day).length > 3"
            class="more-events-indicator"
            @click="showMoreActivities(day)"
          >
            +{{ getActivitiesForDay(day).length - 3 }} more
          </div>
        </div>
      </div>
    </div>

    <!-- More Activities Modal -->
    <Dialog :open="showMoreModal" @update:open="showMoreModal = $event">
      <DialogContent class="max-w-md">
        <DialogHeader>
          <DialogTitle> Activities for {{ selectedDay?.format('MMMM D, YYYY') }} </DialogTitle>
        </DialogHeader>

        <div class="space-y-2 max-h-96 overflow-auto">
          <div
            v-for="activity in selectedDayActivities"
            :key="`${activity.activityName}-${activity.startTime}-modal`"
            class="activity-card p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
            @click="handleActivityClick(activity)"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="font-medium">{{ activity.activityName }}</span>
              <span
                class="px-2 py-1 rounded text-xs text-white"
                :style="{ backgroundColor: getActivityTypeColor(activity.activityType) }"
              >
                {{ getActivityTypeName(activity.activityType) }}
              </span>
            </div>

            <div class="text-sm text-gray-600 space-y-1">
              <div>{{ formatActivityTime(activity) }}</div>
              <div v-if="activity.roomId">房间: {{ activity.roomId }}</div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  CalendarActivityBO,
  getActivityTypeColor,
  getActivityTypeName
} from '@/api/learning/calendar'

interface Props {
  currentDate: Dayjs
  activities: CalendarActivityBO[]
  selectedEvent?: CalendarActivityBO | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'activity-click': [activity: CalendarActivityBO]
  'activity-hover': [activity: CalendarActivityBO | null, event?: MouseEvent]
}>()

// State
const showMoreModal = ref(false)
const selectedDay = ref<Dayjs | null>(null)

// Computed
const weekDayNames = computed(() => {
  return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
})

const monthDays = computed(() => {
  const startOfMonth = props.currentDate.startOf('month')
  const endOfMonth = props.currentDate.endOf('month')
  const startOfCalendar = startOfMonth.startOf('week')
  const endOfCalendar = endOfMonth.endOf('week')

  const days: Dayjs[] = []
  let current = startOfCalendar

  while (current.isBefore(endOfCalendar) || current.isSame(endOfCalendar, 'day')) {
    days.push(current)
    current = current.add(1, 'day')
  }

  return days
})

const selectedDayActivities = computed(() => {
  if (!selectedDay.value) return []
  return getActivitiesForDay(selectedDay.value)
})

// Methods
const getActivitiesForDay = (day: Dayjs) => {
  return props.activities.filter((activity) => {
    const activityDate = dayjs(activity.startTime)
    return activityDate.isSame(day, 'day')
  })
}

const formatActivityTime = (activity: CalendarActivityBO) => {
  const start = dayjs(activity.startTime)
  return start.format('HH:mm')
}

const getActivityIndicatorStyle = (
  activity: CalendarActivityBO,
  index: number,
  totalCount: number
) => {
  const baseColor = getActivityTypeColor(activity.activityType)

  if (totalCount > 1) {
    // 创建层次感：每个后续活动稍微偏移
    const offsetX = index * 3 // 水平偏移
    const offsetY = index * 2 // 垂直偏移

    return {
      backgroundColor: baseColor,
      transform: `translate(${offsetX}px, ${offsetY}px)`,
      zIndex: 10 + index,
      marginBottom: index === totalCount - 1 ? '4px' : `${6 + offsetY}px`,
      boxShadow: `0 ${1 + index}px ${3 + index * 2}px rgba(0, 0, 0, 0.1)`,
      opacity: 1 - index * 0.05, // 后层稍微透明
      position: index > 0 ? 'relative' : 'static'
    }
  } else {
    return {
      backgroundColor: baseColor,
      marginBottom: '4px',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
    }
  }
}

const getLanguageName = (language: string) => {
  const names = {
    AR: 'Arabic',
    EN: 'English',
    CN: 'Chinese'
  }
  return names[language] || language
}

const handleDayHover = (day: Dayjs, event: MouseEvent) => {
  const activities = getActivitiesForDay(day)
  if (activities.length === 1) {
    emit('activity-hover', activities[0], event)
  } else if (activities.length > 1) {
    // For multiple activities, we could show a summary or the first activity
    emit('activity-hover', activities[0], event)
  }
}

const handleDayLeave = () => {
  emit('activity-hover', null)
}

const showMoreActivities = (day: Dayjs) => {
  selectedDay.value = day
  showMoreModal.value = true
}

const handleActivityClick = (activity: CalendarActivityBO) => {
  showMoreModal.value = false
  emit('activity-click', activity)
}
</script>

<style scoped>
@reference "tailwindcss";

.month-view {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.day-cell {
  min-height: 120px;
  max-height: 120px;
}

.activity-indicator {
  font-size: 10px;
  line-height: 1.2;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.activity-indicator:hover {
  opacity: 1 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4);
}

/* 层次感容器 */
.activity-indicators {
  min-height: 60px; /* 确保有足够空间显示层叠效果 */
}

.more-indicator:hover {
  text-decoration: underline;
}

.activity-card {
  transition: all 0.2s ease;
}

.activity-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Grid layout for consistent sizing */
.month-body {
  height: calc(100vh - 200px); /* Adjust based on header height */
}

@media (max-height: 800px) {
  .day-cell {
    min-height: 100px;
    max-height: 100px;
  }
}

@media (max-height: 700px) {
  .day-cell {
    min-height: 80px;
    max-height: 80px;
  }
}

/* macOS 风格的月视图事件样式 */
.macos-month-event {
  @apply flex items-center gap-1 px-1 py-0.5 rounded-sm cursor-pointer transition-all;
  @apply hover:bg-gray-50 hover:shadow-sm;
  @apply text-xs leading-tight;
}

.macos-month-event.selected {
  background-color: #017B3D0D;
  ring: 1px solid #017B3D33;
}

.event-color-dot {
  @apply w-2 h-2 rounded-full flex-shrink-0;
}

.event-content {
  @apply flex items-center gap-1 min-w-0 flex-1;
}

.event-time {
  @apply text-gray-600 font-medium flex-shrink-0;
}

.event-title {
  @apply text-gray-900 truncate;
}

.more-events-indicator {
  @apply text-xs text-gray-500 cursor-pointer hover:text-gray-700;
  @apply px-1 py-0.5 rounded-sm hover:bg-gray-50;
  @apply transition-colors;
}
</style>