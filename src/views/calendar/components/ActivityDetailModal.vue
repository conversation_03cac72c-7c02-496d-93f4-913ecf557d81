<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="max-w-2xl max-h-[90vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <div
            class="w-3 h-3 rounded-full"
            :style="{ backgroundColor: activity ? getActivityTypeColor(activity.activityType) : '#3b82f6' }"
          ></div>
          {{ activity?.activityName || 'Activity Details' }}
        </DialogTitle>
      </DialogHeader>

      <ScrollArea class="max-h-[70vh] pr-4">
        <div v-if="activity" class="space-y-6">
          <!-- 基本信息 -->
          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-sm font-medium text-gray-600">Start Time</label>
                <p class="text-sm">{{ formatDateTime(activity.startTime) }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-600">End Time</label>
                <p class="text-sm">{{ formatDateTime(activity.endTime) }}</p>
              </div>
            </div>

            <div v-if="activity.roomId">
              <label class="text-sm font-medium text-gray-600">Room</label>
              <p class="text-sm">{{ activity.roomId }}</p>
            </div>

            <div v-if="activity.activityType">
              <label class="text-sm font-medium text-gray-600">Type</label>
              <p class="text-sm">
                <Badge variant="secondary">
                  {{ getActivityTypeName(activity.activityType) }}
                </Badge>
              </p>
            </div>

            <div v-if="activity.status">
              <label class="text-sm font-medium text-gray-600">Status</label>
              <p class="text-sm">
                <Badge variant="secondary">
                  {{ getActivityStatusName(activity.status) }}
                </Badge>
              </p>
            </div>
          </div>

          <!-- 描述 -->
          <!-- <div v-if="activity.description">
            <label class="text-sm font-medium text-gray-600">描述</label>
            <p class="text-sm mt-1 text-gray-700">{{ activity.description }}</p>
          </div> -->

          <!-- 参与者信息 -->
          <!-- <div v-if="activity.participants && activity.participants.length > 0">
            <label class="text-sm font-medium text-gray-600">参与者</label>
            <div class="mt-2 space-y-1">
              <div
                v-for="participant in activity.participants"
                :key="participant.id"
                class="text-sm text-gray-700"
              >
                {{ participant.name }}
                <span v-if="participant.role" class="text-gray-500">
                  ({{ participant.role }})
                </span>
              </div>
            </div>
          </div> -->

          <!-- 附件 -->
          <!-- <div v-if="activity.attachments && activity.attachments.length > 0">
            <label class="text-sm font-medium text-gray-600">附件</label>
            <div class="mt-2 space-y-2">
              <div
                v-for="attachment in activity.attachments"
                :key="attachment.id"
                class="flex items-center gap-2 p-2 border rounded-md hover:bg-gray-50"
              >
                <FileText class="w-4 h-4 text-gray-500" />
                <span class="text-sm">{{ attachment.name }}</span>
                <Button variant="ghost" size="sm" @click="downloadAttachment(attachment)">
                  <Download class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div> -->
        </div>
      </ScrollArea>

      <DialogFooter class="flex justify-between">
        <Button variant="outline" @click="$emit('update:open', false)"> Cancel </Button>
        <Button class="cursor-pointer" @click="handleGoToActivity">
          <span> Go </span>
          <ArrowRight class="w-4 h-4" />
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { FileText, Download, Edit, Users, X, ArrowRight } from 'lucide-vue-next'
import type { CalendarActivityBO } from '@/api/learning/calendar'
import {
  getActivityTypeName as getApiActivityTypeName,
  CalendarActivityTypeEnum,
  formatActivityStatus,
  mapActivityStatus,
  getActivityTypeColor
} from '@/api/learning/calendar'

interface Props {
  open: boolean
  activity?: CalendarActivityBO | null
  userRole?: string
}

const props = withDefaults(defineProps<Props>(), {
  open: false,
  activity: null,
  userRole: 'user'
})

const emit = defineEmits<{
  'update:open': [value: boolean]
  action: [action: string, activity: CalendarActivityBO]
}>()

// 计算属性
const canEdit = computed(() => {
  return props.userRole === 'admin' || props.userRole === 'teacher'
})

const canJoin = computed(() => {
  return props.activity?.status === 'upcoming' || props.activity?.status === 'ongoing'
})

const canCancel = computed(() => {
  return canEdit.value && props.activity?.status !== 'completed'
})

// 路由
const router = useRouter()

// 方法
const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('MM-DD-YYYY HH:mm')
}

const getActivityTypeName = (type: CalendarActivityTypeEnum) => {
  return getApiActivityTypeName(type)
}

const getActivityStatusName = (status: number | undefined) => {
  if (status === undefined) return 'Unknown'
  const mappedStatus = mapActivityStatus(status)
  return formatActivityStatus(mappedStatus)
}

const handleAction = (action: string) => {
  if (props.activity) {
    emit('action', action, props.activity)
  }
}

// 处理跳转逻辑
const handleGoToActivity = () => {
  if (!props.activity) return

  const typeName = getApiActivityTypeName(props.activity.activityType)

  if (typeName === 'MLC Training') {
    // 跳转到MLC Training详情页面，传递roomId作为id
    router.push({
      path: '/training/detail',
      query: { id: props.activity.roomId }
    })
  } else if (typeName === 'Live') {
    // 跳转到Live详情页面，传递roomId作为id
    router.push({
      path: '/live/detail',
      query: { id: props.activity.roomId }
    })
  }

  // 关闭弹窗
  emit('update:open', false)
}

const downloadAttachment = (attachment: any) => {
  // 实现附件下载逻辑
  console.log('Download attachment:', attachment)
}
</script>
