<template>
  <div class="week-view h-full flex flex-col">
    <!-- Week Header -->
    <div class="week-header bg-white border-b">
      <div class="grid grid-cols-8 border-r">
        <!-- Time Column Header -->
        <div class="p-4 border-r bg-gray-50">
          <span class="text-sm font-medium text-gray-500">Time</span>
        </div>

        <!-- Day Headers -->
        <div
          v-for="day in weekDays"
          :key="day.format('YYYY-MM-DD')"
          class="p-4 text-center border-r"
          :style="{ backgroundColor: day.isSame(dayjs(), 'day') ? '#017B3D0D' : 'transparent' }"
        >
          <div class="text-sm font-medium text-gray-900">
            {{ day.format('ddd') }}
          </div>
          <div
            class="text-lg font-semibold mt-1"
            :style="{ color: day.isSame(dayjs(), 'day') ? '#017B3D' : '#374151' }"
          >
            {{ day.format('DD') }}
          </div>
        </div>
      </div>
    </div>

    <!-- Week Body -->
    <div class="week-body flex-1">
      <div class="grid grid-cols-8 min-h-full">
        <!-- Time Column -->
        <div class="time-column border-r bg-gray-50">
          <div
            v-for="hour in hours"
            :key="hour"
            class="time-slot h-16 border-b border-gray-200 p-2 text-right"
          >
            <span class="text-xs text-gray-500">
              {{ formatHour(hour) }}
            </span>
          </div>
        </div>

        <!-- Day Columns -->
        <div
          v-for="day in weekDays"
          :key="day.format('YYYY-MM-DD')"
          class="day-column border-r relative"
        >
          <!-- Hour Slots -->
          <div
            v-for="hour in hours"
            :key="hour"
            class="hour-slot h-16 border-b border-gray-100 relative"
            @mouseenter="handleSlotHover(day, hour, $event)"
            @mouseleave="handleSlotLeave"
          >
          </div>

          <!-- macOS 风格的事件块 -->
          <div class="absolute inset-0 pointer-events-none">
            <MacOSEventBlock
              v-for="(activity, index) in getActivitiesForDay(day)"
              :key="`${activity.activityName}-${activity.startTime}-${index}`"
              :event="activity"
              :is-selected="selectedEvent?.activityName === activity.activityName && selectedEvent?.startTime === activity.startTime"
              :has-overlap="getOverlappingEvents(activity, getActivitiesForDay(day)).length > 0"
              :overlap-index="index"
              :duration="getEventDuration(activity)"
              :height="getEventHeight(activity)"
              :style="getEventPosition(activity, getActivitiesForDay(day))"
              class="pointer-events-auto"
              @click="$emit('activity-click', activity)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'

import { CalendarActivityBO } from '@/api/learning/calendar'
import MacOSEventBlock from './MacOSEventBlock.vue'

interface Props {
  currentDate: Dayjs
  activities: CalendarActivityBO[]
  selectedEvent?: CalendarActivityBO | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'activity-click': [activity: CalendarActivityBO]
  'activity-hover': [activity: CalendarActivityBO | null, event?: MouseEvent]
}>()

// Computed
const weekDays = computed(() => {
  const startOfWeek = props.currentDate.startOf('week')
  return Array.from({ length: 7 }, (_, i) => startOfWeek.add(i, 'day'))
})

const hours = computed(() => {
  return Array.from({ length: 24 }, (_, i) => i)
})

// Methods
const formatHour = (hour: number) => {
  return dayjs().hour(hour).minute(0).format('HH:mm')
}

// 缓存每天的活动，避免重复计算
const activitiesByDay = computed(() => {
  const result = new Map<string, CalendarActivityBO[]>()

  weekDays.value.forEach(day => {
    const dayKey = day.format('YYYY-MM-DD')
    const dayActivities = props.activities.filter((activity) => {
      const activityStart = dayjs(activity.startTime)
      return activityStart.isSame(day, 'day')
    })

    // 按开始时间排序，确保事件按时间顺序显示
    const sortedActivities = dayActivities.sort((a, b) => {
      return dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
    })

    result.set(dayKey, sortedActivities)
  })

  return result
})

const getActivitiesForDay = (day: Dayjs) => {
  const dayKey = day.format('YYYY-MM-DD')
  return activitiesByDay.value.get(dayKey) || []
}

// macOS 风格的事件计算方法
const getEventDuration = (activity: CalendarActivityBO) => {
  const start = dayjs(activity.startTime)
  const end = activity.endTime ? dayjs(activity.endTime) : start.add(1, 'hour')
  return end.diff(start, 'minute')
}

const getEventHeight = (activity: CalendarActivityBO) => {
  const duration = getEventDuration(activity)
  const hourHeight = 64 // 每小时 64px
  const height = Math.max((duration / 60) * hourHeight, 18) // 最小高度 18px
  return Math.round(height)
}

// 检查两个事件是否在时间上重叠
const eventsOverlap = (event1: CalendarActivityBO, event2: CalendarActivityBO) => {
  const start1 = dayjs(event1.startTime)
  const end1 = event1.endTime ? dayjs(event1.endTime) : start1.add(1, 'hour')
  const start2 = dayjs(event2.startTime)
  const end2 = event2.endTime ? dayjs(event2.endTime) : start2.add(1, 'hour')

  return start1.isBefore(end2) && start2.isBefore(end1)
}



// 获取与当前事件重叠的事件
const getOverlappingEvents = (
  currentEvent: CalendarActivityBO,
  dayActivities: CalendarActivityBO[]
) => {
  return dayActivities.filter(
    (event) => event.activityName !== currentEvent.activityName && eventsOverlap(currentEvent, event)
  )
}

// 将重叠事件分组为连续的时间组
const groupOverlappingEventsByTime = (dayActivities: CalendarActivityBO[]) => {
  // 按开始时间排序所有事件
  const sortedEvents = [...dayActivities].sort((a, b) =>
    dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
  )

  const groups: CalendarActivityBO[][] = []
  const processedEvents = new Set<string>()

  for (const event of sortedEvents) {
    const eventKey = `${event.activityName}-${event.startTime}`

    // 如果已经处理过这个事件，跳过
    if (processedEvents.has(eventKey)) {
      continue
    }

    // 找到与当前事件重叠的所有事件
    const overlappingEvents = getOverlappingEvents(event, dayActivities)

    if (overlappingEvents.length === 0) {
      // 没有重叠，单独成组
      groups.push([event])
      processedEvents.add(eventKey)
      continue
    }

    // 创建包含当前事件和所有重叠事件的组
    const allEventsInGroup = [event, ...overlappingEvents]

    // 按时间排序
    const sortedGroup = allEventsInGroup.sort((a, b) =>
      dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
    )

    // 去重
    const uniqueGroup = sortedGroup.filter((event, index, arr) =>
      arr.findIndex(e => e.activityName === event.activityName && e.startTime === event.startTime) === index
    )

    groups.push(uniqueGroup)

    // 标记所有事件为已处理
    uniqueGroup.forEach(e => {
      processedEvents.add(`${e.activityName}-${e.startTime}`)
    })
  }

  return groups
}

// 将重叠事件组分解为连续的时间子组
const splitEventGroupByTime = (eventGroup: CalendarActivityBO[]) => {
  if (eventGroup.length <= 1) return [eventGroup]

  // 按时间排序
  const sortedEvents = [...eventGroup].sort((a, b) =>
    dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
  )

  const subGroups: CalendarActivityBO[][] = []
  let currentSubGroup = [sortedEvents[0]]

  for (let i = 1; i < sortedEvents.length; i++) {
    const current = sortedEvents[i - 1]
    const next = sortedEvents[i]

    const start1 = dayjs(current.startTime)
    const start2 = dayjs(next.startTime)
    const timeDiff = Math.abs(start1.diff(start2, 'minute'))

    if (timeDiff < 30) {
      // 时间差小于30分钟，加入当前子组
      currentSubGroup.push(next)
    } else {
      // 时间差大于等于30分钟，开始新的子组
      subGroups.push(currentSubGroup)
      currentSubGroup = [next]
    }
  }

  // 添加最后一个子组
  subGroups.push(currentSubGroup)

  return subGroups
}

// 获取事件组（用于平分网格布局）
const getEventGroup = (
  currentEvent: CalendarActivityBO,
  dayActivities: CalendarActivityBO[]
) => {
  // 获取所有事件组
  const allGroups = groupOverlappingEventsByTime(dayActivities)

  // 找到包含当前事件的组
  const currentGroup = allGroups.find(group =>
    group.some(event =>
      event.activityName === currentEvent.activityName &&
      event.startTime === currentEvent.startTime
    )
  )

  if (!currentGroup) {
    return { events: [currentEvent], useGridLayout: false }
  }

  // 将重叠组分解为时间连续的子组
  const subGroups = splitEventGroupByTime(currentGroup)

  // 找到包含当前事件的子组
  const currentSubGroup = subGroups.find(subGroup =>
    subGroup.some(event =>
      event.activityName === currentEvent.activityName &&
      event.startTime === currentEvent.startTime
    )
  )

  if (!currentSubGroup) {
    return { events: [currentEvent], useGridLayout: false }
  }

  // 如果子组有多个事件，使用网格布局；否则使用层级布局
  const useGridLayout = currentSubGroup.length > 1



  return {
    events: currentSubGroup,
    useGridLayout
  }
}

const getEventPosition = (
  activity: CalendarActivityBO,
  dayActivities: CalendarActivityBO[]
) => {
  const start = dayjs(activity.startTime)
  const startMinutes = start.hour() * 60 + start.minute()
  const hourHeight = 64 // 每小时 64px
  const top = (startMinutes / 60) * hourHeight

  // 获取事件组信息
  const eventGroup = getEventGroup(activity, dayActivities)
  const { events, useGridLayout } = eventGroup

  let left: string | number = 0 // 基础左边距
  let width = '100%' // 默认宽度，充满网格
  let zIndex = 20 // 基础z-index

  if (events.length > 1) {
    const currentIndex = events.findIndex((event) =>
      event.activityName === activity.activityName && event.startTime === activity.startTime
    )

    if (useGridLayout) {
      // 平分网格布局：开始时间相差不足30分钟
      const totalEvents = events.length
      const eventWidthPercent = 100 / totalEvents // 平分宽度
      const leftOffsetPercent = currentIndex * eventWidthPercent // 当前事件的偏移

      left = `${leftOffsetPercent}%`
      width = `${eventWidthPercent}%`
      zIndex = 20 // 同一层级

    } else {
      // 层级覆盖布局：开始时间相差超过30分钟，充满网格宽度
      left = currentIndex * 8 // 每个重叠事件向右偏移 8px
      width = '100%' // 充满网格宽度
      zIndex = 20 + (currentIndex + 1) * 10 // 不同层级
    }
  }

  return {
    top: `${top}px`,
    left: typeof left === 'number' ? `${left}px` : left,
    width: width,
    zIndex: zIndex
  }
}

// 处理时间槽悬停
const handleSlotHover = (_day: Dayjs, _hour: number, event: MouseEvent) => {
  // 可以在这里添加悬停逻辑，比如显示创建事件的提示
  emit('activity-hover', null, event)
}

// 处理时间槽离开
const handleSlotLeave = () => {
  emit('activity-hover', null)
}
</script>

<style scoped>
.week-view {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.time-column {
  min-width: 80px;
}

.day-column {
  min-width: 120px;
}

.hour-slot {
  position: relative;
}

.activity-block {
  font-size: 11px;
  line-height: 1.2;
  backdrop-filter: blur(1px);
}

.activity-block:hover {
  transform: translateY(-1px) scale(1.01); /* 减少缩放和位移 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
  z-index: 100 !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

/* 层次感动画 */
.activity-block {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 多层活动的特殊效果 */
.activity-block.layered {
  position: relative;
}

.activity-block.layered::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-radius: inherit;
  z-index: -1;
}
</style>