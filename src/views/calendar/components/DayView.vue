<template>
  <div class="day-view h-full flex flex-col">
    <!-- 日期标题 -->
    <div class="day-header bg-white border-b border-gray-200 px-4 py-3">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-lg font-semibold text-gray-900">
            {{ currentDate.format('YYYY年M月D日') }}
          </h2>
          <p class="text-sm text-gray-500">
            {{ currentDate.format('dddd') }}
          </p>
        </div>
        <div class="text-sm text-gray-500"> {{ activities.length }} 个事件 </div>
      </div>
    </div>

    <!-- 全天事件区域 -->
    <div
      v-if="allDayEvents.length > 0"
      class="all-day-section bg-gray-50 border-b border-gray-200 p-4"
    >
      <h3 class="text-sm font-medium text-gray-700 mb-2">全天</h3>
      <div class="space-y-1">
        <div
          v-for="event in allDayEvents"
          :key="`${event.activityName}-${event.startTime}-allday`"
          class="all-day-event px-3 py-2 rounded-md cursor-pointer transition-all"
          :style="{
            backgroundColor: getActivityTypeColor(event.activityType) + '20',
            borderLeft: `4px solid ${getActivityTypeColor(event.activityType)}`
          }"
          :class="{ 'ring-2 ring-blue-500': selectedEvent?.activityName === event.activityName && selectedEvent?.startTime === event.startTime }"
          @click="$emit('activity-click', event)"
        >
          <div class="font-medium text-sm">{{ event.activityName }}</div>
          <div class="text-xs text-gray-600 mt-1">
            {{ getActivityTypeName(event.activityType) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 时间轴区域 -->
    <div class="time-grid flex-1 overflow-auto">
      <div class="flex">
        <!-- 时间轴 -->
        <div class="time-axis w-16 bg-gray-50 border-r border-gray-200">
          <div
            v-for="hour in hours"
            :key="hour"
            class="hour-slot h-16 border-b border-gray-100 flex items-start justify-end pr-2 pt-1"
          >
            <span class="text-xs text-gray-500">
              {{ formatHour(hour) }}
            </span>
          </div>
        </div>

        <!-- 事件区域 -->
        <div class="event-area flex-1 relative overflow-hidden">
          <!-- 时间网格线 -->
          <div
            v-for="hour in hours"
            :key="`grid-${hour}`"
            class="hour-line h-16 border-b border-gray-100"
          ></div>

          <!-- 当前时间指示线 -->
          <div
            v-if="isToday"
            class="current-time-line absolute left-0 right-0 z-10"
            :style="{ top: currentTimePosition + 'px' }"
          >
            <div class="flex items-center">
              <div class="w-2 h-2 bg-red-500 rounded-full"></div>
              <div class="flex-1 h-0.5 bg-red-500"></div>
            </div>
          </div>

          <!-- macOS 风格的事件块 -->
          <div class="absolute inset-0 pointer-events-none">
            <MacOSEventBlock
              v-for="(event, index) in timedEvents"
              :key="`${event.activityName}-${event.startTime}-${index}`"
              :event="event"
              :is-selected="selectedEvent?.activityName === event.activityName && selectedEvent?.startTime === event.startTime"
              :has-overlap="getOverlappingEvents(event, timedEvents).length > 0"
              :overlap-index="index"
              :duration="getEventDuration(event)"
              :height="getEventHeight(event)"
              :style="getEventPosition(event, timedEvents)"
              class="pointer-events-auto"
              @click="$emit('activity-click', event)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import dayjs, { type Dayjs } from 'dayjs'
import type { CalendarActivityBO } from '@/api/learning/calendar'
import { getActivityTypeColor, getActivityTypeName } from '@/api/learning/calendar'
import MacOSEventBlock from './MacOSEventBlock.vue'

interface Props {
  currentDate: Dayjs
  activities: CalendarActivityBO[]
  selectedEvent?: CalendarActivityBO | null
}

const props = defineProps<Props>()

defineEmits<{
  'activity-click': [activity: CalendarActivityBO]
}>()

// 当前时间位置（用于今日指示线）
const currentTimePosition = ref(0)

// 小时数组 (0-23)
const hours = computed(() => {
  return Array.from({ length: 24 }, (_, i) => i)
})

// 是否是今天
const isToday = computed(() => {
  return props.currentDate.isSame(dayjs(), 'day')
})


// 全天事件
const allDayEvents = computed(() => {
  return props.activities.filter((activity) => {
    const activityDate = dayjs(activity.startTime)
    const isAllDay = dayjs(activity.endTime).diff(dayjs(activity.startTime), 'hour') >= 24
    return activityDate.isSame(props.currentDate, 'day') && isAllDay
  })
})

// 定时事件
const timedEvents = computed(() => {
  const filtered = props.activities.filter((activity) => {
    const activityDate = dayjs(activity.startTime)
    const endTime = activity.endTime ? dayjs(activity.endTime) : dayjs(activity.startTime).add(1, 'hour')
    const isAllDay = endTime.diff(dayjs(activity.startTime), 'hour') >= 24
    return activityDate.isSame(props.currentDate, 'day') && !isAllDay
  })

  // 按开始时间排序
  return filtered.sort((a, b) => {
    return dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
  })
})

// 格式化小时
const formatHour = (hour: number) => {
  if (hour === 0) return '12 AM'
  if (hour < 12) return `${hour} AM`
  if (hour === 12) return '12 PM'
  return `${hour - 12} PM`
}

// macOS 风格的事件计算方法
const getEventDuration = (event: CalendarActivityBO) => {
  const start = dayjs(event.startTime)
  const end = event.endTime ? dayjs(event.endTime) : start.add(1, 'hour') // 默认1小时
  return end.diff(start, 'minute')
}

const getEventHeight = (event: CalendarActivityBO) => {
  const duration = getEventDuration(event)
  const hourHeight = 64 // 每小时 64px
  const height = Math.max((duration / 60) * hourHeight, 18) // 最小高度 18px
  return Math.round(height)
}

// 检查两个事件是否在时间上重叠
const eventsOverlap = (event1: CalendarActivityBO, event2: CalendarActivityBO) => {
  const start1 = dayjs(event1.startTime)
  const end1 = event1.endTime ? dayjs(event1.endTime) : start1.add(1, 'hour')
  const start2 = dayjs(event2.startTime)
  const end2 = event2.endTime ? dayjs(event2.endTime) : start2.add(1, 'hour')

  return start1.isBefore(end2) && start2.isBefore(end1)
}



// 获取与当前事件重叠的事件
const getOverlappingEvents = (currentEvent: CalendarActivityBO, dayEvents: CalendarActivityBO[]) => {
  return dayEvents.filter(
    (event) => event.activityName !== currentEvent.activityName && eventsOverlap(currentEvent, event)
  )
}

// 将重叠事件分组为连续的时间组
const groupOverlappingEventsByTime = (dayEvents: CalendarActivityBO[]) => {
  // 按开始时间排序所有事件
  const sortedEvents = [...dayEvents].sort((a, b) =>
    dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
  )

  const groups: CalendarActivityBO[][] = []
  const processedEvents = new Set<string>()

  for (const event of sortedEvents) {
    const eventKey = `${event.activityName}-${event.startTime}`

    // 如果已经处理过这个事件，跳过
    if (processedEvents.has(eventKey)) {
      continue
    }

    // 找到与当前事件重叠的所有事件
    const overlappingEvents = getOverlappingEvents(event, dayEvents)

    if (overlappingEvents.length === 0) {
      // 没有重叠，单独成组
      groups.push([event])
      processedEvents.add(eventKey)
      continue
    }

    // 创建包含当前事件和所有重叠事件的组
    const allEventsInGroup = [event, ...overlappingEvents]

    // 按时间排序
    const sortedGroup = allEventsInGroup.sort((a, b) =>
      dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
    )

    // 去重
    const uniqueGroup = sortedGroup.filter((event, index, arr) =>
      arr.findIndex(e => e.activityName === event.activityName && e.startTime === event.startTime) === index
    )

    groups.push(uniqueGroup)

    // 标记所有事件为已处理
    uniqueGroup.forEach(e => {
      processedEvents.add(`${e.activityName}-${e.startTime}`)
    })
  }

  return groups
}

// 将重叠事件组分解为连续的时间子组
const splitEventGroupByTime = (eventGroup: CalendarActivityBO[]) => {
  if (eventGroup.length <= 1) return [eventGroup]

  // 按时间排序
  const sortedEvents = [...eventGroup].sort((a, b) =>
    dayjs(a.startTime).valueOf() - dayjs(b.startTime).valueOf()
  )

  const subGroups: CalendarActivityBO[][] = []
  let currentSubGroup = [sortedEvents[0]]

  for (let i = 1; i < sortedEvents.length; i++) {
    const current = sortedEvents[i - 1]
    const next = sortedEvents[i]

    const start1 = dayjs(current.startTime)
    const start2 = dayjs(next.startTime)
    const timeDiff = Math.abs(start1.diff(start2, 'minute'))

    if (timeDiff < 30) {
      // 时间差小于30分钟，加入当前子组
      currentSubGroup.push(next)
    } else {
      // 时间差大于等于30分钟，开始新的子组
      subGroups.push(currentSubGroup)
      currentSubGroup = [next]
    }
  }

  // 添加最后一个子组
  subGroups.push(currentSubGroup)

  return subGroups
}

// 获取事件组（用于平分网格布局）
const getEventGroup = (
  currentEvent: CalendarActivityBO,
  dayEvents: CalendarActivityBO[]
) => {
  // 获取所有事件组
  const allGroups = groupOverlappingEventsByTime(dayEvents)

  // 找到包含当前事件的组
  const currentGroup = allGroups.find(group =>
    group.some(event =>
      event.activityName === currentEvent.activityName &&
      event.startTime === currentEvent.startTime
    )
  )

  if (!currentGroup) {
    return { events: [currentEvent], useGridLayout: false }
  }

  // 将重叠组分解为时间连续的子组
  const subGroups = splitEventGroupByTime(currentGroup)

  // 找到包含当前事件的子组
  const currentSubGroup = subGroups.find(subGroup =>
    subGroup.some(event =>
      event.activityName === currentEvent.activityName &&
      event.startTime === currentEvent.startTime
    )
  )

  if (!currentSubGroup) {
    return { events: [currentEvent], useGridLayout: false }
  }

  // 如果子组有多个事件，使用网格布局；否则使用层级布局
  const useGridLayout = currentSubGroup.length > 1

  return {
    events: currentSubGroup,
    useGridLayout
  }
}

const getEventPosition = (
  event: CalendarActivityBO,
  dayEvents: CalendarActivityBO[]
) => {
  const start = dayjs(event.startTime)
  const startMinutes = start.hour() * 60 + start.minute()
  const hourHeight = 64 // 每小时 64px
  const top = (startMinutes / 60) * hourHeight

  // 获取事件组信息
  const eventGroup = getEventGroup(event, dayEvents)
  const { events, useGridLayout } = eventGroup

  let left: string | number = 0 // 基础左边距
  let width = '100%' // 默认宽度，充满网格
  let zIndex = 20 // 基础z-index

  if (events.length > 1) {
    const currentIndex = events.findIndex((e) =>
      e.activityName === event.activityName && e.startTime === event.startTime
    )

    if (useGridLayout) {
      // 平分网格布局：开始时间相差不足30分钟
      const totalEvents = events.length
      const eventWidthPercent = 100 / totalEvents // 平分宽度
      const leftOffsetPercent = currentIndex * eventWidthPercent // 当前事件的偏移

      left = `${leftOffsetPercent}%`
      width = `${eventWidthPercent}%`
      zIndex = 20 // 同一层级

    } else {
      // 层级覆盖布局：开始时间相差超过30分钟，充满网格宽度
      left = currentIndex * 8 // 每个重叠事件向右偏移 8px
      width = '100%' // 充满网格宽度
      zIndex = 20 + (currentIndex + 1) * 10 // 不同层级
    }
  }

  return {
    top: `${top}px`,
    left: typeof left === 'number' ? `${left}px` : left,
    width: width,
    zIndex: zIndex
  }
}

// 更新当前时间位置
const updateCurrentTimePosition = () => {
  if (isToday.value) {
    const now = dayjs()
    const minutes = now.hour() * 60 + now.minute()
    currentTimePosition.value = (minutes / 60) * 64 // 64px per hour
  }
}

// 定时器
let timeUpdateInterval: number | null = null

onMounted(() => {
  updateCurrentTimePosition()

  // 每分钟更新一次当前时间位置
  timeUpdateInterval = window.setInterval(updateCurrentTimePosition, 60000)
})

onUnmounted(() => {
  if (timeUpdateInterval) {
    clearInterval(timeUpdateInterval)
  }
})
</script>

<style scoped>
.day-view {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.hour-slot {
  position: relative;
}

.event-block {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.event-block:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.all-day-event {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.all-day-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.current-time-line {
  pointer-events: none;
}

/* 滚动条样式 */
.time-grid {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
}

.time-grid::-webkit-scrollbar {
  width: 6px;
}

.time-grid::-webkit-scrollbar-track {
  background: transparent;
}

.time-grid::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

.time-grid::-webkit-scrollbar-thumb:hover {
  background-color: #a0aec0;
}
</style>