<template>
  <div
    class="macos-event-block"
    :class="[
      eventSizeClass,
      {
        selected: isSelected,
        overlapping: hasOverlap
      }
    ]"
    :style="{ height: `${props.height}px`, minHeight: '18px' }"
    @click="$emit('click', event)"
  >
    <!-- 左侧颜色条 -->
    <div class="event-color-bar" :style="{ backgroundColor: getActivityTypeColor(event.activityType) }"></div>

    <!-- 事件内容 -->
    <div class="event-content">
      <!-- 事件标题 -->
      <div class="event-title">
        {{ event.activityName }}
      </div>

      <!-- 事件时间 (仅在有足够空间时显示) -->
      <div v-if="showTime" class="event-time">
        {{ formatEventTime(event) }}
      </div>

      <!-- 事件类型 (仅在有足够空间时显示) -->
      <div v-if="showLocation" class="event-location">
        {{ getActivityTypeName(event.activityType) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import dayjs from 'dayjs'
import type { CalendarActivityBO } from '@/api/learning/calendar'
import { getActivityTypeColor, getActivityTypeName } from '@/api/learning/calendar'

interface Props {
  event: CalendarActivityBO
  isSelected?: boolean
  hasOverlap?: boolean
  overlapIndex?: number
  duration?: number // 事件持续时间（分钟）
  height?: number // 事件块高度（像素）
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  hasOverlap: false,
  overlapIndex: 0,
  duration: 60,
  height: 60
})

defineEmits<{
  click: [event: CalendarActivityBO]
}>()

// 根据持续时间和高度决定显示内容
const eventSizeClass = computed(() => {
  if (props.height < 20) return 'size-xs'
  if (props.height < 40) return 'size-sm'
  if (props.height < 60) return 'size-md'
  return 'size-lg'
})

// 是否显示时间
const showTime = computed(() => {
  return props.height >= 40 && props.duration >= 30
})

// 是否显示位置
const showLocation = computed(() => {
  return props.height >= 60 && props.duration >= 60
})



// 格式化事件时间
const formatEventTime = (event: CalendarActivityBO) => {
  const start = dayjs(event.startTime)
  const end = event.endTime ? dayjs(event.endTime) : null

  if (end && start.isSame(end, 'day')) {
    return `${start.format('HH:mm')} - ${end.format('HH:mm')}`
  } else {
    return start.format('HH:mm')
  }
}
</script>

<style scoped>
@reference "tailwindcss";

.macos-event-block {
  @apply absolute flex rounded-md cursor-pointer transition-all duration-150;
  @apply bg-white border border-gray-200;
  @apply hover:shadow-md hover:border-gray-300;
  @apply overflow-hidden;
  @apply max-w-full; /* 确保不超出容器宽度 */

  /* macOS 风格的阴影 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.macos-event-block:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.macos-event-block.selected {
  @apply ring-2 ring-blue-500/50;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.macos-event-block.overlapping {
  @apply border-gray-300;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
}

/* 左侧颜色条 */
.event-color-bar {
  @apply w-1 flex-shrink-0;
  border-radius: 6px 0 0 6px;
}

/* 事件内容 */
.event-content {
  @apply flex-1 px-2 py-1 min-w-0 space-y-1;
}

.event-title {
  @apply font-medium text-gray-900 truncate leading-tight;
}

.event-time {
  @apply text-gray-600 truncate leading-tight;
}

.event-location {
  @apply text-gray-500 truncate leading-tight;
}

/* 重叠指示器 */
.overlap-indicator {
  @apply absolute -top-1 -right-1 w-4 h-4;
  @apply bg-gray-600 text-white text-xs;
  @apply rounded-full flex items-center justify-center;
  @apply font-bold leading-none;
}

/* 不同尺寸的样式 */
.size-xs .event-title {
  @apply text-xs;
}

.size-xs .event-content {
  @apply px-1 py-0.5;
}

.size-sm .event-title {
  @apply text-xs;
}

.size-sm .event-time {
  @apply text-xs;
}

.size-sm .event-content {
  @apply px-2 py-0.5;
}

.size-md .event-title {
  @apply text-sm;
}

.size-md .event-time {
  @apply text-xs;
}

.size-lg .event-title {
  @apply text-sm;
}

.size-lg .event-time {
  @apply text-xs;
}

.size-lg .event-location {
  @apply text-xs;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .event-content {
    @apply px-1;
  }

  .event-title {
    @apply text-xs;
  }

  .event-time {
    @apply text-xs;
  }
}
</style>
