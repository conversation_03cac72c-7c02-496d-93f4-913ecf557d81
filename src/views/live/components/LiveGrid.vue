<template>
  <div class="h-full">
    <!-- Loading State -->
    <div
      v-if="loading"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4"
    >
      <div
        v-for="i in 12"
        :key="i"
        class="bg-white rounded-lg border overflow-hidden animate-pulse"
      >
        <div class="h-32 bg-slate-200"></div>
        <div class="p-4 space-y-3">
          <div class="h-4 bg-slate-200 rounded w-3/4"></div>
          <div class="h-3 bg-slate-200 rounded w-1/2"></div>
          <div class="h-3 bg-slate-200 rounded w-2/3"></div>
        </div>
      </div>
    </div>

    <!-- Live Grid -->
    <div
      v-else-if="lives.length > 0"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4"
    >
      <LiveCard
        v-for="live in lives"
        :key="live.id"
        :item="live"
        @click="handleLiveClick"
        @update="handleLiveUpdate"
      />
    </div>

    <!-- Empty State -->
    <div v-else class="flex items-center justify-center h-full min-h-[400px]">
      <div class="text-center text-gray-500">
        <div
          class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4 mx-auto"
        >
          <Play class="w-12 h-12 text-slate-400" />
        </div>
        <h3 class="text-lg font-semibold text-slate-900 mb-2">{{ emptyTitle }}</h3>
        <p class="text-slate-500 max-w-md mb-4">
          {{ emptyDescription }}
        </p>
        <Button v-if="showClearButton" variant="outline" @click="$emit('clear-filters')">
          Clear Filters
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Play } from 'lucide-vue-next'
import LiveCard from './LiveCard.vue'
import { RoomListVO, LiveStatusEnum } from '@/api/live/stream'

// Live interface for live module
interface LiveItem extends Partial<RoomListVO> {
  id: string | number
  title?: string
  cover?: string
  category?: string
  description?: string
  rating?: number
  isLive?: boolean
  isScheduled?: boolean
  viewerCount?: number
  duration?: number
  startTime?: number
  status?: LiveStatusEnum
}

interface Props {
  lives: LiveItem[]
  loading?: boolean
  emptyTitle?: string
  emptyDescription?: string
  showClearButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  lives: () => [],
  loading: false,
  emptyTitle: 'No live streams found',
  emptyDescription: 'Try adjusting your search or filter criteria',
  showClearButton: true
})

const emit = defineEmits<{
  'live-click': [live: LiveItem]
  'clear-filters': []
  'live-update': [live: LiveItem]
}>()

const handleLiveClick = (live: LiveItem) => {
  emit('live-click', live)
}

const handleLiveUpdate = (updatedLive: LiveItem) => {
  emit('live-update', updatedLive)
}
</script>

<style scoped>
/* Responsive grid styles are handled by Tailwind classes */
</style>
