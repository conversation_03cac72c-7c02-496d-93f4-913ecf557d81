<script lang="ts" setup>
import { FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form'
import type { RoomCreateOrUpdateOrPublishReqVO } from '@/api/live/stream'

const emit = defineEmits([
  'previous',
  'publish',
  'startPublish',
  'save',
  'update',
  'update:privacy'
])

defineOptions({
  name: 'LiveSettings'
})

const props = defineProps({
  liveBasicForm: {
    type: Object as PropType<RoomCreateOrUpdateOrPublishReqVO>,
    required: true
  }
})

const { t } = useI18n() // 使用 i18n
const copyIcon = ref('copy') // 默认是复制图标
const message = useMessage()
const { push } = useRouter()
const host = import.meta.env.VITE_BASE_URL
const url = ref('')
const copyToClipboard = () => {
  if (!props.roomDetail.liveLinkUrl) {
    return
  }
  const input = document.createElement('input')
  // todo 待补充
  input.value = props.roomDetail.liveLinkUrl
  document.body.appendChild(input)
  input.select()
  input.setSelectionRange(0, 99999) // 选中文本
  try {
    const successful = document.execCommand('copy')
    if (successful) {
      message.success('Link Copied')
      copyIcon.value = 'check' // 复制成功后变成打钩图标
      // 延时恢复为复制图标
      setTimeout(() => {
        copyIcon.value = 'copy'
      }, 2000) // 2秒后恢复
    } else {
      message.error(t('translation.copyFailed'))
    }
  } catch (err) {
    message.error(t('translation.copyFailed'))
  }
  document.body.removeChild(input)
}
const contentLoading = ref(false)
const { query } = useRoute()
const id = query.id

/* METHODS */
const handlePrivacyChange = (value: boolean) => {
  // 将布尔值转换为数字 (1: 私有, 0: 公开)
  const newPrivacy = value ? 1 : 2
  // 或通过事件通知父级更新
  emit('update:privacy', newPrivacy)
}
</script>

<template>
  <div class="w-full">
    <div class="space-y-4">
      <FormField name="communication_emails">
        <FormItem class="flex flex-row items-center justify-between p-4 border rounded-lg">
          <div class="space-y-0.5">
            <FormLabel class="text-base"> Booking Scope </FormLabel>
            <FormDescription>
              Make this live broadcast public, all employees can visit then
            </FormDescription>
          </div>
          <FormControl>
            <Switch
              :model-value="liveBasicForm.privacy === 1"
              @update:model-value="handlePrivacyChange"
            />
          </FormControl>
        </FormItem>
      </FormField>
    </div>
  </div>
</template>