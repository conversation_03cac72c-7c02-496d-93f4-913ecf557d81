<script lang="ts" setup>
import {SuperUpload} from "@/components/SuperUpload";
import {RoomCreateOrUpdateOrPublishReqVO} from "@/api/live/stream";


</script>

<template>
  <div class="w-[186px] overflow-hidden">
    <AspectRatio :ratio="16 / 9" class="relative overflow-hidden rounded-md bg-muted">
      <!-- 图片 -->
      <img
          :src="liveBasicForm.cover"
          alt="Cover"
          class="object-cover w-full h-full cursor-pointer"
      />

      <!-- hover 显示遮罩 -->
      <div
          class="absolute inset-0 flex items-center justify-center transition-opacity duration-300 bg-black/50 opacity-0 hover:opacity-100"
      >
        <div class="flex space-x-4" v-show="!uploading">
          <Button variant="ghost" class="text-white hover:bg-white hover:text-gray-800">
            <EyeIcon class="w-8 h-8" />
          </Button>
          <Button
              variant="ghost"
              class="text-white hover:bg-white hover:text-gray-800"
              @click="triggerFileUpload"
          >
            <UploadIcon class="w-8 h-8" />
          </Button>
          <Button
              variant="ghost"
              class="text-white hover:bg-white hover:text-gray-800"
              @click="switchCover"
          >
            <RefreshCcwIcon class="w-8 h-8" />
          </Button>
        </div>
        <input
            id="fileInput"
            type="file"
            :accept="'.jpg,.jpeg,.png'"
            class="hidden"
            @change="handleFileUpload"
        />
      </div>
      <!-- 上传组件 (进度显示在图片中央) -->
      <SuperUpload
          v-for="fileInfo in fileInfoList"
          :key="fileInfo"
          :file-info="fileInfo"
          @prepared="handlePrepared"
          @complete="handleComplete"
          @error="handleError"
      >
        <template #default="{ progress }">
          <!-- 上传进度遮罩 -->
          <div
              v-if="progress.percentage > 0 && progress.percentage < 100"
              class="absolute inset-0 flex items-center justify-center overflow-hidden bg-black/60"
          >
            <div class="flex flex-col items-center">
              <CircularProgress
                  :progress="Number(progress.percentage.toFixed(0))"
              ></CircularProgress>
            </div>
          </div>
        </template>
      </SuperUpload>
    </AspectRatio>
  </div>
</template>
