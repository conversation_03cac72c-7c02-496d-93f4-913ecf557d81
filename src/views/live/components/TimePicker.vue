<script lang="ts" setup>
import { ref } from 'vue'

interface TimeSlot {
  time: string
}

// 正确类型定义
const timeRange = ref<TimeSlot[]>(generateTimeSlots())

// 生成时间段的函数
function generateTimeSlots(): TimeSlot[] {
  const slots: TimeSlot[] = []

  for (let hour = 0; hour < 24; hour++) {
    // 生成两种分钟格式：00 和 30
    for (const minute of ['00', '30']) {
      // 处理23:30后的时段（不需要生成）
      if (hour === 23 && minute === '30') break

      const time = `${hour.toString().padStart(2, '0')}:${minute}`

      slots.push({ time: time })
    }
  }

  return slots
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button
        variant="outline"
        class="font-normal"
        :class="!position ? 'text-muted-foreground' : 'text-foreground'"
      >
        <Icon name="Clock" />
        {{ position || 'Pick a time' }}
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent>
      <ScrollArea class="h-72 w-48">
        <DropdownMenuLabel>Time Range</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuRadioGroup v-model="position">
          <DropdownMenuRadioItem v-for="time in timeRange" :key="time.time" :value="time.time">
            {{ time.time }}
          </DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </ScrollArea>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
