<script setup lang="ts">
import LiveCard from './LiveCard.vue'
import {
  CreatedLiveReqVO,
  LiveApi,
  PageResultRoomListVO,
  RoomListVO
} from '@/api/live/stream'
import dayjs from 'dayjs'
import { ref, reactive, onMounted, computed } from 'vue'

defineOptions({ name: 'RecommendedLives' })

const { push } = useRouter()
const route = useRoute()

// 页面是否初始化，如初始化后的加载，不显示骨架
const liveLoading = ref(false)
const lives = ref<RoomListVO[]>([])
const total = ref(0)
const queryParams = reactive<CreatedLiveReqVO>({
  pageNo: 1,
  pageSize: 5,
  name: '',
  statusList: [],
  startTime: [],
  sortRule: 1
})

const getRemainingTime = (startTime: number): number => {
  const now = dayjs() // 当前时间
  const start = dayjs(startTime) // 直播开始时间
  return start.diff(now) // 返回剩余时间（毫秒）
}

const getLiveList = async () => {
  try {
    liveLoading.value = true
    let data: PageResultRoomListVO = await LiveApi.getVisibleRoom(queryParams)
    // 更新直播列表和总数
    lives.value = data?.list || []
    lives.value = lives.value.map((item: RoomListVO) => {
      return {
        ...item,
        isShowCountDown: true,
        remainingTime: getRemainingTime(Number(item.startTime))
      }
    })
    total.value = data?.total || 0
  } catch {
    liveLoading.value = false
  } finally {
    liveLoading.value = false
  }
}

const previousPage = () => {
  const totalPage = Math.ceil(total.value / queryParams.pageSize)
  if (queryParams.pageNo > 1) queryParams.pageNo--
  else queryParams.pageNo = totalPage
  getLiveList()
}

const nextPage = () => {
  const totalPage = Math.ceil(total.value / queryParams.pageSize)
  if (queryParams.pageNo < totalPage) queryParams.pageNo++
  else queryParams.pageNo = 1
  getLiveList()
}

// 计算属性，判断是否隐藏组件
const shouldHideComponent = computed(() => {
  const query = route.query
  for (const key in query) {
    if (key !== 'recommended' && key !== 'type') {
      return true
    }
  }
  return false
})

onMounted(async () => {
  await getLiveList()
})
</script>

<template>
  <div v-if="!shouldHideComponent">
    <div class="w-full space-between flex items-center">
      <div class="space-y-1 me-auto">
        <h2 class="text-2xl font-semibold tracking-tight"> Recommended For You</h2>
        <p class="text-sm text-muted-foreground"> Curated streams for you, updated daily. </p>
      </div>
      <Button variant="outline" class="me-2" @click="previousPage">
        <Icon name="CircleChevronLeft" />
        Previous
      </Button>
      <Button variant="outline" @click="nextPage">
        Explore More
        <Icon name="CircleChevronRight" />
      </Button>
    </div>

    <Separator class="my-4" />
    <div class="relative">
      <div class="grid grid-cols-5 gap-4 mb-4">
        <LiveCard
          v-for="live in lives"
          :key="live.id"
          :item="live"
          aspect-ratio="portrait"
          @item-click="push(`/live/detail?id=${live.id}`)"
        />
      </div>
    </div>
  </div>
</template>
