<template>
  <div
    class="group relative overflow-hidden rounded-lg border bg-card hover:shadow-lg transition-all duration-200 cursor-pointer"
    @click="handleClick"
  >
    <!-- Live Image/Icon Area -->
    <div
      :class="[
        'h-32 flex items-center justify-center text-white relative overflow-hidden',
        getLiveColor(item.category || 'Technology')
      ]"
    >
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="w-full h-full bg-gradient-to-br from-white/20 to-transparent"></div>
      </div>

      <!-- Live Image or Icon -->
      <div v-if="item.cover" class="relative z-10 w-full h-full">
        <img
          :src="item.cover"
          :alt="item.title || item.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </div>
      <div v-else class="relative z-10 text-center">
        <div class="w-12 h-12 mx-auto mb-2 rounded-lg bg-white/20 flex items-center justify-center">
          <component :is="getLiveIcon(item.category || 'Technology')" class="w-6 h-6" />
        </div>
        <div class="text-xs font-medium opacity-90">{{ item.category || 'Live Stream' }}</div>
      </div>

      <!-- Status and Type Badges -->
      <div class="absolute top-2 left-2 flex gap-1">
        <Badge
          v-if="item.isLive"
          variant="destructive"
          class="text-xs font-medium px-2 py-1 animate-pulse"
        >
          ● Live
        </Badge>
        <Badge
          v-else-if="item.isScheduled"
          variant="secondary"
          class="text-xs font-medium px-2 py-1"
        >
          Scheduled
        </Badge>
        <Badge
          v-if="item.status"
          :variant="getStatusVariant(item.status)"
          class="text-xs font-medium px-2 py-1"
        >
          {{ getStatusLabel(item.status) }}
        </Badge>
      </div>

      <!-- Viewer Count -->
      <div class="absolute top-2 right-2">
        <Badge
          variant="outline"
          class="text-xs font-medium px-2 py-1 bg-black/50 text-white border-white/30"
        >
          <Users class="w-3 h-3 mr-1" />
          {{ item.viewerCount || item.reservationTotal || 0 }}
        </Badge>
      </div>

      <!-- Live Time -->
      <div class="absolute bottom-2 right-2">
        <Badge
          variant="outline"
          class="text-xs font-medium px-2 py-1 bg-black/50 text-white border-white/30"
        >
          <Clock class="w-3 h-3 mr-1" />
          {{ formatLiveTime(item.startTime) }}
        </Badge>
      </div>
    </div>

    <!-- Live Information -->
    <div class="p-4">
      <!-- Live Title -->
      <h3 class="font-medium text-sm mb-2 line-clamp-2 group-hover:text-primary transition-colors">
        {{ item.title || item.name }}
      </h3>

      <!-- Live Description -->
      <p v-if="item.description" class="text-xs text-muted-foreground mb-3 line-clamp-2">
        {{ item.description }}
      </p>

      <!-- Live Metadata -->
      <div class="flex items-center justify-between text-xs text-muted-foreground">
        <div class="flex items-center gap-2">
          <div v-if="item.duration" class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ formatDuration(item.duration) }}</span>
          </div>
          <span v-if="item.duration">•</span>
          <div class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatLiveTime(item.startTime) }}</span>
          </div>
        </div>
        <div class="flex items-center gap-1">
          <Users class="w-3 h-3" />
          <span>{{ item.reservationTotal || 0 }}</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="bg-background py-2">
        <div class="flex items-center gap-2">
          <!-- Primary Action Button -->
          <div class="flex-1">
            <!-- Subscribe/Unsubscribe Button -->
            <Button
              v-if="
                item.status === LiveStatusEnum.UPCOMING && !item.reserved && item.joinType === null
              "
              size="sm"
              variant="default"
              @click.stop="handleSubscribe"
              class="w-full text-xs h-8 rounded-md font-medium cursor-pointer"
            >
              <Users class="w-3.5 h-3.5 mr-1.5" />
              Subscribe
            </Button>
            <Button
              v-else-if="
                item.status === LiveStatusEnum.UPCOMING &&
                item.reserved &&
                item.joinType === LiveJoinTypeEnum.RESERVE
              "
              size="sm"
              variant="outline"
              @click.stop="handleUnsubscribe"
              class="w-full text-xs h-8 rounded-md font-medium cursor-pointer"
            >
              <Users class="w-3.5 h-3.5 mr-1.5" />
              Unsubscribe
            </Button>

            <!-- Join/Start Button -->
            <Button
              v-else-if="item.status === LiveStatusEnum.LIVING"
              size="sm"
              variant="default"
              @click.stop="handleJoin"
              class="w-full text-xs h-8 rounded-md font-medium bg-green-600 hover:bg-green-700 cursor-pointer"
            >
              <Play class="w-3.5 h-3.5 mr-1.5" />
              Join Live
            </Button>
            <Button
              v-else-if="item.status === LiveStatusEnum.UPCOMING && item.userId === currentUserId"
              size="sm"
              variant="default"
              @click.stop="handleStart"
              class="w-full text-xs h-8 rounded-md font-medium bg-blue-600 hover:bg-blue-700 cursor-pointer"
            >
              <Play class="w-3.5 h-3.5 mr-1.5" />
              Start Live
            </Button>

            <!-- Playback Button -->
            <Button
              v-else-if="item.status === LiveStatusEnum.OVER"
              size="sm"
              variant="secondary"
              @click.stop="handlePlayback"
              class="w-full text-xs h-8 rounded-md font-medium cursor-pointer"
            >
              <Play class="w-3.5 h-3.5 mr-1.5" />
              Watch Replay
            </Button>

            <!-- Default View Button -->
            <Button
              v-else
              size="sm"
              variant="secondary"
              @click.stop="handleClick"
              class="w-full text-xs h-8 rounded-md font-medium cursor-pointer"
            >
              View Details
            </Button>
          </div>

          <!-- Secondary Action Buttons -->
          <div class="flex gap-1">
            <!-- Favorite Button -->
            <Button
              size="sm"
              variant="ghost"
              @click.stop="handleFavorite"
              class="h-8 w-8 p-0 rounded-md hover:bg-muted cursor-pointer"
              :title="item.favourite ? 'Remove from favorites' : 'Add to favorites'"
            >
              <Star
                :class="[
                  'w-4 h-4',
                  item.favourite
                    ? 'fill-yellow-400 text-yellow-400'
                    : 'text-muted-foreground hover:text-foreground'
                ]"
              />
            </Button>

            <!-- Share Button -->
            <Button
              v-if="
                item.status === LiveStatusEnum.LIVING || item.status === LiveStatusEnum.UPCOMING
              "
              size="sm"
              variant="ghost"
              @click.stop="handleShare"
              class="h-8 w-8 p-0 rounded-md hover:bg-muted cursor-pointer"
              title="Share live stream"
            >
              <Share2 class="w-4 h-4 text-muted-foreground hover:text-foreground" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Calendar, CircleArrowRight, Clock, Play, Share2, Star, Users } from 'lucide-vue-next'
import { LiveApi, LiveJoinTypeEnum, LiveStatusEnum, RoomListVO } from '@/api/live/stream'
import moment from 'moment'
import { useMessage } from '@/hooks/web/useMessage'
import { useUserStore } from '@/store/modules/user'
import { useRouter } from 'vue-router'

// Live interface for live module
interface LiveItem extends Partial<RoomListVO> {
  id: string | number
  title?: string
  cover?: string
  category?: string
  description?: string
  reservationTotal?: number
  isLive?: boolean
  isScheduled?: boolean
  viewerCount?: number
  duration?: number
  startTime?: number
  status?: LiveStatusEnum
}

interface Props {
  item: LiveItem
  aspectRatio?: 'portrait' | 'square'
}

const props = withDefaults(defineProps<Props>(), {
  aspectRatio: 'portrait'
})

const emit = defineEmits<{
  click: [item: LiveItem]
  subscribe: [item: LiveItem]
  unsubscribe: [item: LiveItem]
  join: [item: LiveItem]
  start: [item: LiveItem]
  playback: [item: LiveItem]
  favorite: [item: LiveItem]
  share: [item: LiveItem]
  update: [item: LiveItem]
}>()

// Composables
const message = useMessage()
const userStore = useUserStore()
const currentUserId = computed(() => userStore.user?.id)

// Methods
const getLiveColor = (category: string) => {
  const colors = {
    Technology: 'bg-gradient-to-br from-blue-500 to-blue-600',
    Business: 'bg-gradient-to-br from-green-500 to-green-600',
    Education: 'bg-gradient-to-br from-purple-500 to-purple-600',
    Entertainment: 'bg-gradient-to-br from-pink-500 to-pink-600',
    Health: 'bg-gradient-to-br from-red-500 to-red-600',
    Sports: 'bg-gradient-to-br from-orange-500 to-orange-600',
    News: 'bg-gradient-to-br from-gray-500 to-gray-600'
  }
  return colors[category as keyof typeof colors] || 'bg-gradient-to-br from-slate-500 to-slate-600'
}

const getLiveIcon = (category: string) => {
  const icons = {
    Technology: Play,
    Business: Users,
    Education: Calendar,
    Entertainment: Star,
    Health: Clock,
    Sports: CircleArrowRight,
    News: Share2
  }
  return icons[category as keyof typeof icons] || Play
}

const getStatusVariant = (status: LiveStatusEnum) => {
  switch (status) {
    case LiveStatusEnum.LIVING:
      return 'destructive' // Live - red
    case LiveStatusEnum.UPCOMING:
      return 'secondary' // Scheduled - blue
    case LiveStatusEnum.OVER:
      return 'outline' // Ended - gray
    default:
      return 'outline'
  }
}

const getStatusLabel = (status: LiveStatusEnum) => {
  switch (status) {
    case LiveStatusEnum.LIVING:
      return 'Live'
    case LiveStatusEnum.UPCOMING:
      return 'Scheduled'
    case LiveStatusEnum.OVER:
      return 'Ended'
    default:
      return 'Unknown'
  }
}

const formatLiveTime = (timestamp?: number) => {
  if (!timestamp) return ''
  return moment(timestamp).format('MMM DD, HH:mm')
}

const formatDuration = (duration?: number) => {
  if (!duration) return ''
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`
}

const handleClick = () => {
  emit('click', props.item)
}

// Action handlers
const handleSubscribe = async () => {
  try {
    await LiveApi.subscribeLive(String(props.item.id))
    const updatedItem = { ...props.item, reserved: true }
    message.success('Subscribed Successfully')
    emit('subscribe', updatedItem)
    emit('update', updatedItem)
  } catch (error) {
    message.error('Subscribe Failed')
  }
}

const handleUnsubscribe = async () => {
  try {
    await LiveApi.unSubscribeLive(props.item.id)
    const updatedItem = { ...props.item, reserved: false }
    message.success('Unsubscribed Successfully')
    emit('unsubscribe', updatedItem)
    emit('update', updatedItem)
  } catch (error) {
    message.error('Unsubscribe Failed')
  }
}

const handleJoin = async () => {
  try {
    const liveUrl = await LiveApi.joinRoom(props.item.id)
    window.open(liveUrl)
    emit('join', props.item)
  } catch (error) {
    message.error('Failed to join live stream')
  }
}

const handleStart = async () => {
  try {
    const liveUrl = await LiveApi.joinRoom(props.item.id)
    const updatedItem = { ...props.item, status: LiveStatusEnum.LIVING }
    window.open(liveUrl)
    emit('start', updatedItem)
    emit('update', updatedItem)
  } catch (error) {
    message.error('Failed to start live stream')
  }
}

const handlePlayback = () => {
  // Navigate to live detail page with record tab
  const { push } = useRouter()
  push(`/live/detail?id=${props.item.id}&tab=record`)
}

const handleFavorite = async () => {
  try {
    let updatedItem
    if (props.item.favourite) {
      await LiveApi.removeFavorite(props.item.id)
      updatedItem = { ...props.item, favourite: false }
      message.success('Removed from favorites')
    } else {
      await LiveApi.addFavorite(props.item.id)
      updatedItem = { ...props.item, favourite: true }
      message.success('Added to favorites')
    }
    emit('favorite', updatedItem)
    emit('update', updatedItem)
  } catch (error) {
    message.error('Failed to update favorites')
  }
}

const handleShare = () => {
  // Copy share link to clipboard
  const shareUrl = `${window.location.origin}/live/detail?id=${props.item.id}`
  navigator.clipboard
    .writeText(shareUrl)
    .then(() => {
      message.success('Share link copied to clipboard')
    })
    .catch(() => {
      message.error('Failed to copy share link')
    })
  emit('share', props.item)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
