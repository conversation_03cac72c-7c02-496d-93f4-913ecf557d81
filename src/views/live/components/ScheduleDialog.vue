<script setup lang="ts">
import DatePicker from '../detail/components/DatePicker.vue'
import Settings from './Settings.vue'
import TimeSelect from '../detail/components/TimeSelect.vue'
import Loading from '@/components/Loading/index.vue'
import { useRouter } from 'vue-router'
import UserPicker from '../detail/components/UserPicker.vue'
import {
  RoomCreateOrUpdateOrPublishReqVO,
  DeptAndUserMixedVO,
  RoomDetailVO,
  RoomUserVO,
  RoomAttachmentVO,
  LiveApi
} from '@/api/live/stream'
import cover1 from '@/assets/live/management/default_cover_1.jpg'
import cover2 from '@/assets/live/management/default_cover_2.jpg'
import cover3 from '@/assets/live/management/default_cover_3.jpg'
import { useUserStore } from '@/store/modules/user'
import {
  getOneHourFifteenMinutesLater,
  get15Minutes,
  getTodayDate,
  isFutureDate,
  isTimeBefore,
  isTimeBeforeCurrent,
  formatLiveDate2,
  formatTime,
  compareTime,
  toCalendarDate,
  getTomorrowDate
} from '@/utils/formatTime'
import dayjs from 'dayjs'
import { SuperUpload } from '@/components/SuperUpload'
import { FileInfo } from '@/components/SuperUpload/src/config'
import { checkFile } from '@/utils/fileUtil'
import { nanoid } from 'nanoid'
import { UploadResult } from '@/components/SuperUpload/src/config'
import { type DateValue, getLocalTimeZone } from '@internationalized/date'
import {
  Eye as EyeIcon,
  Upload as UploadIcon,
  RefreshCcw as RefreshCcwIcon,
  Plus
} from 'lucide-vue-next'
interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
}

const message = useMessage()
const { push } = useRouter()
const userStore = useUserStore() // 获取用户登录信息
const currentUser = computed(() => userStore.user)
/* 初始化 */
const emit = defineEmits(['item-click'])
const isSubmission = ref<boolean>(false)
const liveBasicForm = reactive<RoomCreateOrUpdateOrPublishReqVO>({
  name: '',
  startTime: '',
  endTime: '',
  privacy: 1,
  cover: cover1,
  speakerIds: [userStore.user?.id],
  description: '',
  participantIds: [],
  attachmentIds: [],
  configOptions: []
})

// 日期相关
const startDate = ref()
const endDate = ref()
const startDateRef = ref()
const endDateRef = ref()
const startTimeRef = ref()
const endTimeRef = ref()
// 开始时间，具体到分钟
const startTime = ref()
const startTimeCopy = ref()
const endTime = ref()
const endTimeCopy = ref()
const isDialogOpen = ref(false)
const currentCoverIndex = ref(0)
const fileInfoList = ref<FileInfo[]>([])
const fileTypes = ['JPG', 'JPEG', 'PNG'] // 限制的文件格式
const uploading = ref(false)
const submitLoading = ref(false)
// 提交成功后的对话框状态
const hasSubmitSuccess = ref(false)

// 用于存储格式化后的时间显示
const formattedStartTime = ref('')
const formattedStartDate = ref('')
const formattedEndTime = ref('')
const formattedEndDate = ref('')

// 用于储存返回的直播间id
const liveId = ref<any>()
// 选人相关
const speakers = ref<RoomUserVO[]>([])
const invitedUsers = ref<RoomUserVO[]>([])
const isAddSpeaker = ref(false)
const UserPickerRef = ref()
const disabledUserList = ref<DeptAndUserMixedVO[]>([])
// 选人相关
const handleDeptUserToUser = (deptUserList: DeptAndUserMixedVO[]): RoomUserVO[] => {
  return deptUserList.map((item: DeptAndUserMixedVO) => {
    return {
      userId: item.id,
      nickname: item.name,
      avatar: item.avatar,
      joinType: 20,
      role: 2,
      deptName: 'Managment',
      deptId: 1
    }
  })
}
const handleUserToDeptUserMixed = (users: RoomUserVO[]): DeptAndUserMixedVO[] => {
  return users.map((item: RoomUserVO) => {
    return {
      id: item.userId,
      name: item.nickname,
      avatar: item.avatar,
      isChecked: true,
      type: 2
    }
  })
}
const handleConfirm = (data: DepartmentAndUserData[]) => {
  if (isAddSpeaker.value) {
    speakers.value = handleDeptUserToUser(data)
  }

  if (!isAddSpeaker.value) {
    invitedUsers.value = handleDeptUserToUser(data)
  }
}

const addSpeaker = () => {
  isAddSpeaker.value = true
  UserPickerRef.value.openDialog()
  // 这里不行，必须Map
  UserPickerRef.value.selectedUsers = handleUserToDeptUserMixed(speakers.value)
  disabledUserList.value = handleUserToDeptUserMixed(invitedUsers.value)
}

const addInvitee = () => {
  isAddSpeaker.value = false
  UserPickerRef.value.openDialog()
  UserPickerRef.value.selectedUsers = handleUserToDeptUserMixed(invitedUsers.value)
  disabledUserList.value = handleUserToDeptUserMixed(speakers.value)
}
/* METHODS */
// 初始化表单数据
const initializeForm = async () => {
  await nextTick() // 确保 DOM 渲染完成
  if (startDateRef.value && endDateRef.value && startTimeRef.value && endTimeRef.value) {
    startDate.value = toCalendarDate(getTodayDate())
    endDate.value = toCalendarDate(getTodayDate())
    startDateRef.value.date = startDate.value
    endDateRef.value.date = endDate.value
    startTime.value = get15Minutes()
    endTime.value = getOneHourFifteenMinutesLater()
    startTimeRef.value.selectedTime = startTime.value
    endTimeRef.value.selectedTime = endTime.value

    // 设置默认时间到liveBasicForm
    const formattedStartDate = formatLiveDate2(startDate.value.toDate(getLocalTimeZone()))
    const formattedEndDate = formatLiveDate2(endDate.value.toDate(getLocalTimeZone()))
    liveBasicForm.startTime = `${formattedStartDate} ${startTime.value}:00`
    liveBasicForm.endTime = `${formattedEndDate} ${endTime.value}:00`
  }
}

// 将DateValue类型的时间转为正常的Date类型
const handleCalenderToNormal = (date: DateValue) => {
  return date.toDate(getLocalTimeZone())
}

// 判断两个日期是否在同一天
const isSameDate = (start: Date, end: Date) => {
  return dayjs(start).isSame(dayjs(end), 'day')
}

// 开始日期变更逻辑
const handleStartDateChange = (date: DateValue) => {
  const curCheckedStartDate = handleCalenderToNormal(date)
  const curEndDate = handleCalenderToNormal(endDateRef.value.date)
  // 判断开始时间是否晚于当前时间
  const isRightDate = isFutureDate(curCheckedStartDate)
  if (!isRightDate) {
    // 不可小于当前时间
    message.warning('Start time cannot be earlier than current time')
    startDateRef.value.date = startDate.value
    return
  }
  // 选择的开始时间晚于结束时间，自动将结束时间设置为开始时间
  if (dayjs(curCheckedStartDate).isAfter(dayjs(curEndDate))) {
    endDateRef.value.date = date
    endDate.value = date

    // 更新liveBasicForm中的时间
    const formattedStartDate = formatLiveDate2(curCheckedStartDate)
    liveBasicForm.startTime = `${formattedStartDate} ${startTime.value}:00`
    liveBasicForm.endTime = `${formattedStartDate} ${endTime.value}:00`
  } else {
    startDate.value = date

    // 更新liveBasicForm中的startTime
    const formattedStartDate = formatLiveDate2(curCheckedStartDate)
    liveBasicForm.startTime = `${formattedStartDate} ${startTime.value}:00`
  }
}

// 开始时间变更逻辑
const handleStartTimeChange = (value: string) => {
  // 判断value是否小于当前的时间
  const curStartDate = handleCalenderToNormal(startDateRef.value.date)
  const isBefore = isTimeBeforeCurrent(value, curStartDate)
  if (isBefore) {
    message.warning('Start time cannot be earlier than current time')
    startTimeRef.value.selectedTime = startTimeCopy.value
    return
  }
  // 更新startTime的副本
  startTimeCopy.value = value
  const curEndDate = handleCalenderToNormal(endDateRef.value.date)
  // 判断startDate和endDate是否为同一天，如果是，进行时间比较
  const isSameDay = isSameDate(curStartDate, curEndDate)
  if (isSameDay) {
    // 如果startTime晚于endTime，endTime需要加一个小时
    if (!compareTime(value, endTimeCopy.value)) {
      // endTime需要加一个小时
      const endTimeAddOneHour = addOneHourToTime(startTimeCopy.value)
      endTimeRef.value.selectedTime = endTimeAddOneHour
      endTimeCopy.value = endTimeAddOneHour

      // 如果加了一小时之后变成了以00开头的数据，那么需要重新设置endDateRef.value.date的值
      if (endTimeAddOneHour.startsWith('00')) {
        endDateRef.value.date = toCalendarDate(getTomorrowDate())
        endDate.value = toCalendarDate(getTomorrowDate())
        // 更新liveBasicForm中的endTime（下一天的日期）
        const tomorrowDate = handleCalenderToNormal(endDate.value)
        const formattedTomorrowDate = formatLiveDate2(tomorrowDate)
        liveBasicForm.endTime = `${formattedTomorrowDate} ${endTimeAddOneHour}:00`
      } else {
        // 更新liveBasicForm中的endTime（同一天的日期）
        const formattedEndDate = formatLiveDate2(curEndDate)
        liveBasicForm.endTime = `${formattedEndDate} ${endTimeAddOneHour}:00`
      }
    }
  }
  // 更新liveBasicForm中的startTime
  const formateStartDate = formatLiveDate2(curStartDate)
  liveBasicForm.startTime = `${formateStartDate} ${value}:00`
}

// 结束日期变更逻辑
const handleEndDateChange = (date: DateValue) => {
  // 选择的结束时间早于当日，报错
  const curCheckedEndDate = handleCalenderToNormal(date)
  const curStartDate = handleCalenderToNormal(startDateRef.value.date)
  const isRightDate = isFutureDate(curCheckedEndDate)
  if (!isRightDate) {
    // 不可小于当前时间
    message.warning('End time cannot be earlier than current time')
    endDateRef.value.date = endDate.value
    return
  }
  const formateStartDate = formatLiveDate2(curStartDate)
  const realStartDate = `${formateStartDate} ${startTime.value}:00`
  const formateEndDate = formatLiveDate2(curCheckedEndDate)
  const realEndDate = `${formateEndDate} ${endTime.value}:00`
  // 选择的结束时间早于开始时间（忽略时间部分），给出提示
  if (dayjs(realEndDate).isBefore(dayjs(realStartDate))) {
    message.warning('End time cannot be earlier than start time')
    // 恢复为之前的值
    endDateRef.value.date = endDate.value
    return
  } else {
    endDate.value = date
    // 更新liveBasicForm中的endTime
    liveBasicForm.endTime = realEndDate
  }
}

// 结束时间变更逻辑
const handleEndTimeChange = (value: string) => {
  const curStartDate = startDate.value.toDate(getLocalTimeZone())
  const curEndDate = endDate.value.toDate(getLocalTimeZone())
  // 判断startDate和endDate是否为同一天，如果是，进行时间比较
  const isSameDay = isSameDate(curStartDate, curEndDate)
  if (isSameDay) {
    // 判断value是否小于当前的时间和分钟
    const isBefore = isTimeBefore(value, startTimeCopy.value)
    if (isBefore) {
      message.warning('End time cannot be earlier than start time')
      endTimeRef.value.selectedTime = endTimeCopy.value
      return
    } else {
      endTimeCopy.value = value
    }
  }
  // 更新liveBasicForm中的endTime
  const formateEndDate = formatLiveDate2(curEndDate)
  liveBasicForm.endTime = `${formateEndDate} ${value}:00`
}

// 将 HH:mm 格式的时间字符串转换为 Date 对象
const addOneHourToTime = (time: string): string => {
  // 将 HH:mm 格式的时间字符串转换为 Date 对象
  const [hours, minutes] = time.split(':').map(Number)
  const date = new Date()
  date.setHours(hours, minutes, 0, 0) // 设置当前时间为给定的时间
  // 增加一个小时
  date.setHours(date.getHours() + 1)

  // 格式化回 HH:mm 格式
  const newHours = String(date.getHours()).padStart(2, '0') // 保证小时是两位数
  const newMinutes = String(date.getMinutes()).padStart(2, '0') // 保证分钟是两位数

  return `${newHours}:${newMinutes}`
}

const handlePrepared = (value: UploadResult) => {
  uploading.value = true
}

const handleError = () => {
  uploading.value = false
}

const handleComplete = (uploadResult: UploadResult) => {
  liveBasicForm.cover = uploadResult.url
  uploading.value = false
}

// 上传封面相关
const uploadFile = async (file: File) => {
  try {
    // 检测文件
    const checkResult = await checkFile(file, fileTypes)
    // 检测文件大小
    if (file.size > 10 * 1024 * 1024) {
      message.notifyWarning('The file size exceeds the 100MB')
      return false
    }
    // 检查通过，创建上传文件信息
    if (checkResult.can) {
      const fileInfo: FileInfo = {
        uid: nanoid(10),
        file: file,
        type: checkResult.type,
        folderId: 0, // 例如目录id，默认根目录
        relativePath: '' // 例如相对路径，默认无相对路径
      }
      // 添加到上传文件列表
      fileInfoList.value.push(fileInfo)
    }
  } catch {
  } finally {
  }
}

// 处理封面上传
const handleFileUpload = (event) => {
  const file = event.target.files[0]
  uploadFile(file)
  // console.log('上传的文件信息：', file)
}

// 触发文件上传
const triggerFileUpload = () => {
  const fileInput = document.getElementById('fileInput')
  if (fileInput) {
    fileInput.click()
  }
}

// 切换封面逻辑
const switchCover = () => {
  // 睡500秒然后再执行
  currentCoverIndex.value = currentCoverIndex.value + 1
  // 除3取余，如果为0那么liveBasicForm.cover是cover1,1的话为cover2,2的话为cover3
  const index = currentCoverIndex.value % 3
  if (index == 0) {
    liveBasicForm.cover = cover1
  }
  if (index == 1) {
    liveBasicForm.cover = cover2
  }
  if (index == 2) {
    liveBasicForm.cover = cover3
  }
}

// 更新隐私设置
const handlePrivacyUpdate = (newPrivacy: number) => {
  liveBasicForm.privacy = newPrivacy
  // console.log('隐私设置已更新：', newPrivacy)
}

// 重置表单
const handleReset = () => {
  liveBasicForm.name = ''
  liveBasicForm.startTime = ''
  liveBasicForm.endTime = ''
  liveBasicForm.privacy = 1
  liveBasicForm.cover = cover1
  liveBasicForm.speakerIds = [userStore.user?.id]
  liveBasicForm.description = ''
  liveBasicForm.participantIds = []
  liveBasicForm.attachmentIds = []
  liveBasicForm.configOptions = []

  // 重置时间
  startDate.value = toCalendarDate(getTodayDate())
  endDate.value = toCalendarDate(getTodayDate())
  startDateRef.value.date = startDate.value
  endDateRef.value.date = endDate.value
  startTime.value = get15Minutes()
  endTime.value = getOneHourFifteenMinutesLater()
  startTimeRef.value.selectedTime = startTime.value
  endTimeRef.value.selectedTime = endTime.value
  // 重置主讲人和邀请者
  speakers.value = [
    {
      userId: currentUser.value.id,
      nickname: currentUser.value.nickname,
      avatar: currentUser.value.avatar,
      joinType: 20,
      role: 2,
      deptName: 'Management',
      deptId: 1
    }
  ]
  invitedUsers.value = []
  // 重置封面
  currentCoverIndex.value = 0
  liveBasicForm.cover = cover1
  // 重置上传文件列表
  fileInfoList.value = []
}

// 取消创建直播
const handleCancel = () => {
  // 关闭对话框
  isDialogOpen.value = false
  // 重置表单
  handleReset()
}

// 提交表单
const handleSubmit = async () => {
  if (!liveBasicForm.name) {
    message.notifyWarning('Please input live title first')
    return
  }
  // 关闭对话框，展示loading
  // submitLoading.value = true
  // isDialogOpen.value = false

  // 将时间戳格式传递给后端
  const curStartDate = handleCalenderToNormal(startDateRef.value.date)
  const curEndDate = handleCalenderToNormal(endDateRef.value.date)
  const formattedStartDateStr = formatLiveDate2(curStartDate)
  const formattedEndDateStr = formatLiveDate2(curEndDate)
  const realStartDate = `${formattedStartDateStr} ${startTime.value}:00`
  const realEndDate = `${formattedEndDateStr} ${endTime.value}:00`
  liveBasicForm.startTime = String(formatTime(realStartDate))
  liveBasicForm.endTime = String(formatTime(realEndDate))
  liveBasicForm.participantIds = invitedUsers.value.map((item: RoomUserVO) => item.userId)
  liveBasicForm.speakerIds = speakers.value.map((item: RoomUserVO) => item.userId)
  // 保存格式化的时间用于显示
  formattedStartTime.value = startTime.value
  formattedStartDate.value = formattedStartDateStr
  formattedEndTime.value = endTime.value
  formattedEndDate.value = formattedEndDateStr

  try {
    submitLoading.value = true
    const res = await LiveApi.publishRoom({ ...liveBasicForm })
    isSubmission.value = true
    hasSubmitSuccess.value = true // 标记提交成功
    // console.log('直播发布成功啦！')
    // console.log('直播间ID：', res)
    liveId.value = res // 保存返回的直播间ID

    // 请求成功，重新打开对话框显示成功信息
    isDialogOpen.value = true
  } catch (error) {
    // console.log('直播发布失败...', error)
    // 请求失败，重新打开对话框让用户修改
    isDialogOpen.value = true
  } finally {
    submitLoading.value = false
  }
}

/* WATCH */
watch(isDialogOpen, async (newVal, oldVal) => {
  if (newVal) {
    await initializeForm() // 当弹窗打开时初始化表单
  } else if (oldVal && !newVal && !submitLoading.value && hasSubmitSuccess.value) {
    // 当对话框从打开变为关闭状态，且不是因为提交表单导致的关闭，且提交已成功
    // 重新加载页面
    window.location.reload()
  }
})

/* ON MOUNTED */
onMounted(() => {
  speakers.value.push({
    userId: currentUser.value.id,
    nickname: currentUser.value.nickname,
    avatar: currentUser.value.avatar,
    joinType: 20,
    role: 2,
    deptName: 'Management',
    deptId: 1
  })
  // console.log('ScheduleDialog 组件已挂载', liveBasicForm)
})
</script>

<template>
  <!-- 对话框 -->
  <Dialog v-model:open="isDialogOpen">
    <DialogTrigger as-child>
      <Button class="flex items-center gap-1">
        <Plus class="w-4 h-4" />
        Create Live
      </Button>
    </DialogTrigger>

    <DialogContent class="w-auto max-w-none min-w-[575px]">
      <DialogHeader>
        <DialogTitle> Schedule </DialogTitle>
        <DialogDescription> Important live are warmed up </DialogDescription>
      </DialogHeader>

      <!--表单容器-->
      <!--没有创建直播时展示需要填写内容的表单-->
      <div v-if="!isSubmission" class="flex flex-col gap-4" v-loading="submitLoading">
        <!--标题-->
        <div class="flex flex-row items-center justify-between gap-3">
          <Label for="title" class="text-right">
            <Icon name="Captions" />
          </Label>
          <Input v-model="liveBasicForm.name" id="title" placeholder="Live broadcast title" />
        </div>

        <!--日期时间-->
        <div class="flex flex-row items-center justify-between gap-3">
          <Label for="date" class="text-right">
            <Icon name="Clock" />
          </Label>

          <!--日期时间选择器-->
          <div class="flex flex-row items-center justify-between gap-2">
            <DatePicker ref="startDateRef" @change="handleStartDateChange"></DatePicker>
            <TimeSelect
              ref="startTimeRef"
              start="00:00"
              end="23:45"
              :step="15"
              @change="handleStartTimeChange"
            ></TimeSelect>
            <span class="text-stone-400">-</span>
            <DatePicker ref="endDateRef" @change="handleEndDateChange"></DatePicker>
            <TimeSelect
              ref="endTimeRef"
              start="00:00"
              end="23:45"
              :step="15"
              @change="handleEndTimeChange"
            ></TimeSelect>
          </div>
        </div>

        <!--主讲人-->
        <div class="flex flex-row items-center justify-between gap-3">
          <Label for="title" class="text-right">
            <Icon name="Mic" />
          </Label>

          <!--主讲人选取-->
          <TagsInput v-model="speakers" class="w-full h-9" @click="addSpeaker">
            <TagsInputItem
              v-for="item in speakers.slice(0, 2)"
              :key="item.userId"
              :value="item.nickname"
            >
              <TagsInputItemText />
            </TagsInputItem>

            <span v-if="speakers.length > 2" class="text-sm text-gray-500">
              +{{ speakers.length - 2 }}
            </span>

            <TagsInputInput placeholder="Speakers..." />
          </TagsInput>

          <!--<Button @click="addSpeaker">Add Speaker</Button>-->
        </div>

        <!--邀请者-->
        <div class="flex flex-row items-center justify-between gap-3">
          <Label for="title" class="text-right">
            <Icon name="Users" />
          </Label>

          <!--邀请成员选取-->
          <TagsInput v-model="invitedUsers" class="w-full h-9" @click="addInvitee">
            <TagsInputItem
              v-for="item in invitedUsers.slice(0, 2)"
              :key="item.userId"
              :value="item.nickname"
            >
              <TagsInputItemText />
              <!--<TagsInputItemDelete />-->
            </TagsInputItem>

            <span v-if="invitedUsers.length > 2" class="text-sm text-gray-500">
              +{{ invitedUsers.length - 2 }}
            </span>

            <TagsInputInput placeholder="Invitees..." />
          </TagsInput>
        </div>

        <!--封面-->
        <div class="flex flex-row items-start justify-start gap-3">
          <Label for="title" class="text-right">
            <Icon name="Image" />
          </Label>

          <div class="w-[186px] overflow-hidden">
            <AspectRatio :ratio="16 / 9" class="relative overflow-hidden rounded-md bg-muted">
              <!-- 图片 -->
              <img
                :src="liveBasicForm.cover"
                alt="Cover"
                class="object-cover w-full h-full cursor-pointer"
              />

              <!-- hover 显示遮罩 -->
              <div
                class="absolute inset-0 flex items-center justify-center transition-opacity duration-300 bg-black/50 opacity-0 hover:opacity-100"
              >
                <div class="flex space-x-4" v-show="!uploading">
                  <Button variant="ghost" class="text-white hover:bg-white hover:text-gray-800">
                    <EyeIcon class="w-8 h-8" />
                  </Button>
                  <Button
                    variant="ghost"
                    class="text-white hover:bg-white hover:text-gray-800"
                    @click="triggerFileUpload"
                  >
                    <UploadIcon class="w-8 h-8" />
                  </Button>
                  <Button
                    variant="ghost"
                    class="text-white hover:bg-white hover:text-gray-800"
                    @click="switchCover"
                  >
                    <RefreshCcwIcon class="w-8 h-8" />
                  </Button>
                </div>
                <input
                  id="fileInput"
                  type="file"
                  :accept="'.jpg,.jpeg,.png'"
                  class="hidden"
                  @change="handleFileUpload"
                />
              </div>
              <!-- 上传组件 (进度显示在图片中央) -->
              <SuperUpload
                v-for="fileInfo in fileInfoList"
                :key="fileInfo"
                :file-info="fileInfo"
                @prepared="handlePrepared"
                @complete="handleComplete"
                @error="handleError"
              >
                <template #default="{ progress }">
                  <!-- 上传进度遮罩 -->
                  <div
                    v-if="progress.percentage > 0 && progress.percentage < 100"
                    class="absolute inset-0 flex items-center justify-center overflow-hidden bg-black/60"
                  >
                    <div class="flex flex-col items-center">
                      <CircularProgress
                        :progress="Number(progress.percentage.toFixed(0))"
                      ></CircularProgress>
                    </div>
                  </div>
                </template>
              </SuperUpload>
            </AspectRatio>
          </div>
        </div>

        <!--设置-->
        <div class="flex flex-row items-start justify-start gap-3">
          <Label for="title" class="text-right">
            <Icon name="Settings" />
          </Label>

          <!--设置组-->
          <Settings :liveBasicForm="liveBasicForm" @update:privacy="handlePrivacyUpdate" />
        </div>
      </div>

      <!--创建直播后展示直播间信息-->
      <div v-else class="flex flex-col gap-4 my-6 mb-20">
        <!--创建直播成功提示-->
        <div class="flex flex-col items-center justify-between gap-4">
          <div class="p-2 rounded-full bg-lime-600">
            <Icon name="Check" :size="28" color="white" />
          </div>

          <span class="font-bold">Live broadcast reservation successful!</span>
        </div>

        <!--分割线-->
        <DropdownMenuSeparator class="mb-6" />

        <!--直播主题-->
        <div class="flex flex-col items-center justify-center gap-2">
          <!--直播间状态-->
          <Badge class="bg-amber-400">Upcoming</Badge>

          <!--标题 & 主讲人-->
          <div class="flex flex-col items-center justify-center gap-3">
            <span class="font-bold">{{ liveBasicForm.name || 'Untitled Live Broadcast' }}</span>
            <span class="text-sm text-stone-500"
              >Speaker: {{ userStore.user?.nickname || 'You' }}</span
            >
          </div>
        </div>

        <!--日期时间-->
        <div class="flex flex-row items-center justify-between gap-4">
          <!--开始时间-->
          <div class="flex flex-col items-center justify-center w-full gap-2">
            <span class="text-2xl font-bold">{{ formattedStartTime }}</span>
            <span class="text-sm">{{ formattedStartDate }}</span>
          </div>

          <!--分割线-->
          <DropdownMenuSeparator class="w-full" />

          <!--结束时间-->
          <div class="flex flex-col items-center justify-center w-full gap-2">
            <span class="text-2xl font-bold">{{ formattedEndTime }}</span>
            <span class="text-sm">{{ formattedEndDate }}</span>
          </div>
        </div>
      </div>

      <DialogFooter>
        <!--没有创建直播时显示 Submit-->
        <div v-if="!isSubmission" class="flex flex-row gap-2">
          <Button variant="outline" @click="handleCancel"> Cancel </Button>

          <Button type="submit" @click="handleSubmit"> Submit </Button>
        </div>

        <!--创建直播后显示 Share & More Settings & Start-->
        <div v-if="isSubmission" class="flex flex-row items-center justify-between w-full">
          <Button variant="outline" size="icon">
            <Icon name="SquareArrowOutUpRight" />
          </Button>

          <div class="flex flex-row gap-2">
            <Button variant="outline" @click="push(`/live/detail?id=${liveId}`)">
              More Settings
            </Button>
            <Button type="submit" @click="isDialogOpen = false"> Close </Button>
            <!--<Button type="submit"> Start </Button>-->
          </div>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
  <UserPicker
    ref="UserPickerRef"
    :disabledUserList="disabledUserList"
    @confirm="handleConfirm"
    :showModal="false"
  />
</template>
