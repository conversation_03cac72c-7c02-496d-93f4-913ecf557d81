<script setup lang="ts">
import { Copy, Check } from 'lucide-vue-next'
const props = defineProps({
  link: {
    type: String,
    required: false,
    default: ''
  },
  open: {
    type: Boolean,
    required: false,
    default: false
  }
})
const message = useMessage()
const showCheckIcon = ref(false)
const copyToClipboard = () => {
  const input = document.createElement('input')
  // todo 待补充
  input.value = props.link
  document.body.appendChild(input)
  input.select()
  input.setSelectionRange(0, 99999) // 选中文本
  try {
    const successful = document.execCommand('copy')
    if (successful) {
      showCheckIcon.value = true
      message.success('Link Copied')
      // 隔两秒之后又转为false
      setTimeout(() => {
        showCheckIcon.value = false
      }, 2000)
    } else {
      // message.error(t('translation.copyFailed'))
    }
  } catch (err) {
    // message.error(t('translation.copyFailed'))
  }
  document.body.removeChild(input)
}
const isDialogOpen = ref(false)
const linkHost = ref('')
watch(
  () => props.open,
  (newValue) => {
    isDialogOpen.value = true
  }
)
watch(
  () => props.link,
  (newValue) => {
    linkHost.value = newValue
  }
)
</script>

<template>
  <Dialog v-model:open="isDialogOpen">
    <DialogTrigger as-child>
      <!-- <Button variant="outline"> Share </Button> -->
    </DialogTrigger>
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Share link</DialogTitle>
        <DialogDescription> Anyone who has this link will be able to view this. </DialogDescription>
      </DialogHeader>
      <div class="flex items-center space-x-2">
        <div class="grid flex-1 gap-2">
          <Label for="link" class="sr-only"> Link </Label>
          <Input id="link" v-model="linkHost" read-only />
        </div>
        <Button size="sm" class="px-3" @click="copyToClipboard">
          <span class="sr-only">Copy</span>
          <Copy class="w-4 h-4" v-if="!showCheckIcon" />
          <Check class="w-4 h-4" v-else />
        </Button>
      </div>
      <DialogFooter class="sm:justify-start">
        <DialogClose as-child>
          <Button type="button" variant="secondary"> Close </Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
