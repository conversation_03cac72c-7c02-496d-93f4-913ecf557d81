<script setup lang="ts">
import BasicForm from './BasicForm.vue'
import Courseware from './Courseware.vue'
import Participants from './Participants.vue'
import Setting from './Setting.vue'
import Record from './Record.vue'
import { ref } from 'vue'
import { cn } from '@/lib/utils'
import { LiveApi, DeptAndUserMixedVO, RoomCreateOrUpdateOrPublishReqVO } from '@/api/live/stream'
import { UploadResult } from '@/components/SuperUpload/src/config'
import { getLocalTimeZone } from '@internationalized/date'
import { formatTime } from '@/utils/formatTime'
import { LinkProp } from '@/components/ContainerWrap'
import { ScrollArea } from '@/components/ui/scroll-area'
interface LiveDetailSideNav extends LinkProp {
  key: string
}
interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
}
const contentLoading = ref(false)
const liveDetailSideNav: LiveDetailSideNav[] = [
  { name: 'Basic Info', key: 'basic' },
  { name: 'Participants', key: 'participants' },
  { name: 'Courseware', key: 'courseware' },
  { name: 'Live Setting', key: 'setting' },
  { name: 'Record', key: 'record' }
]
const props = defineProps({
  roomDetail1: {
    type: Object,
    default: () => ({})
  }
})
const liveBasicForm = reactive<RoomCreateOrUpdateOrPublishReqVO>({
  name: '',
  startTime: '',
  endTime: '',
  privacy: 1,
  cover: '',
  speakerIds: [],
  description: '',
  participantIds: [],
  attachmentIds: [],
  configOptions: []
})
// 记录当前激活的项
const activeNavKey = ref(liveDetailSideNav[0].key)
const { query } = useRoute()
const id = query.id
const settingRef = ref()
const courseRef = ref()
const participantsRef = ref()
const basicRef = ref()
const { push } = useRouter()
const emit = defineEmits(['change'])
const handleSave = async () => {
  try {
    contentLoading.value = true
    await LiveApi.updateRoom({
      id: id,
      ...basicRef.value.liveBasicForm,
      // participantIds: participantsRef.value.invitees.map((item: DepartmentAndUserData) => item.id),
      attachmentIds: courseRef.value.fileList.map((item: UploadResult) => item.id),
      configOptions: settingRef.value.configList,
      privacy: settingRef.value.privacy ? 1 : 2,
      startTime: formatTime(basicRef.value.startDate.toDate(getLocalTimeZone())),
      endTime: formatTime(basicRef.value.endDate.toDate(getLocalTimeZone()))
    })
    handleReturn()
  } finally {
    contentLoading.value = false
  }
}
const handleReturn = () => {
  push('/live/center/my')
}
const handleSaveBasic = (liveBasic: RoomCreateOrUpdateOrPublishReqVO) => {
  liveBasicForm.cover = liveBasic.cover
  liveBasicForm.name = liveBasic.name
  liveBasicForm.startTime = liveBasic.startTime
  emit('change', liveBasicForm)
}
</script>

<template>
    <div class="flex-1 flex flex-col">
      <ScrollArea class="flex-1 p-4">
          <ContainerNav :is-collapsed="false" :links="liveDetailSideNav" @changeNav="($event) => activeNavKey = $event.key"/>
      </ScrollArea>
      <ScrollArea class="flex-1">
        <div class="p-4">
          <!-- 右侧内容区域 -->
          <div v-show="activeNavKey === 'basic'" class="space-y-6">
            <BasicForm :room-detail="roomDetail1" ref="basicRef" @save="handleSaveBasic"></BasicForm>
          </div>
          <div v-show="activeNavKey === 'participants'" class="space-y-6">
            <Participants ref="participantsRef" :room-detail="roomDetail1"></Participants>
          </div>
          <div v-show="activeNavKey === 'courseware'" class="space-y-6">
            <Courseware ref="courseRef" :room-detail="roomDetail1"></Courseware>
          </div>
          <div v-show="activeNavKey === 'setting'" class="space-y-6">
            <Setting :room-detail="roomDetail1" ref="settingRef"></Setting>
          </div>
          <div v-show="activeNavKey === 'record'" class="space-y-6">
            <Record :room-detail="roomDetail1" ref="recordRef"></Record>
          </div>
          <!--      <Button @click="handleSave" class="mt-6">Save</Button>-->
        </div>
      </ScrollArea>



<!--      <ContainerWrapper :border="false" :shadow="false" :width-handle="false" class="flex-1 min-h-0">-->
<!--        <template #nav>-->
<!--          <div class="flex flex-col h-full">-->
<!--            <ScrollArea class="flex-1">-->
<!--              <div class="p-4">-->
<!--                <ContainerNav :is-collapsed="false" :links="liveDetailSideNav" @changeNav="($event) => activeNavKey = $event.key"/>-->
<!--              </div>-->
<!--            </ScrollArea>-->
<!--          </div>-->
<!--        </template>-->
<!--        <template #detail>-->
<!--          <div class="flex flex-col h-full">-->
<!--            <ScrollArea class="flex-1">-->
<!--              <div class="p-4">-->
<!--                &lt;!&ndash; 右侧内容区域 &ndash;&gt;-->
<!--                <div v-show="activeNavKey === 'basic'" class="space-y-6">-->
<!--                  <BasicForm :room-detail="roomDetail1" ref="basicRef" @save="handleSaveBasic"></BasicForm>-->
<!--                </div>-->
<!--                <div v-show="activeNavKey === 'participants'" class="space-y-6">-->
<!--                  <Participants ref="participantsRef" :room-detail="roomDetail1"></Participants>-->
<!--                </div>-->
<!--                <div v-show="activeNavKey === 'courseware'" class="space-y-6">-->
<!--                  <Courseware ref="courseRef" :room-detail="roomDetail1"></Courseware>-->
<!--                </div>-->
<!--                <div v-show="activeNavKey === 'setting'" class="space-y-6">-->
<!--                  <Setting :room-detail="roomDetail1" ref="settingRef"></Setting>-->
<!--                </div>-->
<!--                <div v-show="activeNavKey === 'record'" class="space-y-6">-->
<!--                  <Record :room-detail="roomDetail1" ref="recordRef"></Record>-->
<!--                </div>-->
<!--                &lt;!&ndash;      <Button @click="handleSave" class="mt-6">Save</Button>&ndash;&gt;-->
<!--              </div>-->
<!--            </ScrollArea>-->
<!--          </div>-->
<!--        </template>-->
<!--      </ContainerWrapper>-->
    </div>


    <!-- 侧边导航栏 -->
<!--    <ScrollArea class="p-4 w-1/5">-->
<!--      <ContainerNav :is-collapsed="false" :links="liveDetailSideNav" @changeNav="($event) => activeNavKey = $event.key"/>-->
<!--    </ScrollArea>-->
<!--      <Separator orientation="vertical"/>-->

<!--      <nav class="flex flex-col space-y-1">-->
<!--        <Button-->
<!--          v-for="item in liveDetailSideNav"-->
<!--          :key="item.key"-->
<!--          variant="ghost"-->
<!--          class="justify-start w-full text-left"-->
<!--          :class="-->
<!--            cn(activeNavKey === item.key && 'bg-muted hover:bg-muted w-full text-left justify-start')-->
<!--          "-->
<!--          @click="activeNavKey = item.key"-->
<!--        >-->
<!--          {{ item.name }}-->
<!--        </Button>-->
<!--      </nav>-->

</template>
