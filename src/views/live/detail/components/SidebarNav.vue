<script setup lang="ts">
import BasicForm from './BasicForm.vue'
import Courseware from './Courseware.vue'
import Participants from './Participants.vue'
import Setting from './Setting.vue'
import Record from './Record.vue'
import { ref } from 'vue'
import { cn } from '@/lib/utils'
import { LiveApi, DeptAndUserMixedVO, RoomCreateOrUpdateOrPublishReqVO } from '@/api/live/stream'
import { UploadResult } from '@/components/SuperUpload/src/config'
import { getLocalTimeZone } from '@internationalized/date'
import { formatTime } from '@/utils/formatTime'
import { useUserStore } from '@/store/modules/user'
interface Item {
  title: string
  key: string
}
interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
}
const contentLoading = ref(false)
const props = defineProps({
  roomDetail1: {
    type: Object,
    default: () => ({})
  },
  isEditMode: {
    type: Boolean,
    default: false
  }
})

const allSidebarNavItems: Item[] = [
  { title: 'Basic Info', key: 'basic' },
  { title: 'Participants', key: 'participants' },
  { title: 'Courseware', key: 'courseware' },
  { title: 'Live Setting', key: 'setting' },
  { title: 'Record', key: 'record' }
]

// 根据编辑模式过滤侧边栏项目
const sidebarNavItems = computed(() => {
  if (props.isEditMode) {
    return allSidebarNavItems
  } else {
    // 查看模式下不显示 Setting 标签
    return allSidebarNavItems.filter((item) => item.key !== 'setting')
  }
})

const userStore = useUserStore()
const currentUser = computed(() => userStore.user)
const liveBasicForm = reactive<RoomCreateOrUpdateOrPublishReqVO>({
  name: '',
  startTime: '',
  endTime: '',
  privacy: 1,
  cover: '',
  speakerIds: [],
  description: '',
  participantIds: [],
  attachmentIds: [],
  configOptions: []
})
// 记录当前激活的项
const activeItem = ref('basic')
const { query } = useRoute()
const id = query.id

// 处理 tab 查询参数
if (query.tab && typeof query.tab === 'string') {
  const validTabs = ['basic', 'participants', 'courseware', 'setting', 'record']
  if (validTabs.includes(query.tab)) {
    activeItem.value = query.tab
  }
}
const settingRef = ref()
const courseRef = ref()
const participantsRef = ref()
const basicRef = ref()
const { push } = useRouter()
const emit = defineEmits(['change'])
const handleSave = async () => {
  try {
    contentLoading.value = true
    await LiveApi.updateRoom({
      id: id,
      ...basicRef.value.liveBasicForm,
      // participantIds: participantsRef.value.invitees.map((item: DepartmentAndUserData) => item.id),
      attachmentIds: courseRef.value.fileList.map((item: UploadResult) => item.id),
      configOptions: settingRef.value.configList,
      privacy: settingRef.value.privacy ? 1 : 2,
      startTime: formatTime(basicRef.value.startDate.toDate(getLocalTimeZone())),
      endTime: formatTime(basicRef.value.endDate.toDate(getLocalTimeZone()))
    })
    handleReturn()
  } finally {
    contentLoading.value = false
  }
}
const handleReturn = () => {
  push('/live/center/my')
}

// 暴露组件引用给父组件
defineExpose({
  basicRef,
  participantsRef,
  courseRef,
  settingRef
})
const handleSaveBasic = (liveBasic: RoomCreateOrUpdateOrPublishReqVO) => {
  liveBasicForm.cover = liveBasic.cover
  liveBasicForm.name = liveBasic.name
  liveBasicForm.startTime = liveBasic.startTime
  emit('change', liveBasicForm)
}
</script>

<template>
  <div class="flex flex-col gap-6 lg:flex-row lg:gap-8 lg:items-start">
    <!-- 侧边导航栏 -->
    <div class="w-full lg:w-48 lg:flex-shrink-0">
      <nav class="flex flex-col gap-2 p-4 bg-gray-50 rounded-lg lg:bg-transparent lg:p-0">
        <Button
          v-for="item in sidebarNavItems"
          :key="item.key"
          variant="ghost"
          class="justify-start text-left h-11 px-4 py-2 w-full font-medium transition-colors"
          :class="
            cn(
              activeItem === item.key
                ? 'bg-primary/10 text-primary border-r-2 border-primary lg:border-r-0 lg:bg-muted hover:bg-primary/15 lg:hover:bg-muted'
                : 'hover:bg-gray-100 lg:hover:bg-muted/50'
            )
          "
          @click="activeItem = item.key"
        >
          {{ item.title }}
        </Button>
      </nav>
    </div>
    <div class="flex-1 lg:max-w-4xl">
      <!-- 右侧内容区域 -->
      <div class="bg-white rounded-lg border shadow-sm min-h-[500px]">
        <div v-show="activeItem === 'basic'" class="p-6">
          <BasicForm
            :room-detail="roomDetail1"
            :is-edit-mode="isEditMode"
            ref="basicRef"
            @save="handleSaveBasic"
          ></BasicForm>
        </div>
        <div v-show="activeItem === 'participants'" class="p-6">
          <Participants
            ref="participantsRef"
            :room-detail="roomDetail1"
            :is-edit-mode="isEditMode"
          ></Participants>
        </div>
        <div v-show="activeItem === 'courseware'" class="p-6">
          <Courseware
            ref="courseRef"
            :room-detail="roomDetail1"
            :is-edit-mode="isEditMode"
          ></Courseware>
        </div>
        <div v-if="activeItem === 'setting' && isEditMode" class="p-6">
          <Setting :room-detail="roomDetail1" ref="settingRef"></Setting>
        </div>
        <div v-show="activeItem === 'record'" class="p-6">
          <Record :room-detail="roomDetail1" ref="recordRef"></Record>
        </div>
      </div>
      <!--            <Button @click="handleSave" class="mt-6">Save</Button>-->
    </div>
  </div>
</template>
