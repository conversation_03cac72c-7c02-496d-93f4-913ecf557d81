<template>
  <div
    class="group relative overflow-hidden rounded-lg border bg-card hover:shadow-lg transition-all duration-200 cursor-pointer"
    @click="handleClick"
  >
    <!-- Image Area -->
    <div class="h-32 relative overflow-hidden bg-gradient-to-br from-slate-100 to-slate-200">
      <LazyImage
        :src="journey.cover"
        :alt="journey?.title"
        :aspect-ratio="'16/9'"
        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
      />

      <!-- Status Badge -->
      <div class="absolute top-2 right-2">
        <Badge
          :variant="journey.completed ? 'default' : 'secondary'"
          class="text-xs font-medium px-2 py-1"
        >
          {{ journey.completed ? 'Completed' : 'In Progress' }}
        </Badge>
      </div>

      <!-- Course Count Badge -->
      <div class="absolute top-2 left-2">
        <Badge variant="outline" class="text-xs font-medium px-2 py-1 bg-white/90">
          {{ journey.courseCount }} Courses
        </Badge>
      </div>
    </div>

    <!-- Content Area -->
    <div class="p-4">
      <!-- Title -->
      <h3 class="font-medium text-sm mb-2 line-clamp-2 group-hover:text-primary transition-colors">
        {{ journey.title || 'Untitled Journey' }}
      </h3>

      <!-- Description -->
      <p v-if="journey.introduction" class="text-xs text-muted-foreground mb-3 line-clamp-2">
        {{ journey.introduction }}
      </p>

      <!-- Metadata -->
      <div class="flex items-center justify-between text-xs text-muted-foreground">
        <div class="flex items-center gap-2">
          <div v-if="journey.courseDuration" class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ journey.courseDuration }}</span>
          </div>
          <span v-if="journey.courseDuration && journey.studentCount">•</span>
          <div v-if="journey.studentCount" class="flex items-center gap-1">
            <Users class="w-3 h-3" />
            <span>{{ journey.studentCount }} Students</span>
          </div>
        </div>
        <div class="flex items-center gap-1">
          <Eye class="w-3 h-3" />
          <span>View</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Clock, Users, Eye } from 'lucide-vue-next'

interface JourneyItem {
  completed: boolean
  courseCount: number
  courseDuration: string
  courseIds: Array<string>
  cover: string
  id: number
  introduction: string
  keywords: string
  status: number
  studentCount: number
  title: string
}

interface Props {
  journey: JourneyItem
}

interface Emits {
  (e: 'click', journey: JourneyItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('click', props.journey)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
