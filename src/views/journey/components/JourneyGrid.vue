<template>
  <div class="flex flex-col h-full">
    <!-- Journey List -->
    <div v-if="journeyList.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
      <JourneyCard
        v-for="journey in journeyList"
        :key="journey.id"
        :journey="journey"
        @click="handleJourneyClick"
      />
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="min-h-[60vh] flex flex-col items-center justify-center text-center">
      <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
        <Map class="w-12 h-12 text-slate-400" />
      </div>
      <h3 class="text-lg font-semibold text-slate-900 mb-2">No Learning Journeys Found</h3>
      <p class="text-slate-500 max-w-md">
        You don't have any {{ currentTabLabel.toLowerCase() }} learning journeys yet
      </p>
    </div>

    <!-- Loading State -->
    <div v-else class="min-h-[60vh] flex items-center justify-center">
      <div class="flex flex-col items-center gap-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p class="text-sm text-muted-foreground">Loading learning journeys...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import JourneyCard from './JourneyCard.vue'
import { Map } from 'lucide-vue-next'

interface JourneyItem {
  completed: boolean
  courseCount: number
  courseDuration: string
  courseIds: Array<string>
  cover: string
  id: number
  introduction: string
  keywords: string
  status: number
  studentCount: number
  title: string
}

interface Props {
  journeyList: JourneyItem[]
  loading?: boolean
  currentTabLabel?: string
}

interface Emits {
  (e: 'journey-click', journey: JourneyItem): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  currentTabLabel: 'journeys'
})

const emit = defineEmits<Emits>()

// Handle journey click
const handleJourneyClick = (journey: JourneyItem) => {
  emit('journey-click', journey)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
