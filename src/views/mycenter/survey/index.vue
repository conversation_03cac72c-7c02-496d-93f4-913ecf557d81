<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { ContainerScroll } from '@/components/ContainerWrap'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { SmartPagination } from '@/components/SmartPagination'
import { ScrollArea } from '@/components/ui/scroll-area'
import TabNav from '@/components/TabNav.vue'
import SurveyGrid from './components/SurveyGrid.vue'
import { SurveyApi } from '@/api/survey'
import type { SurveyInstanceVO } from '@/api/survey/types'
import { SurveyInstanceStatusEnum } from '@/api/survey/types'

const router = useRouter()

// Survey tabs configuration - 只有一个Tab保持风格统一
const surveyTabs = ref([{ key: 'all', label: 'All Surveys' }])

// Reactive state
const selectedType = ref('all')
const selectedStatus = ref('all')
const loading = ref(false)
const surveyList = ref<SurveyInstanceVO[]>([])
const total = ref(0)

// Query parameters for API calls
const queryParams = reactive({
  name: '',
  categoryId: undefined,
  status: undefined,
  pageNum: 1,
  pageSize: 12
})

// Status filter mapping
const getStatusFromFilter = (filter: string): SurveyInstanceStatusEnum | undefined => {
  switch (filter) {
    case 'pending':
      return SurveyInstanceStatusEnum.IN_PROGRESS
    case 'completed':
      return SurveyInstanceStatusEnum.ENDED
    case 'draft':
      return SurveyInstanceStatusEnum.DRAFT
    case 'all':
    default:
      return undefined
  }
}

// API call to fetch survey list
const getSurveyList = async () => {
  try {
    console.log('=== getSurveyList called ===')
    console.log('Current selectedStatus:', selectedStatus.value)
    console.log('Current queryParams:', queryParams)
    loading.value = true

    // Clean up empty parameters
    const params = { ...queryParams }
    if (!params.name) {
      delete params.name
    }
    if (!params.categoryId) {
      delete params.categoryId
    }

    // Set status filter
    const status = getStatusFromFilter(selectedStatus.value)
    console.log('selectedStatus.value:', selectedStatus.value)
    console.log('mapped status:', status)
    if (status !== undefined) {
      params.status = status
    } else {
      delete params.status
    }

    console.log('Survey API params:', params)

    const response = await SurveyApi.getAvailableInstances(params)
    console.log('Survey API response:', response)
    console.log('Response type:', typeof response)
    console.log('Response keys:', Object.keys(response || {}))

    // 根据实际API响应结构调整数据提取
    // 检查响应结构并适配不同的数据格式
    if (response && typeof response === 'object') {
      if (Array.isArray(response)) {
        // 如果响应直接是数组
        surveyList.value = response
        total.value = response.length
      } else if (response.list !== undefined) {
        // 如果响应有 list 字段 (PageResult 格式)
        surveyList.value = response.list || []
        total.value = response.total || 0
      } else if (response.data !== undefined) {
        // 如果响应有 data 字段
        const data = response.data
        if (Array.isArray(data)) {
          surveyList.value = data
          total.value = data.length
        } else if (data.list !== undefined) {
          surveyList.value = data.list || []
          total.value = data.total || 0
        } else {
          surveyList.value = []
          total.value = 0
        }
      } else {
        // 其他情况，尝试直接使用响应作为列表
        surveyList.value = []
        total.value = 0
      }
    } else {
      surveyList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('=== Survey API Error ===')
    console.error('Error details:', error)
    console.error('Error message:', error?.message)
    console.error('Error response:', error?.response)
    console.error('Error status:', error?.response?.status)
    console.error('Error data:', error?.response?.data)
    surveyList.value = []
    total.value = 0
  } finally {
    loading.value = false
    console.log('=== getSurveyList completed ===')
    console.log('Final surveyList length:', surveyList.value.length)
    console.log('Final total:', total.value)
  }
}

// Event handlers
const handlePageChange = (page: number) => {
  queryParams.pageNum = page
  getSurveyList()
}

const handleChangeType = (type: string, resetPage = false) => {
  selectedType.value = type
  if (resetPage) {
    queryParams.pageNum = 1
  }
  // TODO: Implement category filtering based on type
  getSurveyList()
}

const handleFillSurvey = (survey: SurveyInstanceVO) => {
  console.log('Fill survey clicked:', survey)
  router.push(`/survey/fill/${survey.id}`)
}

const handleViewResult = (survey: SurveyInstanceVO) => {
  console.log('View result clicked:', survey)
  router.push(`/survey/result/${survey.id}`)
}

// Watch for status changes
watch(
  selectedStatus,
  (newStatus, oldStatus) => {
    console.log('=== Survey Status Change ===')
    console.log('Old status:', oldStatus)
    console.log('New status:', newStatus)
    console.log('selectedStatus.value:', selectedStatus.value)
    console.log('Mapped status:', getStatusFromFilter(newStatus))

    // Skip initialization
    if (oldStatus === undefined) {
      console.log('Skipping initialization (oldStatus is undefined)')
      return
    }

    console.log('Triggering getSurveyList due to status change')
    // Reset to first page when status changes
    queryParams.pageNum = 1
    getSurveyList()
  },
  { immediate: false }
)

// Initialize data on component mount
onMounted(() => {
  console.log('My Surveys component mounted')
  getSurveyList()
})

// Refresh data when component is activated (for keep-alive)
onActivated(() => {
  getSurveyList()
})

// Emit pagination updates to parent component
const emit = defineEmits<{
  'update-pagination': [data: { total: number; currentPage: number; pageSize: number }]
}>()

// Pagination data for UI
const paginationData = computed(() => ({
  total: total.value,
  currentPage: queryParams.pageNum,
  pageSize: queryParams.pageSize
}))

// Watch pagination data and emit updates
watch(
  paginationData,
  (newData) => {
    emit('update-pagination', newData)
  },
  { immediate: true }
)

// Computed property for current tab label
const currentTabLabel = computed(() => {
  return 'Surveys'
})
</script>

<template>
  <ContainerScroll>
    <!-- Header with Status Filter -->
    <template #header>
      <div class="px-6 py-3 w-full h-full flex items-center">
        <div class="flex items-center justify-between w-full">
          <TabNav
            :tabs="surveyTabs"
            :active-tab="selectedType"
            @update:active-tab="($event: string) => handleChangeType($event, true)"
          />

          <!-- Status Filter in Top Right -->
          <!--          <ToggleGroup v-model="selectedStatus" type="single" size="sm" class="border rounded-lg">-->
          <!--            <ToggleGroupItem value="all" class="text-xs">All</ToggleGroupItem>-->
          <!--            <ToggleGroupItem value="pending" class="text-xs">Pending</ToggleGroupItem>-->
          <!--            <ToggleGroupItem value="completed" class="text-xs">Completed</ToggleGroupItem>-->
          <!--            <ToggleGroupItem value="draft" class="text-xs">Draft</ToggleGroupItem>-->
          <!--          </ToggleGroup>-->
        </div>
      </div>
    </template>

    <!-- Scrollable Content -->
    <div class="flex-1 overflow-hidden">
      <ScrollArea class="h-full">
        <div class="p-6">
          <SurveyGrid
            :survey-list="surveyList"
            :loading="loading"
            :current-tab-label="currentTabLabel"
            @fill-survey="handleFillSurvey"
            @view-result="handleViewResult"
          />
        </div>
      </ScrollArea>
    </div>

    <!-- Fixed Pagination at Bottom -->
    <template #footer v-if="total > 0">
      <div class="px-6 py-4 w-full h-full flex items-center">
        <SmartPagination
          :total="total"
          :current-page="queryParams.pageNum"
          :page-size="queryParams.pageSize"
          @current-change="handlePageChange"
        />
      </div>
    </template>
  </ContainerScroll>
</template>

<style scoped lang="scss"></style>
