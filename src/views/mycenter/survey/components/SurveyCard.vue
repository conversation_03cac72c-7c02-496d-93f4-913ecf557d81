<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  Eye,
  ClipboardList,
  FileText,
  Target,
  BarChart
} from 'lucide-vue-next'
import type { SurveyInstanceVO } from '@/api/survey/types'
import { SurveyInstanceStatusEnum } from '@/api/survey/types'
import { SurveyApi } from '@/api/survey'

interface Props {
  survey: SurveyInstanceVO
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false
})

const emit = defineEmits<{
  fillSurvey: [survey: SurveyInstanceVO]
  viewResult: [survey: SurveyInstanceVO]
}>()

const router = useRouter()

// 计算属性
const surveyStatus = computed(() => {
  if (props.survey.userSubmissionCount > 0) return 'submitted'
  if (!props.survey.canParticipate) return 'unavailable'
  if (props.survey.status === SurveyInstanceStatusEnum.ENDED) return 'expired'
  if (new Date(props.survey.endTime) < new Date()) return 'expired'
  return 'available'
})

const statusConfig = computed(() => {
  switch (surveyStatus.value) {
    case 'submitted':
      return {
        text: 'Submitted',
        variant: 'default' as const,
        icon: CheckCircle,
        color: 'text-green-600'
      }
    case 'unavailable':
      return {
        text: 'Not Available',
        variant: 'secondary' as const,
        icon: AlertCircle,
        color: 'text-gray-600'
      }
    case 'expired':
      return {
        text: 'Expired',
        variant: 'destructive' as const,
        icon: Clock,
        color: 'text-red-600'
      }
    case 'available':
    default:
      return {
        text: 'Available',
        variant: 'outline' as const,
        icon: Play,
        color: 'text-blue-600'
      }
  }
})

const canParticipate = computed(() => {
  return surveyStatus.value === 'available'
})

const hasSubmitted = computed(() => {
  return surveyStatus.value === 'submitted'
})

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: new Date(dateString).getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
  })
}

// 格式化日期范围
const formatDateRange = (startTime: string, endTime: string) => {
  const start = formatDate(startTime)
  const end = formatDate(endTime)
  return `${start} - ${end}`
}

// Get submission frequency text based on enum value
const getSubmissionFrequencyText = (frequency: number) => {
  return SurveyApi.formatSubmissionFrequency(frequency)
}

// Get survey status text based on enum value
const getSurveyStatusText = (status: number) => {
  return SurveyApi.formatSurveyStatus(status)
}

// Get max submissions text based on submission frequency
const getMaxSubmissionsText = (survey: SurveyInstanceVO) => {
  // If submission frequency is 3 (unlimited), show ∞
  if (survey.submissionFrequency === 3) {
    return '∞'
  }
  // Otherwise show the actual maxSubmissions value
  return survey.maxSubmissions || '∞'
}

// 获取剩余时间
const getRemainingTime = (endTime: string) => {
  const now = new Date()
  const end = new Date(endTime)
  const diff = end.getTime() - now.getTime()

  if (diff <= 0) return 'Ended'

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

  if (days > 0) return `${days}d left`
  if (hours > 0) return `${hours}h left`
  return 'Ending soon'
}

// 处理操作
const handleFillSurvey = () => {
  emit('fillSurvey', props.survey)
}

const handleViewResult = () => {
  emit('viewResult', props.survey)
}

// 处理卡片点击 - 跳转到详情页面
const handleCardClick = () => {
  router.push(`/survey/detail/${props.survey.id}`)
}
</script>

<template>
  <div
    class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group"
  >
    <div class="flex items-center p-4 gap-4">
      <!-- Survey Icon -->
      <div
        class="relative w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg overflow-hidden flex-shrink-0"
      >
        <div class="w-full h-full flex items-center justify-center text-white">
          <div class="text-center">
            <ClipboardList class="w-8 h-8 mx-auto mb-1" />
            <div class="text-xs font-medium">Survey</div>
          </div>
        </div>

        <!-- Status Badge -->
        <div class="absolute top-1 left-1">
          <Badge :variant="statusConfig.variant" class="text-xs font-medium px-1.5 py-0.5">
            {{ statusConfig.text }}
          </Badge>
        </div>

        <!-- Submission Count Badge -->
        <div v-if="survey.userSubmissionCount > 0" class="absolute top-1 right-1">
          <Badge variant="outline" class="text-xs p-1">
            <CheckCircle class="w-3 h-3 text-green-500" />
          </Badge>
        </div>
      </div>

      <!-- Survey Content -->
      <div class="flex-1 min-w-0">
        <!-- Survey Title -->
        <h3
          class="font-semibold text-slate-900 text-base mb-1 group-hover:text-blue-600 transition-colors line-clamp-1"
        >
          {{ survey.name }}
        </h3>

        <!-- Survey Details -->
        <div class="flex items-center gap-4 text-sm mb-2">
          <span class="text-blue-600 font-medium">
            {{ survey.creatorName || 'System' }}
          </span>
          <span :class="statusConfig.color" class="font-medium">
            {{ getSurveyStatusText(survey.status) }}
          </span>
        </div>

        <!-- Survey Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-3">
          <div class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDateRange(survey.startTime, survey.endTime) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Users class="w-3 h-3" />
            <span>{{ survey.responseCount }} / {{ survey.maxResponses || '∞' }} responses</span>
          </div>
          <div class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ getRemainingTime(survey.endTime) }}</span>
          </div>
        </div>

        <!-- Survey Stats -->
        <div class="flex items-center gap-4 text-xs text-slate-400">
          <div class="flex items-center gap-1">
            <FileText class="w-3 h-3" />
            <span>{{ getSubmissionFrequencyText(survey.submissionFrequency) }}</span>
          </div>
          <div v-if="survey.userSubmissionCount > 0" class="flex items-center gap-1">
            <Target class="w-3 h-3" />
            <span
              >submitted {{ survey.userSubmissionCount }} /
              {{ getMaxSubmissionsText(survey) }}</span
            >
          </div>
          <div v-if="survey.allowViewStatistics" class="flex items-center gap-1">
            <BarChart class="w-3 h-3" />
            <span>Statistics available</span>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex-shrink-0">
        <Button
          v-if="canParticipate"
          @click.stop="handleCardClick"
          size="sm"
          class="opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
        >
          <Play class="w-4 h-4 mr-1" />
          Participate
        </Button>

        <Button
          v-else-if="hasSubmitted"
          @click.stop="handleCardClick"
          variant="outline"
          size="sm"
          class="opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
        >
          <Eye class="w-4 h-4 mr-1" />
          View Result
        </Button>

        <Button
          v-else
          disabled
          variant="ghost"
          size="sm"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <AlertCircle class="w-4 h-4 mr-1" />
          Not Available
        </Button>
      </div>
    </div>

    <!-- Description (if available) -->
    <div v-if="survey.description" class="px-4 pb-4">
      <div class="bg-slate-50 rounded-md p-2">
        <p class="text-xs text-slate-600 line-clamp-2">
          {{ survey.description }}
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
