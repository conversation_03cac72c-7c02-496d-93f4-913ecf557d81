<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useSurveyStore } from '@/store/modules/survey'
import SurveyCard from './SurveyCard.vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { ClipboardList } from 'lucide-vue-next'
// import EmptyPlaceholder from '@/components/EmptyPlaceholder/index.vue'
import { SmartPagination } from '@/components/SmartPagination'
import { SurveyInstanceStatusEnum } from '@/api/survey/types'
import type { SurveyInstanceVO } from '@/api/survey/types'

interface Props {
  status: string
  statusFilter?: string
  searchQuery?: string
  parentQueryParams?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  fillSurvey: [surveyId: number]
  viewResult: [surveyId: number]
  viewRecords: []
}>()

const surveyStore = useSurveyStore()

// 本地搜索状态
const localSearchQuery = ref(props.searchQuery || '')
const loading = ref(false)
const categoryFilter = ref('')

// 分页参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 12,
  title: '',
  status: undefined
})

// 问卷状态选项 - 使用枚举类
const statusOptions = ref([
  { value: SurveyInstanceStatusEnum.UNPUBLISHED, label: 'Unpublished' },
  { value: SurveyInstanceStatusEnum.IN_PROGRESS, label: 'In Progress' },
  { value: SurveyInstanceStatusEnum.ENDED, label: 'Ended' },
  { value: SurveyInstanceStatusEnum.DRAFT, label: 'Draft' }
])

// 直接使用store中的问卷列表和分页信息
const surveyList = computed(() => surveyStore.surveyList)
const total = computed(() => surveyStore.pagination.total)

// 处理搜索
const handleSearch = async () => {
  queryParams.value.pageNum = 1
  loading.value = true
  try {
    await surveyStore.fetchSurveyList({
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      name: queryParams.value.title,
      status: queryParams.value.status
    })
  } catch (error) {
    console.error('Search failed:', error)
  } finally {
    loading.value = false
  }
}

// 处理重置
const handleReset = async () => {
  queryParams.value.pageNum = 1
  queryParams.value.title = ''
  queryParams.value.status = undefined
  localSearchQuery.value = ''

  loading.value = true
  try {
    await surveyStore.fetchSurveyList({
      pageNum: 1,
      pageSize: queryParams.value.pageSize
    })
  } catch (error) {
    console.error('Reset failed:', error)
  } finally {
    loading.value = false
  }
}

// 处理分页
const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNum = newPage
}

// 处理问卷操作
const handleFillSurvey = (survey: SurveyInstanceVO) => {
  emit('fillSurvey', survey.id)
}

const handleViewResult = (survey: SurveyInstanceVO) => {
  emit('viewResult', survey.id)
}

const handleViewRecords = () => {
  emit('viewRecords')
}

// 获取问卷状态文本
const getSurveyStatusText = (survey: SurveyInstanceVO) => {
  if (survey.userSubmissionCount > 0) return 'Submitted'
  if (!survey.canParticipate) return 'Not Available'
  if (new Date(survey.endTime) < new Date()) return 'Expired'
  return 'Available'
}

// 获取问卷状态颜色
const getSurveyStatusColor = (survey: SurveyInstanceVO) => {
  if (survey.userSubmissionCount > 0) return 'default'
  if (!survey.canParticipate) return 'secondary'
  if (new Date(survey.endTime) < new Date()) return 'destructive'
  return 'default'
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

// 监听props变化
watch(
  () => props.searchQuery,
  (newQuery) => {
    localSearchQuery.value = newQuery || ''
  }
)

// 获取数据
onMounted(async () => {
  if (surveyStore.surveyList.length === 0) {
    loading.value = true
    try {
      await surveyStore.fetchSurveyList()
    } catch (error) {
      console.error('Failed to fetch surveys:', error)
    } finally {
      loading.value = false
    }
  }
})
</script>

<template>
  <!-- 搜索表单 -->
  <div class="flex pt-5 space-x-4">
    <div class="flex items-center space-x-2">
      <Label class="me-2">Survey Name</Label>
      <Input
        v-model="queryParams.title"
        placeholder="Please input survey name"
        class="w-[320px]"
        clearable
        @keydown.enter="handleSearch"
      />
      <Label class="mx-2">Status</Label>

      <!--      <Select v-model="queryParams.status">-->
      <!--        <SelectTrigger class="w-[320px]">-->
      <!--          <SelectValue placeholder="Select status" />-->
      <!--        </SelectTrigger>-->
      <!--        <SelectContent>-->
      <!--          <SelectGroup>-->
      <!--            <SelectItem v-for="item in statusOptions" :key="item.value" :value="item.value">-->
      <!--              {{ item.label }}-->
      <!--            </SelectItem>-->
      <!--          </SelectGroup>-->
      <!--        </SelectContent>-->
      <!--      </Select>-->
      <div class="space-x-2">
        <Button @click="handleSearch">Search</Button>
        <Button @click="handleReset" variant="outline">Reset</Button>
      </div>
    </div>
  </div>

  <!-- 问卷列表 -->
  <div class="pt-5 min-h-[480px]">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center min-h-[300px]">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="text-gray-500 mt-2 text-sm">Loading surveys...</p>
      </div>
    </div>
    <!-- 问卷网格 -->
    <div v-else class="grid grid-cols-4 gap-6 mb-4">
      <div v-for="survey in surveyList" :key="survey.id">
        <SurveyCard
          :survey="survey"
          :compact="true"
          @fill-survey="handleFillSurvey"
          @view-result="handleViewResult"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div
      v-if="!loading && surveyList.length === 0"
      class="flex justify-center items-center min-h-[300px]"
    >
      <div class="text-center">
        <ClipboardList class="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">No surveys found</h3>
        <p class="text-gray-500">
          {{
            props.status === 'available'
              ? 'No available surveys at the moment'
              : props.status === 'submitted'
                ? "You haven't submitted any surveys yet"
                : 'No surveys match your search criteria'
          }}
        </p>
      </div>
    </div>

    <!-- 分页 -->
    <SmartPagination
      v-if="!loading && total > 0"
      :current-page="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss"></style>
