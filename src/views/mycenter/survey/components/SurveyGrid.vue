<template>
  <div class="flex-1 overflow-hidden">
    <!-- Survey List -->
    <div v-if="surveyList.length > 0" class="space-y-3">
      <SurveyCard
        v-for="survey in surveyList"
        :key="survey.id"
        :survey="survey"
        @fill-survey="handleFillSurvey"
        @view-result="handleViewResult"
      />
    </div>

    <!-- Empty State -->
    <div
      v-else-if="!loading"
      class="min-h-[60vh] flex flex-col items-center justify-center text-center"
    >
      <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
        <ClipboardList class="w-12 h-12 text-slate-400" />
      </div>
      <h3 class="text-lg font-semibold text-slate-900 mb-2">No Surveys Found</h3>
      <p class="text-slate-500 max-w-md">
        You don't have any {{ currentTabLabel.toLowerCase() }} surveys yet or there are no surveys available
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-3">
      <div
        v-for="i in 6"
        :key="i"
        class="bg-white rounded-lg border border-slate-200 overflow-hidden animate-pulse"
      >
        <div class="flex items-center p-4 gap-4">
          <div class="w-24 h-24 bg-slate-200 rounded-lg"></div>
          <div class="flex-1 space-y-3">
            <div class="h-4 bg-slate-200 rounded w-3/4"></div>
            <div class="h-3 bg-slate-200 rounded w-1/2"></div>
            <div class="h-3 bg-slate-200 rounded w-2/3"></div>
          </div>
          <div class="w-20 h-8 bg-slate-200 rounded"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ClipboardList } from 'lucide-vue-next'
import SurveyCard from './SurveyCard.vue'
import type { SurveyInstanceVO } from '@/api/survey/types'

interface Props {
  surveyList: SurveyInstanceVO[]
  loading: boolean
  currentTabLabel?: string
}

interface Emits {
  (e: 'fill-survey', survey: SurveyInstanceVO): void
  (e: 'view-result', survey: SurveyInstanceVO): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  currentTabLabel: 'surveys'
})

const emit = defineEmits<Emits>()

const handleFillSurvey = (survey: SurveyInstanceVO) => {
  emit('fill-survey', survey)
}

const handleViewResult = (survey: SurveyInstanceVO) => {
  emit('view-result', survey)
}
</script>

<style scoped lang="scss"></style>
