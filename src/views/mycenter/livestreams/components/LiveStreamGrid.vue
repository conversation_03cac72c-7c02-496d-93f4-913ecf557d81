<template>
  <div class="flex-1 overflow-hidden">
    <!-- Live Stream List -->
    <div v-if="livestreamList.length > 0" class="space-y-3">
      <LiveStreamCard
        v-for="livestream in livestreamList"
        :key="livestream.id"
        :livestream="livestream"
        @click="handleLiveStreamClick"
      />
    </div>

    <!-- Empty State -->
    <div
      v-else-if="!loading"
      class="min-h-[60vh] flex flex-col items-center justify-center text-center"
    >
      <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
        <Video class="w-12 h-12 text-slate-400" />
      </div>
      <h3 class="text-lg font-semibold text-slate-900 mb-2">No Live Streams Found</h3>
      <p class="text-slate-500 max-w-md">
        You haven't subscribed to any live streams yet or there are no live streams available
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-3">
      <div
        v-for="i in 6"
        :key="i"
        class="bg-white rounded-lg border border-slate-200 overflow-hidden animate-pulse"
      >
        <div class="flex items-center p-4 gap-4">
          <div class="w-24 h-24 bg-slate-200 rounded-lg"></div>
          <div class="flex-1 space-y-3">
            <div class="h-4 bg-slate-200 rounded w-3/4"></div>
            <div class="h-3 bg-slate-200 rounded w-1/2"></div>
            <div class="h-3 bg-slate-200 rounded w-2/3"></div>
          </div>
          <div class="w-20 h-8 bg-slate-200 rounded"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Video } from 'lucide-vue-next'
import LiveStreamCard from './LiveStreamCard.vue'
import { LiveRoomRespVO } from '@/api/mycenter/myrecords/livestream'

interface Props {
  livestreamList: LiveRoomRespVO[]
  loading: boolean
}

interface Emits {
  (e: 'livestream-click', livestream: LiveRoomRespVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleLiveStreamClick = (livestream: LiveRoomRespVO) => {
  emit('livestream-click', livestream)
}
</script>

<style scoped lang="scss"></style>
