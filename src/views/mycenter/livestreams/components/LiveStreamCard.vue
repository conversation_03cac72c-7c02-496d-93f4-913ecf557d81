<template>
  <div
    class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group cursor-pointer"
    @click="emit('click', livestream)"
  >
    <div class="flex items-center p-4 gap-4">
      <!-- Live Stream Cover -->
      <div
        class="relative w-24 h-24 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg overflow-hidden flex-shrink-0"
      >
        <img
          v-if="livestream.cover"
          :src="livestream.cover"
          :alt="livestream.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div v-else class="w-full h-full flex items-center justify-center text-white">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ livestream.name.charAt(0) }}</div>
          </div>
        </div>

        <!-- Live Status Badge -->
        <div class="absolute top-1 left-1">
          <Badge
            :variant="getStatusVariant(livestream.status)"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ getStatusLabel(livestream.status) }}
          </Badge>
        </div>

        <!-- Reserved Badge -->
        <div v-if="livestream.reserved" class="absolute top-1 right-1">
          <Badge variant="outline" class="text-xs p-1">
            <Heart class="w-3 h-3 text-red-500" />
          </Badge>
        </div>
      </div>

      <!-- Live Stream Content -->
      <div class="flex-1 min-w-0">
        <!-- Live Stream Title -->
        <h3
          class="font-semibold text-slate-900 text-base mb-1 group-hover:text-blue-600 transition-colors line-clamp-1"
        >
          {{ livestream.name }}
        </h3>

        <!-- Speaker and Status -->
        <div class="flex items-center gap-4 text-sm mb-2">
          <span class="text-slate-600"> by {{ livestream.nickname }} </span>
          <span :class="getStatusColor(livestream.status)" class="font-medium">
            {{ livestream.statusName }}
          </span>
        </div>

        <!-- Live Stream Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-3">
          <div class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDate(livestream.startTime) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Users class="w-3 h-3" />
            <span>{{ livestream.reservationTotal }} reserved</span>
          </div>
          <div class="flex items-center gap-1">
            <Globe class="w-3 h-3" />
            <span>{{ livestream.joinTypeName }}</span>
          </div>
        </div>

        <!-- Speakers List -->
        <div
          v-if="livestream.speakers && livestream.speakers.length > 0"
          class="flex items-center gap-2 text-xs text-slate-400"
        >
          <span>Speakers:</span>
          <div class="flex -space-x-1">
            <div
              v-for="speaker in livestream.speakers.slice(0, 3)"
              :key="speaker.userId"
              class="w-5 h-5 rounded-full bg-slate-200 border border-white flex items-center justify-center"
              :title="speaker.nickname"
            >
              <img
                v-if="speaker.avatar"
                :src="speaker.avatar"
                :alt="speaker.nickname"
                class="w-full h-full rounded-full object-cover"
              />
              <span v-else class="text-xs font-medium text-slate-600">
                {{ speaker.nickname.charAt(0) }}
              </span>
            </div>
            <div
              v-if="livestream.speakers.length > 3"
              class="w-5 h-5 rounded-full bg-slate-300 border border-white flex items-center justify-center text-xs text-slate-600"
            >
              +{{ livestream.speakers.length - 3 }}
            </div>
          </div>
        </div>
      </div>

      <!-- Action Button -->

      <!--      <div class="flex-shrink-0">-->
      <!--        <Button-->
      <!--          :variant="getActionVariant(livestream.status)"-->
      <!--          size="sm"-->
      <!--          @click.stop="emit('click', livestream)"-->
      <!--          class="opacity-0 group-hover:opacity-100 transition-opacity"-->
      <!--        >-->
      <!--          <component :is="getActionIcon(livestream.status)" class="w-4 h-4 mr-1" />-->
      <!--          {{ getActionLabel(livestream.status) }}-->
      <!--        </Button>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Calendar, Users, Globe, Heart, Play, Eye, Clock, CheckCircle } from 'lucide-vue-next'
import { LiveRoomRespVO, LiveStatusEnum } from '@/api/mycenter/myrecords/livestream'
import dayjs from 'dayjs'

interface Props {
  livestream: LiveRoomRespVO
}

interface Emits {
  (e: 'click', livestream: LiveRoomRespVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Format date
const formatDate = (date: Date | string) => {
  if (!date) return ''
  return dayjs(date).format('MMM DD, YYYY HH:mm')
}

// Get status label
const getStatusLabel = (status: LiveStatusEnum) => {
  const statusMap: Record<LiveStatusEnum, string> = {
    [LiveStatusEnum.TO_BE_RELEASED]: 'Upcoming',
    [LiveStatusEnum.TO_BE_BROADCAST]: 'Scheduled',
    [LiveStatusEnum.LIVE_BROADCAST]: 'Live',
    [LiveStatusEnum.CLOSED]: 'Ended'
  }
  return statusMap[status] || 'Unknown'
}

// Get status variant
const getStatusVariant = (status: LiveStatusEnum) => {
  const variantMap: Record<LiveStatusEnum, string> = {
    [LiveStatusEnum.TO_BE_RELEASED]: 'secondary',
    [LiveStatusEnum.TO_BE_BROADCAST]: 'default',
    [LiveStatusEnum.LIVE_BROADCAST]: 'destructive',
    [LiveStatusEnum.CLOSED]: 'outline'
  }
  return variantMap[status] || 'secondary'
}

// Get status color
const getStatusColor = (status: LiveStatusEnum) => {
  const colorMap: Record<LiveStatusEnum, string> = {
    [LiveStatusEnum.TO_BE_RELEASED]: 'text-gray-500',
    [LiveStatusEnum.TO_BE_BROADCAST]: 'text-blue-500',
    [LiveStatusEnum.LIVE_BROADCAST]: 'text-red-500',
    [LiveStatusEnum.CLOSED]: 'text-green-500'
  }
  return colorMap[status] || 'text-gray-500'
}

// Get action button properties
const getActionVariant = (status: LiveStatusEnum) => {
  return status === LiveStatusEnum.LIVE_BROADCAST ? 'default' : 'ghost'
}

const getActionIcon = (status: LiveStatusEnum) => {
  const iconMap: Record<LiveStatusEnum, any> = {
    [LiveStatusEnum.TO_BE_RELEASED]: Clock,
    [LiveStatusEnum.TO_BE_BROADCAST]: Calendar,
    [LiveStatusEnum.LIVE_BROADCAST]: Play,
    [LiveStatusEnum.CLOSED]: Eye
  }
  return iconMap[status] || Eye
}

const getActionLabel = (status: LiveStatusEnum) => {
  const labelMap: Record<LiveStatusEnum, string> = {
    [LiveStatusEnum.TO_BE_RELEASED]: 'Reserve',
    [LiveStatusEnum.TO_BE_BROADCAST]: 'Join',
    [LiveStatusEnum.LIVE_BROADCAST]: 'Watch Live',
    [LiveStatusEnum.CLOSED]: 'View Recording'
  }
  return labelMap[status] || 'View'
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
