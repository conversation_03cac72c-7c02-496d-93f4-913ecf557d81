<template>
  <div class="flex flex-col h-full">
    <!-- Course List -->
    <div v-if="courseList.length > 0" class="space-y-3">
      <CourseCard
        v-for="course in courseList"
        :key="course.id"
        :course="course"
        @click="handleCourseClick"
      />
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="flex flex-col items-center justify-center py-16 text-center">
      <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
        <BookOpen class="w-12 h-12 text-slate-400" />
      </div>
      <h3 class="text-lg font-semibold text-slate-900 mb-2">No Courses Found</h3>
      <p class="text-slate-500 max-w-md"> You haven't enrolled in any courses yet </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-3">
      <div
        v-for="i in 8"
        :key="i"
        class="bg-white rounded-lg border border-slate-200 overflow-hidden animate-pulse"
      >
        <div class="flex items-center p-4 gap-4">
          <div class="w-24 h-24 bg-slate-200 rounded-lg"></div>
          <div class="flex-1 space-y-3">
            <div class="h-4 bg-slate-200 rounded w-3/4"></div>
            <div class="h-3 bg-slate-200 rounded w-1/2"></div>
            <div class="h-3 bg-slate-200 rounded w-2/3"></div>
          </div>
          <div class="w-20 h-8 bg-slate-200 rounded"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, onMounted, computed, reactive, ref, toRefs, onActivated } from 'vue'
import { CourseApi } from '@/api/course/content'
import { SmartPagination } from '@/components/SmartPagination'
import { BookOpen } from 'lucide-vue-next'
import CourseCard from './CourseCard.vue'

// Props interface
interface Props {
  type: number
  statusFilter?: string
  searchQuery?: string
  parentQueryParams?: any
  courseTypeFilter?: string
  typeFilter?: string
  showStats?: boolean
  activeContentTab?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 1,
  statusFilter: 'all',
  searchQuery: '',
  courseTypeFilter: 'mandatory',
  typeFilter: '',
  showStats: true,
  activeContentTab: 'all',
  parentQueryParams: () => ({
    pageNum: 1,
    pageSize: 12
  })
})

const emit = defineEmits<{
  'course-click': [course: any]
  'stats-update': [stats: any]
  'update-pagination': [data: { total: number; currentPage: number; pageSize: number }]
}>()

// Reactive data
const queryParams = ref({
  name: '',
  topicId: '',
  pageNum: 1,
  pageSize: 12,
  type: 1,
  status: ''
})

const courseList = ref([])
const total = ref(0)
const loading = ref(false)

// Computed properties
const stats = computed(() => {
  const completed = courseList.value.filter((course) => course.studyStatus === 2).length
  const inProgress = courseList.value.filter((course) => course.studyStatus === 1).length
  const totalCourses = courseList.value.length
  const completionRate = totalCourses > 0 ? Math.round((completed / totalCourses) * 100) : 0

  return {
    total: totalCourses,
    completed,
    inProgress,
    completionRate
  }
})

// Methods
const getCourseList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams.value,
      name: props.searchQuery || queryParams.value.name,
      type: props.type
    }

    // Apply status filter
    if (props.statusFilter && props.statusFilter !== 'all') {
      switch (props.statusFilter) {
        case 'completed':
          params.status = '2'
          break
        case 'in-progress':
          params.status = '1'
          break
        case 'pending':
          params.status = '0'
          break
      }
    }

    try {
      const res = await CourseApi.listMycenterCourse(params)
      courseList.value = res.list || []
      total.value = res.total || 0
    } catch (apiError) {
      console.warn('API call failed, using mock data:', apiError)
      // 使用模拟数据进行展示
      courseList.value = getMockCourses()
      total.value = courseList.value.length
    }

    // Emit stats update
    emit('stats-update', stats.value)

    // Emit pagination update
    emit('update-pagination', {
      total: total.value,
      currentPage: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize
    })
  } catch (error) {
    console.error('Failed to fetch courses:', error)
    courseList.value = getMockCourses()
    total.value = courseList.value.length
  } finally {
    loading.value = false
  }
}

// Mock course data
const getMockCourses = () => [
  {
    id: 1,
    name: 'TypeScript Quick Start',
    cover: '',
    type: 'free',
    progress: 0,
    duration: 22,
    studyCount: 101,
    level: 1,
    category: 'Frontend Development',
    noteCount: 0,
    codeCount: 0,
    qaCount: 0
  },
  {
    id: 2,
    name: 'Product Manager Experience Course',
    cover: '',
    type: 'practical',
    progress: 6,
    duration: 12,
    studyCount: 102,
    level: 2,
    category: 'Product Design',
    noteCount: 1,
    codeCount: 0,
    qaCount: 0
  },
  {
    id: 3,
    name: 'IoT Application Development Basics',
    cover: '',
    type: 'free',
    progress: 0,
    duration: 3,
    studyCount: 101,
    level: 1,
    category: 'Internet of Things',
    noteCount: 0,
    codeCount: 0,
    qaCount: 0
  },
  {
    id: 4,
    name: 'Data Platform: Integration & Eliminating Data Silos',
    cover: '',
    type: 'system',
    progress: 15,
    duration: 45,
    studyCount: 89,
    level: 3,
    category: 'Data Science',
    noteCount: 2,
    codeCount: 1,
    qaCount: 3
  }
]

const handleCourseClick = (course: any) => {
  emit('course-click', course)
}

const handleCurrentChange = (page: number) => {
  queryParams.value.pageNum = page
  getCourseList()
}

// Watchers
watch(
  () => props.type,
  () => {
    queryParams.value.pageNum = 1
    getCourseList()
  },
  { immediate: true }
)

watch(
  () => props.searchQuery,
  () => {
    queryParams.value.pageNum = 1
    getCourseList()
  }
)

watch(
  () => props.statusFilter,
  () => {
    queryParams.value.pageNum = 1
    getCourseList()
  }
)

watch(
  () => props.courseTypeFilter,
  () => {
    queryParams.value.pageNum = 1
    getCourseList()
  }
)

// Lifecycle
onMounted(() => {
  getCourseList()
})

onActivated(() => {
  getCourseList()
})
</script>

<style scoped>
.course-grid {
  display: grid;
  gap: 1rem;
}

@media (min-width: 640px) {
  .course-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .course-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .course-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1280px) {
  .course-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
</style>
