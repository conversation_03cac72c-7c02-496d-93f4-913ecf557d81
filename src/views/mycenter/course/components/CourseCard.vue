<template>
  <div
    class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group"
  >
    <div class="flex items-center p-4 gap-4">
      <!-- Course Image -->
      <div
        class="relative w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg overflow-hidden flex-shrink-0"
      >
        <img
          :src="course.cover || defaultCoverImage"
          :alt="course.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          @error="handleImageError"
        />
        <div v-if="showFallback" class="absolute inset-0 w-full h-full flex items-center justify-center text-white bg-gradient-to-br from-blue-500 to-purple-600">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ course.name.charAt(0).toUpperCase() }}</div>
          </div>
        </div>

        <!-- Course Type Badge -->
        <div class="absolute top-1 left-1">
          <Badge
            :variant="course.type === 0 ? 'secondary' : 'default'"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ getCourseTypeLabel(course.type) }}
          </Badge>
        </div>

        <!-- Certificate & Exam Badges -->
        <div class="absolute top-1 right-1 flex gap-1">
          <Badge v-if="showCertificate" variant="outline" class="text-xs p-1">
            <Award class="w-3 h-3 text-yellow-500" />
          </Badge>
          <Badge v-if="showExam" variant="outline" class="text-xs p-1">
            <FileText class="w-3 h-3 text-blue-500" />
          </Badge>
        </div>
      </div>

      <!-- Course Content -->
      <div class="flex-1 min-w-0">
        <!-- Course Title -->
        <h3
          class="font-semibold text-slate-900 text-base mb-1 group-hover:text-blue-600 transition-colors line-clamp-1"
        >
          {{ course.name }}
        </h3>

        <!-- Progress and Study Status -->
        <div class="flex items-center gap-4 text-sm mb-2">
          <span v-if="course.progress !== undefined" class="text-blue-600 font-medium">
            {{ course.progress }}% Complete
          </span>
          <span :class="getStudyStatus(course.studyStatus).color" class="font-medium">
            {{ getStudyStatus(course.studyStatus).label }}
          </span>
        </div>

        <!-- Course Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-3">
          <div class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ formatCourseDuration(course.duration || course.handDuration) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Users class="w-3 h-3" />
            <span>{{ course.enrollNumber || 0 }} students</span>
          </div>
          <div class="flex items-center gap-1">
            <Target class="w-3 h-3" />
            <span>{{ getCourseLevelLabel(course.level) }}</span>
          </div>
          <div v-if="course.topic" class="flex items-center gap-1">
            <BookOpen class="w-3 h-3" />
            <span>{{ course.topic }}</span>
          </div>
        </div>

        <!-- Course Stats -->
        <div class="flex items-center gap-4 text-xs text-slate-400">
          <div class="flex items-center gap-1">
            <MessageCircle class="w-3 h-3" />
            <span>{{ course.chapterNum || 0 }} chapters</span>
          </div>
          <div class="flex items-center gap-1">
            <FileText class="w-3 h-3" />
            <span>{{ course.examNum || 0 }} exams</span>
          </div>
          <div class="flex items-center gap-1">
            <Heart class="w-3 h-3" />
            <span>{{ Math.round(course.star || 0) }}/5 rating</span>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          @click.stop="handleCourseClick"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <Play class="w-4 h-4 mr-1" />
          View Details
        </Button>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { SvgIcon } from '@/components/SvgIcon'
import {
  Clock,
  Users,
  Target,
  BookOpen,
  MessageCircle,
  Heart,
  Play,
  Award,
  FileText
} from 'lucide-vue-next'
import {
  CourseRespVO,
  CourseTypeEnum,
  StudyStatusEnum,
  formatCourseDuration,
  getCourseLevelLabel,
  getLanguageLabel,
  getCourseTypeLabel,
  getStudyStatusLabel,
  hasCertificate,
  hasExam
} from '@/api/learning/course'

interface Props {
  course: CourseRespVO
}

interface Emits {
  (e: 'click', course: CourseRespVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Router instance
const router = useRouter()

// Default course cover image
const defaultCoverImage = 'https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=Course'
const showFallback = ref(false)

// Handle image loading error
const handleImageError = () => {
  showFallback.value = true
}

// Handle course click - navigate to course detail page
const handleCourseClick = () => {
  // Navigate to course detail page
  router.push({
    name: 'ContentDetail',
    params: { id: props.course.id }
  })
}

// Get study status label and color
const getStudyStatus = (status?: StudyStatusEnum) => {
  const statusMap = {
    [StudyStatusEnum.PENDING]: { label: 'Not Started', color: 'text-gray-500' },
    [StudyStatusEnum.IN_PROGRESS]: { label: 'In Progress', color: 'text-blue-500' },
    [StudyStatusEnum.COMPLETED]: { label: 'Completed', color: 'text-green-500' }
  }
  return statusMap[status as StudyStatusEnum] || { label: 'Not Started', color: 'text-gray-500' }
}

// Check if course has certificate
const showCertificate = computed(() => {
  return hasCertificate(props.course)
})

// Check if course has exam
const showExam = computed(() => {
  return hasExam(props.course)
})
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
