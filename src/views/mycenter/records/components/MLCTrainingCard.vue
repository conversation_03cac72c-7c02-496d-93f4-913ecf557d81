<template>
  <div
    class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group"
  >
    <div class="flex items-center p-4 gap-4">
      <!-- Training Image/Icon -->
      <div
        class="relative w-24 h-24 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg overflow-hidden flex-shrink-0"
      >
        <div class="w-full h-full flex items-center justify-center text-white">
          <div class="text-center">
            <GraduationCap class="w-8 h-8 mx-auto mb-1" />
            <div class="text-xs font-medium">MLC</div>
          </div>
        </div>

        <!-- Status Badge -->
        <div class="absolute top-1 left-1">
          <Badge
            :variant="getStatusVariant(training.studyStatus)"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ getStatusLabel(training.studyStatus) }}
          </Badge>
        </div>

        <!-- Class Type Badge -->
        <div class="absolute top-1 right-1">
          <Badge variant="outline" class="text-xs p-1">
            <Users class="w-3 h-3 text-blue-500" />
          </Badge>
        </div>
      </div>

      <!-- Training Content -->
      <div class="flex-1 min-w-0">
        <!-- Training Title -->
        <h3
          class="font-semibold text-slate-900 text-base mb-1 group-hover:text-blue-600 transition-colors line-clamp-1"
        >
          {{ training.courseName }}
        </h3>

        <!-- Training Details -->
        <div class="flex items-center gap-4 text-sm mb-2">
          <span class="text-blue-600 font-medium">
            {{ training.trainerName }}
          </span>
          <span :class="getStatusColor(training.studyStatus)" class="font-medium">
            {{ getStudyStatusLabel(training.studyStatus) }}
          </span>
        </div>

        <!-- Training Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-3">
          <div class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDate(training.startTime) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ training.trainingDays }} day(s)</span>
          </div>
          <div class="flex items-center gap-1">
            <MapPin class="w-3 h-3" />
            <span>{{ training.classRoomName }}</span>
          </div>
        </div>

        <!-- Training Stats -->
        <div class="flex items-center gap-4 text-xs text-slate-400">
          <div class="flex items-center gap-1">
            <Target class="w-3 h-3" />
            <span>{{ getLanguageLabel(training.language) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <BookOpen class="w-3 h-3" />
            <span>{{ getClassTypeLabel(training.type) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Users class="w-3 h-3" />
            <span>{{ training.assignNum || 0 }} assigned</span>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          @click.stop="emit('click', training)"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <Eye class="w-4 h-4 mr-1" />
          View Details
        </Button>
      </div>
    </div>

    <!-- Progress Bar (if applicable) -->
    <div v-if="training.progress !== undefined" class="px-4 pb-4">
      <div class="w-full bg-slate-200 rounded-full h-1.5">
        <div
          class="bg-green-600 h-1.5 rounded-full transition-all duration-300"
          :style="{ width: `${training.progress}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Calendar,
  Clock,
  MapPin,
  Target,
  BookOpen,
  Award,
  Eye,
  Users,
  GraduationCap
} from 'lucide-vue-next'
import {
  MlcTrainingRespVO,
  ClassLanguageEnum,
  ClassTypeEnum,
  TrainingStudyStatusEnum
} from '@/api/mycenter/myrecords/training'
import dayjs from 'dayjs'

interface Props {
  training: MlcTrainingRespVO
}

interface Emits {
  (e: 'click', training: MlcTrainingRespVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Format date
const formatDate = (date: Date | string) => {
  if (!date) return ''
  return dayjs(date).format('MMM DD, YYYY HH:mm')
}



// Get status label and variant
const getStatusLabel = (status: TrainingStudyStatusEnum) => {
  const statusMap: Record<TrainingStudyStatusEnum, string> = {
    [TrainingStudyStatusEnum.BOOKED]: 'Booked',
    [TrainingStudyStatusEnum.PASSED]: 'Passed',
    [TrainingStudyStatusEnum.FAIL]: 'Failed',
    [TrainingStudyStatusEnum.POSTPONE]: 'Postponed',
    [TrainingStudyStatusEnum.REJECTED]: 'Rejected'
  }
  return statusMap[status] || 'Unknown'
}

const getStatusVariant = (status: TrainingStudyStatusEnum) => {
  const variantMap: Record<TrainingStudyStatusEnum, string> = {
    [TrainingStudyStatusEnum.BOOKED]: 'default',
    [TrainingStudyStatusEnum.PASSED]: 'outline',
    [TrainingStudyStatusEnum.FAIL]: 'destructive',
    [TrainingStudyStatusEnum.POSTPONE]: 'secondary',
    [TrainingStudyStatusEnum.REJECTED]: 'destructive'
  }
  return variantMap[status] || 'secondary'
}

const getStatusColor = (status: TrainingStudyStatusEnum) => {
  const colorMap: Record<TrainingStudyStatusEnum, string> = {
    [TrainingStudyStatusEnum.BOOKED]: 'text-blue-500',
    [TrainingStudyStatusEnum.PASSED]: 'text-green-500',
    [TrainingStudyStatusEnum.FAIL]: 'text-red-500',
    [TrainingStudyStatusEnum.POSTPONE]: 'text-yellow-500',
    [TrainingStudyStatusEnum.REJECTED]: 'text-red-500'
  }
  return colorMap[status] || 'text-gray-500'
}

// Get study status label
const getStudyStatusLabel = (status: number) => {
  return getStatusLabel(status)
}

// Get language label
const getLanguageLabel = (language: ClassLanguageEnum) => {
  const languageMap: Record<ClassLanguageEnum, string> = {
    [ClassLanguageEnum.EN]: 'English',
    [ClassLanguageEnum.AR]: 'Arabic',
    [ClassLanguageEnum.CN]: 'Chinese'
  }
  return languageMap[language] || 'English'
}

// Get class type label
const getClassTypeLabel = (type: ClassTypeEnum) => {
  const typeMap: Record<ClassTypeEnum, string> = {
    [ClassTypeEnum.OFFLINE_CLASS]: 'Offline',
    [ClassTypeEnum.VIRTUAL_CLASS]: 'Virtual',
    [ClassTypeEnum.HYBRID_CLASS]: 'Hybrid'
  }
  return typeMap[type] || 'Virtual'
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
