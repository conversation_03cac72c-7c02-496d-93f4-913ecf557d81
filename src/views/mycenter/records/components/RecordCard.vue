<template>
  <div class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group cursor-pointer" @click="handleClick">
    <div class="flex items-center p-4 gap-4">
      <!-- Record Image/Icon -->
      <div class="relative w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg overflow-hidden flex-shrink-0">
        <img
          v-if="record.cover"
          :src="record.cover"
          :alt="record.name || record.title"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div v-else class="w-full h-full flex items-center justify-center text-white">
          <component :is="getRecordIcon(record.type)" class="w-8 h-8" />
        </div>

        <!-- Record Type Badge -->
        <div class="absolute top-1 left-1">
          <Badge
            :variant="getRecordTypeVariant(record.type)"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ getRecordTypeLabel(record.type) }}
          </Badge>
        </div>

        <!-- Status Badge -->
        <div v-if="record.status" class="absolute top-1 right-1">
          <Badge
            :variant="getStatusVariant(record.status)"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ getStatusLabel(record.status) }}
          </Badge>
        </div>
      </div>

      <!-- Record Content -->
      <div class="flex-1 min-w-0">
        <!-- Record Title -->
        <h3 class="font-semibold text-slate-900 text-base mb-1 group-hover:text-blue-600 transition-colors line-clamp-1">
          {{ record.name || record.title }}
        </h3>

        <!-- Record Progress/Score -->
        <div v-if="record.progress !== undefined || record.score !== undefined" class="flex items-center gap-4 text-sm mb-2">
          <span v-if="record.progress !== undefined" class="text-blue-600 font-medium">
            {{ record.progress }}% Complete
          </span>
          <span v-if="record.score !== undefined" class="text-green-600 font-medium">
            Score: {{ record.score }}
          </span>
        </div>

        <!-- Record Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-2">
          <div v-if="record.duration" class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ formatDuration(record.duration) }}</span>
          </div>
          <div v-if="record.completedTime" class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDate(record.completedTime) }}</span>
          </div>
          <div v-if="record.category" class="flex items-center gap-1">
            <Tag class="w-3 h-3" />
            <span>{{ record.category }}</span>
          </div>
        </div>

        <!-- Record Description -->
        <div v-if="record.description" class="text-sm text-slate-600 line-clamp-1">
          {{ record.description }}
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          @click.stop="emit('click', record)"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <ExternalLink class="w-4 h-4 mr-1" />
          View
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Clock,
  Calendar,
  Tag,
  ExternalLink,
  BookOpen,
  Video,
  Users,
  Building,
  GraduationCap
} from 'lucide-vue-next'

interface Props {
  record: any
}

interface Emits {
  (e: 'click', record: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Get record type icon
const getRecordIcon = (type: string) => {
  const iconMap = {
    'Course': BookOpen,
    'LiveStream': Video,
    'MlcTraining': GraduationCap,
    'InternalTraining': Users,
    'ExternalTraining': Building
  }
  return iconMap[type as keyof typeof iconMap] || BookOpen
}

// Get record type label
const getRecordTypeLabel = (type: string) => {
  const labelMap = {
    'Course': 'Course',
    'LiveStream': 'Live Stream',
    'MlcTraining': 'MLC Training',
    'InternalTraining': 'Internal',
    'ExternalTraining': 'External'
  }
  return labelMap[type as keyof typeof labelMap] || type
}

// Get record type variant
const getRecordTypeVariant = (type: string) => {
  const variantMap = {
    'Course': 'default',
    'LiveStream': 'secondary',
    'MlcTraining': 'outline',
    'InternalTraining': 'default',
    'ExternalTraining': 'secondary'
  }
  return variantMap[type as keyof typeof variantMap] || 'default'
}

// Get status label
const getStatusLabel = (status: any) => {
  if (typeof status === 'string') return status
  // Handle different status enums
  const statusMap = {
    1: 'Active',
    2: 'Completed',
    3: 'Expired',
    0: 'Inactive'
  }
  return statusMap[status as keyof typeof statusMap] || 'Unknown'
}

// Get status variant
const getStatusVariant = (status: any) => {
  const variantMap = {
    1: 'default',
    2: 'default',
    3: 'destructive',
    0: 'secondary'
  }
  return variantMap[status as keyof typeof variantMap] || 'secondary'
}

// Format duration
const formatDuration = (duration: number) => {
  if (duration < 60) return `${duration}m`
  const hours = Math.floor(duration / 60)
  const minutes = duration % 60
  return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
}

// Format date
const formatDate = (date: string | number) => {
  return new Date(date).toLocaleDateString()
}

// Handle click
const handleClick = () => {
  emit('click', props.record)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
