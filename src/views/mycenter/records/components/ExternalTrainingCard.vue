<template>
  <Card
    class="bg-white rounded-lg border border-slate-200 overflow-hidden group"
  >
    <div class="flex items-center p-4 gap-4">
      <!-- Training Image/Icon -->
      <div
        class="relative w-24 h-24 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg overflow-hidden flex-shrink-0"
      >
        <div class="w-full h-full flex items-center justify-center text-white">
          <div class="text-center">
            <Plane class="w-8 h-8 mx-auto mb-1" />
            <div class="text-xs font-medium">External</div>
          </div>
        </div>

        <!-- Country Badge -->
        <div class="absolute top-1 left-1">
          <Badge
            variant="default"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ training.receivingCountry }}
          </Badge>
        </div>

        <!-- Travel Badge -->
        <div class="absolute top-1 right-1">
          <Badge variant="outline" class="text-xs p-1">
            <Globe class="w-3 h-3 text-orange-500" />
          </Badge>
        </div>
      </div>

      <!-- Training Content -->
      <div class="flex-1 min-w-0">
        <!-- Training Title -->
        <h3
          class="font-semibold text-slate-900 text-base mb-1 line-clamp-1"
        >
          {{ training.title }}
        </h3>

        <!-- Training Details -->
        <div class="flex items-center gap-4 text-sm mb-2">
          <span class="text-orange-600 font-medium">
            {{ training.costBearer }}
          </span>
          <span class="text-slate-600">
            Code: {{ training.code }}
          </span>
        </div>

        <!-- Training Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-3">
          <div class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDateRange(training.travelDate, training.returnDate) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <MapPin class="w-3 h-3" />
            <span>{{ training.receivingCountry }}</span>
          </div>
          <div class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ calculateDuration(training.travelDate, training.returnDate) }}</span>
          </div>
        </div>

        <!-- Training Stats -->
        <div class="flex items-center gap-4 text-xs text-slate-400">
          <div class="flex items-center gap-1">
            <Users class="w-3 h-3" />
            <span>{{ training.userCount }}</span>
          </div>
          <div class="flex items-center gap-1">
            <FileText class="w-3 h-3" />
            <span>{{ training.attachments ? 'Has attachments' : 'No attachments' }}</span>
          </div>
          <div class="flex items-center gap-1">
            <User class="w-3 h-3" />
            <span>Admin: {{ training.adminNo }}</span>
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <!-- <div class="flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          @click.stop="emit('click', training)"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <Eye class="w-4 h-4 mr-1" />
          View Details
        </Button>
      </div> -->
    </div>

    <!-- Additional Info Bar -->
    <div v-if="training.remark" class="px-4 pb-4">
      <div class="bg-slate-50 rounded-md p-2">
        <p class="text-xs text-slate-600 line-clamp-2">
          {{ training.remark }}
        </p>
      </div>
    </div>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  FileText,
  Eye,
  Plane,
  Globe,
  User
} from 'lucide-vue-next'
import { ExternalTrainingRespVO } from '@/api/mycenter/myrecords/external'
import dayjs from 'dayjs'

interface Props {
  training: ExternalTrainingRespVO
}

interface Emits {
  (e: 'click', training: ExternalTrainingRespVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Format date
const formatDate = (date: Date | string) => {
  if (!date) return ''
  return dayjs(date).format('MMM DD, YYYY')
}

// Format date range
const formatDateRange = (travelDate: Date | string, returnDate: Date | string) => {
  if (!travelDate) return ''
  const travel = dayjs(travelDate).format('MMM DD')
  const returnD = returnDate ? dayjs(returnDate).format('MMM DD, YYYY') : ''
  return returnD ? `${travel} - ${returnD}` : travel
}

// Calculate duration
const calculateDuration = (travelDate: Date | string, returnDate: Date | string) => {
  if (!travelDate || !returnDate) return ''
  const start = dayjs(travelDate)
  const end = dayjs(returnDate)
  const days = end.diff(start, 'day') + 1
  return `${days} day${days > 1 ? 's' : ''}`
}
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
