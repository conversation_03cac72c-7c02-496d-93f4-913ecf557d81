<template>
  <div class="bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-all duration-200 overflow-hidden group cursor-pointer" @click="handleClick">
    <div class="flex items-center p-4 gap-4">
      <!-- Exam Image/Icon -->
      <div class="relative w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg overflow-hidden flex-shrink-0">
        <img
          v-if="exam.cover"
          :src="exam.cover"
          :alt="exam.name"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div v-else class="w-full h-full flex items-center justify-center text-white">
          <FileText class="w-8 h-8" />
        </div>

        <!-- Exam Status Badge -->
        <div class="absolute top-1 left-1">
          <Badge
            :variant="getStatusVariant(exam.isPass)"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            {{ getStatusLabel(exam.isPass) }}
          </Badge>
        </div>

        <!-- On Schedule Badge -->
        <div v-if="exam.onSchedule" class="absolute top-1 right-1">
          <Badge
            variant="outline"
            class="text-xs font-medium px-1.5 py-0.5"
          >
            Scheduled
          </Badge>
        </div>
      </div>

      <!-- Exam Content -->
      <div class="flex-1 min-w-0">
        <!-- Exam Title -->
        <h3 class="font-semibold text-slate-900 text-base mb-1 group-hover:text-purple-600 transition-colors line-clamp-1">
          {{ exam.name }}
        </h3>

        <!-- Exam Score -->
        <div v-if="exam.paperScore" class="flex items-center gap-4 text-sm mb-2">
          <span class="text-purple-600 font-medium">
            Score: {{ exam.paperScore }}
          </span>
          <span v-if="exam.totalScore" class="text-slate-500">
            / {{ exam.totalScore }}
          </span>
        </div>

        <!-- Exam Metadata -->
        <div class="flex items-center gap-4 text-xs text-slate-500 mb-2">
          <div v-if="exam.answerTime" class="flex items-center gap-1">
            <Clock class="w-3 h-3" />
            <span>{{ exam.answerTime }} mins</span>
          </div>
          <div v-if="exam.beginTime" class="flex items-center gap-1">
            <Calendar class="w-3 h-3" />
            <span>{{ formatDate(exam.beginTime) }}</span>
          </div>
          <div v-if="exam.endTime" class="flex items-center gap-1">
            <CalendarX class="w-3 h-3" />
            <span>{{ formatDate(exam.endTime) }}</span>
          </div>
        </div>

        <!-- Exam Time Range -->
        <div v-if="exam.beginTime && exam.endTime" class="text-sm text-slate-600 line-clamp-1">
          {{ formatDateRange(exam.beginTime, exam.endTime) }}
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          @click.stop="emit('click', exam)"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <ExternalLink class="w-4 h-4 mr-1" />
          View
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Clock,
  Calendar,
  CalendarX,
  ExternalLink,
  FileText
} from 'lucide-vue-next'

interface Props {
  exam: any
}

interface Emits {
  (e: 'click', exam: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Get status label
const getStatusLabel = (isPass: any) => {
  if (isPass === true || isPass === 'true' || isPass === 1) return 'Passed'
  if (isPass === false || isPass === 'false' || isPass === 0) return 'Failed'
  return 'Pending'
}

// Get status variant
const getStatusVariant = (isPass: any) => {
  if (isPass === true || isPass === 'true' || isPass === 1) return 'default'
  if (isPass === false || isPass === 'false' || isPass === 0) return 'destructive'
  return 'secondary'
}

// Format date
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

// Format date range
const formatDateRange = (beginTime: string, endTime: string) => {
  if (!beginTime || !endTime) return ''
  const begin = formatDate(beginTime)
  const end = formatDate(endTime)
  return `${begin} - ${end}`
}

// Handle click
const handleClick = () => {
  emit('click', props.exam)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
