<template>
  <div class="flex flex-col h-full">
    <!-- Policy List -->
    <div v-if="policyList.length > 0" class="space-y-3">
      <PolicyCard
        v-for="policy in policyList"
        :key="policy.id"
        :policy="policy"
        @click="handlePolicyClick"
      />
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="min-h-[60vh] flex flex-col items-center justify-center text-center">
      <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
        <FileText class="w-12 h-12 text-slate-400" />
      </div>
      <h3 class="text-lg font-semibold text-slate-900 mb-2">No Policies Found</h3>
      <p class="text-slate-500 max-w-md">
        You don't have any {{ currentTabLabel.toLowerCase() }} policies yet
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="space-y-3">
      <div
        v-for="i in 6"
        :key="i"
        class="bg-white rounded-lg border overflow-hidden animate-pulse"
      >
        <div class="flex items-center p-4 gap-4">
          <div class="w-24 h-24 bg-slate-200 rounded-lg flex-shrink-0"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-slate-200 rounded w-3/4"></div>
            <div class="h-3 bg-slate-200 rounded w-1/2"></div>
            <div class="h-3 bg-slate-200 rounded w-2/3"></div>
          </div>
          <div class="flex-shrink-0">
            <div class="h-8 w-20 bg-slate-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import PolicyCard from './PolicyCard.vue'
import { FileText } from 'lucide-vue-next'

interface Props {
  policyList: any[]
  loading?: boolean
  currentTabLabel?: string
}

interface Emits {
  (e: 'policy-click', policy: any): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  currentTabLabel: 'policies'
})

const emit = defineEmits<Emits>()

// Handle policy click
const handlePolicyClick = (policy: any) => {
  emit('policy-click', policy)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
