<script setup lang="ts">
import { computed } from 'vue'
import type { SurveyQuestionVO, QuestionOption } from '@/api/survey/types'
import { SurveyQuestionTypeEnum } from '@/api/survey/types'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { CheckCircle, Circle, Star, FileText, Upload, Type, AlignLeft } from 'lucide-vue-next'

interface Props {
  question: SurveyQuestionVO
  answer: any // User's answer data
  readonly?: boolean // Whether in read-only mode
}

const props = withDefaults(defineProps<Props>(), {
  readonly: true // Default to read-only mode
})

// Get option list
const options = computed<QuestionOption[]>(() => {
  return props.question.config?.options || []
})

// Get selected option values list
const selectedValues = computed(() => {
  if (!props.answer?.answerValue) {
    return []
  }

  const { questionType } = props.question
  const answerValue = props.answer.answerValue

  if (questionType === SurveyQuestionTypeEnum.MULTIPLE_CHOICE) {
    let values = []

    if (Array.isArray(answerValue)) {
      // If answerValue is an array, use it directly
      values = answerValue
    } else if (typeof answerValue === 'string') {
      // If answerValue is a string, try to parse as JSON first
      try {
        const parsed = JSON.parse(answerValue)
        if (Array.isArray(parsed)) {
          values = parsed
        } else {
          // If not an array, split by comma
          values = answerValue.split(',')
        }
      } catch (e) {
        // If JSON parsing fails, split by comma
        values = answerValue.split(',')
      }
    }
    return values
  } else if (questionType === SurveyQuestionTypeEnum.SINGLE_CHOICE) {
    return Array.isArray(answerValue) ? [answerValue[0]] : [answerValue]
  }
  return []
})

// Check if option is selected
const isOptionSelected = (value: string) => {
  return selectedValues.value.includes(value)
}

// Get rating value
const ratingValue = computed(() => {
  if (props.question.questionType === SurveyQuestionTypeEnum.RATING && props.answer?.answerValue) {
    return parseInt(props.answer.answerValue)
  }
  return 0
})

// Get maximum rating
const maxRating = computed(() => {
  return props.question.config?.maxScore || 5
})

// Get uploaded files for file upload questions
const uploadedFiles = computed(() => {
  if (props.question.questionType === SurveyQuestionTypeEnum.FILE_UPLOAD && props.answer?.answerValue) {
    try {
      const parsed = JSON.parse(props.answer.answerValue)
      if (Array.isArray(parsed)) {
        return parsed
      }
    } catch (e) {
      console.warn('Failed to parse file upload answer value:', e)
    }
  }
  return []
})
</script>

<template>
  <div class="question-view-renderer">
    <!-- Single choice question -->
    <div
      v-if="question.questionType === SurveyQuestionTypeEnum.SINGLE_CHOICE"
      class="single-choice-question"
    >
      <RadioGroup :modelValue="answer?.answerValue || ''" class="space-y-3" disabled>
        <div
          v-for="option in options"
          :key="option.value"
          class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 transition-colors"
          :class="{
            'border-blue-300 bg-blue-50': isOptionSelected(option.value),
            'bg-gray-50': !isOptionSelected(option.value)
          }"
        >
          <RadioGroupItem
            :value="option.value"
            :id="`option-${option.value}`"
            disabled
            :checked="isOptionSelected(option.value)"
          />
          <Label
            :for="`option-${option.value}`"
            class="flex-1 text-sm font-medium leading-relaxed"
            :class="{
              'text-blue-600': isOptionSelected(option.value),
              'text-gray-700': !isOptionSelected(option.value)
            }"
          >
            {{ option.text }}
            <span v-if="option.description" class="block text-xs text-gray-500 mt-1 font-normal">
              {{ option.description }}
            </span>
          </Label>
        </div>
      </RadioGroup>
    </div>

    <!-- Multiple choice question -->
    <div
      v-else-if="question.questionType === SurveyQuestionTypeEnum.MULTIPLE_CHOICE"
      class="multiple-choice-question"
    >
      <div class="space-y-3">
        <div
          v-for="option in options"
          :key="option.value"
          class="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 transition-colors"
          :class="{
            'border-blue-300 bg-blue-50': isOptionSelected(option.value),
            'bg-gray-50': !isOptionSelected(option.value)
          }"
        >
          <Checkbox
            :modelValue="isOptionSelected(option.value)"
            :id="`option-${option.value}`"
            disabled
          />
          <Label
            :for="`option-${option.value}`"
            class="flex-1 text-sm font-medium leading-relaxed"
            :class="{
              'text-blue-600': isOptionSelected(option.value),
              'text-gray-700': !isOptionSelected(option.value)
            }"
          >
            {{ option.text }}
            <span v-if="option.description" class="block text-xs text-gray-500 mt-1 font-normal">
              {{ option.description }}
            </span>
          </Label>
        </div>
      </div>
    </div>

    <!-- True/False question -->
    <div
      v-else-if="question.questionType === SurveyQuestionTypeEnum.TRUE_FALSE"
      class="true-false-question"
    >
      <RadioGroup :modelValue="answer?.answerValue || ''" class="space-y-3" disabled>
        <div
          class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 transition-colors"
          :class="{
            'border-blue-300 bg-blue-50': answer?.answerValue === 'true',
            'bg-gray-50': answer?.answerValue !== 'true'
          }"
        >
          <RadioGroupItem
            value="true"
            id="option-true"
            disabled
            :checked="answer?.answerValue === 'true'"
          />
          <Label
            for="option-true"
            class="flex-1 text-sm font-medium leading-relaxed"
            :class="{
              'text-blue-600': answer?.answerValue === 'true',
              'text-gray-700': answer?.answerValue !== 'true'
            }"
          >
            Yes
          </Label>
        </div>
        <div
          class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 transition-colors"
          :class="{
            'border-blue-300 bg-blue-50': answer?.answerValue === 'false',
            'bg-gray-50': answer?.answerValue !== 'false'
          }"
        >
          <RadioGroupItem
            value="false"
            id="option-false"
            disabled
            :checked="answer?.answerValue === 'false'"
          />
          <Label
            for="option-false"
            class="flex-1 text-sm font-medium leading-relaxed"
            :class="{
              'text-blue-600': answer?.answerValue === 'false',
              'text-gray-700': answer?.answerValue !== 'false'
            }"
          >
            No
          </Label>
        </div>
      </RadioGroup>
    </div>

    <!-- Rating question -->
    <div
      v-else-if="question.questionType === SurveyQuestionTypeEnum.RATING"
      class="rating-question"
    >
      <div class="flex items-center space-x-2">
        <Star
          v-for="i in maxRating"
          :key="i"
          :class="i <= ratingValue ? 'text-yellow-400 fill-current' : 'text-gray-300'"
          class="w-6 h-6 cursor-default"
        />
        <span class="text-sm text-gray-600 ml-2"> {{ ratingValue }} / {{ maxRating }} </span>
      </div>
    </div>

    <!-- Text question -->
    <div v-else-if="question.questionType === SurveyQuestionTypeEnum.TEXT" class="text-question">
      <div class="p-4 rounded-lg border border-gray-200 bg-gray-50">
        <div class="flex items-start space-x-3">
          <AlignLeft class="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
          <div class="flex-1 min-w-0">
            <div class="text-sm text-gray-900 whitespace-pre-wrap break-words">
              {{ answer?.answerValue || 'Not filled' }}
            </div>
            <div class="text-xs text-gray-500 mt-2">
              Character count: {{ (answer?.answerValue || '').length }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- File upload question -->
    <div v-else-if="question.questionType === SurveyQuestionTypeEnum.FILE_UPLOAD" class="space-y-3">
      <div v-if="uploadedFiles.length > 0">
        <div class="space-y-2">
          <div
            v-for="(file, index) in uploadedFiles"
            :key="file.id || index"
            class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200"
          >
            <FileText class="w-5 h-5 text-blue-600 flex-shrink-0" />
            <div class="flex-1 min-w-0">
<!--              <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>-->
              <a
                :href="file.url"
                target="_blank"
                class="text-xs text-blue-600 hover:text-blue-800 underline break-all"
              >
                {{ file.name }}
              </a>
            </div>
            <Badge variant="outline" class="text-xs flex-shrink-0">Uploaded</Badge>
          </div>
        </div>
      </div>
      <div v-else class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <Upload class="w-5 h-5 text-gray-400" />
        <span class="text-gray-600">No file uploaded</span>
      </div>
    </div>

    <!-- Other question types or unknown types -->
    <div v-else class="unknown-question">
      <div class="p-4 rounded-lg border border-gray-200 bg-gray-50">
        <div class="flex items-center space-x-3">
          <Type class="w-5 h-5 text-gray-400" />
          <span class="text-gray-600">{{ answer?.answerValue || 'Not answered' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.question-view-renderer {
  width: 100%;
}
</style>
