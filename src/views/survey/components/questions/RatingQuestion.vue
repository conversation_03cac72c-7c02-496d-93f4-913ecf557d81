<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Star, Heart, ThumbsUp, Smile } from 'lucide-vue-next'
import type { SurveyQuestionVO, SurveyAnswerReqVO } from '@/api/survey/types'

interface Props {
  question: SurveyQuestionVO
  answer: SurveyAnswerReqVO
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [answer: Partial<SurveyAnswerReqVO>]
}>()

// Local selected value
const selectedValue = ref<number>(0)

// Rating configuration
const ratingConfig = computed(() => {
  const config = props.question.config || {}
  return {
    minValue: config.minScore || 1,
    maxValue: config.maxScore || 5,
    step: config.step || 1,
    style: config.style || 'star', // star, heart, thumbs, smile, number
    labels: config.labels || {},
    showLabels: config.showLabels !== false,
    allowHalf: config.allowHalf || false,
    minLabel: config.minLabel || '',
    maxLabel: config.maxLabel || ''
  }
})

// Generate rating options
const ratingOptions = computed(() => {
  const config = props.question.config || {}

  // 如果存在传统的labels对象格式，优先使用（向后兼容）
  if (config.labels && typeof config.labels === 'object' && Object.keys(config.labels).length > 0) {
    const options = Object.entries(config.labels)
      .map(([value, label]) => ({
        value: Number(value),
        label: label
      }))
      .sort((a, b) => a.value - b.value)
    return options
  }

  // 根据minScore、maxScore、minLabel、maxLabel生成评分选项
  const minScore = config.minScore || 1
  const maxScore = config.maxScore || 5
  const step = config.step || 1
  const minLabel = config.minLabel || ''
  const maxLabel = config.maxLabel || ''

  const options = []
  for (let i = minScore; i <= maxScore; i += step) {
    let label = i.toString()

    // 为最小值和最大值设置标签
    if (i === minScore && minLabel) {
      label = minLabel
    } else if (i === maxScore && maxLabel) {
      label = maxLabel
    }

    options.push({
      value: i,
      label: label
    })
  }

  return options
})

// Get icon component
const getIconComponent = (style: string) => {
  switch (style) {
    case 'heart':
      return Heart
    case 'thumbs':
      return ThumbsUp
    case 'smile':
      return Smile
    case 'star':
    default:
      return Star
  }
}

// Get rating description
const getRatingDescription = (value: number) => {
  const { labels, maxValue, minLabel, maxLabel } = ratingConfig.value

  // 首先检查是否有对应的标签
  if (labels && typeof labels === 'object' && labels[value]) {
    return labels[value]
  }

  // 检查是否是最小值或最大值，使用对应的标签
  if (value === ratingConfig.value.minValue && minLabel) {
    return minLabel
  }
  if (value === ratingConfig.value.maxValue && maxLabel) {
    return maxLabel
  }

  // 从ratingOptions中查找对应的标签
  const option = ratingOptions.value.find(opt => opt.value === value)
  if (option && option.label !== value.toString()) {
    return option.label
  }

  // Default description
  const percentage = (value / maxValue) * 100
  if (percentage >= 80) return 'Excellent'
  if (percentage >= 60) return 'Good'
  if (percentage >= 40) return 'Average'
  if (percentage >= 20) return 'Poor'
  return 'Very Poor'
}

// Handle rating selection
const handleRatingSelect = (value: number) => {
  selectedValue.value = value

  emit('update', {
    answerValue: value.toString(),
    answerText: `${value} - ${getRatingDescription(value)}`
  })
}

// Watch external answer changes
watch(
  () => props.answer?.answerValue,
  (newValue) => {
    if (newValue) {
      const numValue = Number(newValue)
      if (!isNaN(numValue) && numValue !== selectedValue.value) {
        selectedValue.value = numValue
      }
    } else {
      selectedValue.value = 0
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="rating-question">
    <!-- Rating description -->
    <!-- 已隐藏：顶部标签范围显示 -->
    <!--
    <div v-if="ratingConfig.showLabels && (ratingConfig.minLabel || ratingConfig.maxLabel || Object.keys(ratingConfig.labels).length > 0)" class="mb-6">
      <div class="flex justify-between text-sm text-gray-600">
        <span>{{ ratingConfig.minLabel || ratingConfig.labels[ratingConfig.minValue] || ratingConfig.minValue }}</span>
        <span>{{ ratingConfig.maxLabel || ratingConfig.labels[ratingConfig.maxValue] || ratingConfig.maxValue }}</span>
      </div>
    </div>
    -->

    <!-- Star rating style -->
    <div
      v-if="ratingConfig.style === 'star'"
      class="flex items-center justify-center space-x-2 mb-6"
    >
      <button
        v-for="option in ratingOptions"
        :key="option.value"
        @click="handleRatingSelect(option.value)"
        class="p-2 rounded-lg transition-all duration-200 hover:scale-110"
        :class="{
          'text-yellow-400': selectedValue >= option.value,
          'text-gray-300 hover:text-yellow-200': selectedValue < option.value
        }"
      >
        <Star class="w-8 h-8" :fill="selectedValue >= option.value ? 'currentColor' : 'none'" />
      </button>
    </div>

    <!-- 心形评分样式 -->
    <div
      v-else-if="ratingConfig.style === 'heart'"
      class="flex items-center justify-center space-x-2 mb-6"
    >
      <button
        v-for="option in ratingOptions"
        :key="option.value"
        @click="handleRatingSelect(option.value)"
        class="p-2 rounded-lg transition-all duration-200 hover:scale-110"
        :class="{
          'text-red-400': selectedValue >= option.value,
          'text-gray-300 hover:text-red-200': selectedValue < option.value
        }"
      >
        <Heart class="w-8 h-8" :fill="selectedValue >= option.value ? 'currentColor' : 'none'" />
      </button>
    </div>

    <!-- 数字评分样式 -->
    <div v-else class="flex items-center justify-center space-x-3 mb-6">
      <Button
        v-for="option in ratingOptions"
        :key="option.value"
        @click="handleRatingSelect(option.value)"
        :variant="selectedValue === option.value ? 'default' : 'outline'"
        :size="'lg'"
        class="w-12 h-12 rounded-full font-bold text-lg transition-all duration-200"
        :class="{
          'scale-110 shadow-lg': selectedValue === option.value,
          'hover:scale-105': selectedValue !== option.value
        }"
      >
        {{ option.value }}
      </Button>
    </div>

    <!-- 选中值显示 -->
    <!--    <div v-if="selectedValue > 0" class="text-center">-->
    <!--      <div class="text-2xl font-bold text-gray-900 mb-2">-->
    <!--        {{ selectedValue }} / {{ ratingConfig.maxValue }}-->
    <!--      </div>-->
    <!--      <div class="text-lg text-gray-600">-->
    <!--        {{ getRatingDescription(selectedValue) }}-->
    <!--      </div>-->
    <!--    </div>-->

    <!-- 评分标签 -->
    <!-- 已隐藏：底部评分标签按钮 -->
    <!--
    <div v-if="ratingConfig.showLabels && ratingOptions.length <= 7" class="mt-6">
      <div class="grid gap-2" :class="`grid-cols-${ratingOptions.length}`">
        <div v-for="option in ratingOptions" :key="option.value" class="text-center">
          <button
            @click="handleRatingSelect(option.value)"
            class="w-full p-2 text-sm rounded-lg border transition-colors"
            :class="{
              'bg-blue-50 border-blue-300 text-blue-700': selectedValue === option.value,
              'border-gray-200 text-gray-600 hover:border-gray-300': selectedValue !== option.value
            }"
          >
            {{ option.label }}
          </button>
        </div>
      </div>
    </div>
    -->

    <!-- 评分提示 -->
    <div v-if="question.config?.hint" class="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <p class="text-sm text-blue-800"> <strong>Tip:</strong> {{ question.config.hint }} </p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.rating-question {
  .rating-button {
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }

    &.selected {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .star-rating {
    .star {
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}
</style>
