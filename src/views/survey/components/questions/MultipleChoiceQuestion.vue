<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-vue-next'
import type { SurveyQuestionVO, SurveyAnswerReqVO, QuestionOption } from '@/api/survey/types'

interface Props {
  question: SurveyQuestionVO
  answer: SurveyAnswerReqVO
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [answer: Partial<SurveyAnswerReqVO>]
}>()

// Local selected values array
const selectedValues = ref<string[]>([])

// Get options from question configuration
const options = computed<QuestionOption[]>(() => {
  if (!props.question.config?.options) return []
  return props.question.config.options
})

// Get selection limits
const minSelections = computed(() => props.question.config?.minSelections || 0)
const maxSelections = computed(() => props.question.config?.maxSelections || options.value.length)

// Validate selection count
const validationError = computed(() => {
  const count = selectedValues.value.length

  if (props.question.required && count === 0) {
    return 'This question is required. Please select at least one option.'
  }

  if (count < minSelections.value) {
    return `Please select at least ${minSelections.value} option(s).`
  }

  if (count > maxSelections.value) {
    return `Please select no more than ${maxSelections.value} option(s).`
  }

  return null
})

// Check if option is selected
const isOptionSelected = (value: string) => {
  return selectedValues.value.includes(value)
}

// Handle option click
const handleOptionToggle = (value: string) => {
  console.log('handleOptionToggle', value)
  const index = selectedValues.value.indexOf(value)

  if (index > -1) {
    // Deselect
    selectedValues.value.splice(index, 1)
  } else {
    // Check if exceeds maximum selections
    if (selectedValues.value.length < maxSelections.value) {
      selectedValues.value.push(value)
    }
  }
}

// Get selected option texts
const getSelectedTexts = () => {
  return selectedValues.value.map((value) => {
    const option = options.value.find((opt) => opt.value === value)
    return option?.text || value
  })
}

// Watch selected values changes
watch(
  selectedValues,
  (newValues) => {
    const selectedTexts = getSelectedTexts()

    emit('update', {
      answerValue: JSON.stringify(newValues),
      answerText: selectedTexts.join(', ')
    })
  },
  { deep: true }
)

// Initialize selected values
watch(
  () => props.answer?.answerValue,
  (newValue) => {
    if (newValue) {
      try {
        const parsed = JSON.parse(newValue)
        if (Array.isArray(parsed)) {
          selectedValues.value = parsed
        }
      } catch (error) {
        console.warn('Failed to parse answer value:', error)
        selectedValues.value = []
      }
    } else {
      selectedValues.value = []
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="multiple-choice-question">
    <!-- Selection limit hint -->
    <div
      v-if="minSelections > 0 || maxSelections < options.length"
      class="mb-4 text-sm text-gray-600"
    >
      <span v-if="minSelections > 0 && maxSelections < options.length">
        Select {{ minSelections }} to {{ maxSelections }} options
      </span>
      <span v-else-if="minSelections > 0"> Select at least {{ minSelections }} option(s) </span>
      <span v-else> Select up to {{ maxSelections }} option(s) </span>
    </div>

    <!-- Validation error message -->
    <Alert v-if="validationError" variant="destructive" class="mb-4">
      <AlertCircle class="h-4 w-4" />
      <AlertDescription>{{ validationError }}</AlertDescription>
    </Alert>

    <!-- Options list -->
    <div class="space-y-3">
      <div
        v-for="option in options"
        :key="option.value"
        class="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors cursor-pointer"
        :class="{
          'border-blue-300 bg-blue-50': isOptionSelected(option.value),
          'opacity-50 cursor-not-allowed':
            !isOptionSelected(option.value) && selectedValues.length >= maxSelections
        }"
        @click="handleOptionToggle(option.value)"
      >
        <Checkbox
          :modelValue="isOptionSelected(option.value)"
          :disabled="!isOptionSelected(option.value) && selectedValues.length >= maxSelections"
          :id="`option-${option.value}`"
          @click.stop
        />
        <Label
          class="flex-1 cursor-pointer text-sm font-medium leading-relaxed"
          :class="{
            'cursor-not-allowed':
              !isOptionSelected(option.value) && selectedValues.length >= maxSelections
          }"
        >
          {{ option.text }}
          <span v-if="option.description" class="block text-xs text-gray-500 mt-1 font-normal">
            {{ option.description }}
          </span>
        </Label>
      </div>
    </div>

    <!-- Selection count -->
    <div v-if="selectedValues.length > 0" class="mt-4 text-sm text-gray-600">
      Selected: {{ selectedValues.length }} /
      {{ maxSelections === options.length ? '∞' : maxSelections }}
    </div>

    <!-- Empty state message -->
    <div v-if="options.length === 0" class="text-center py-8 text-gray-500">
      <p>No options available for this question.</p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.multiple-choice-question {
  .checkbox-option {
    transition: all 0.2s ease;

    &:hover:not(.disabled) {
      background-color: #f8fafc;
      border-color: #cbd5e1;
    }

    &.selected {
      background-color: #eff6ff;
      border-color: #3b82f6;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>
