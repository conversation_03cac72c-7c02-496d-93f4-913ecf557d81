<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import type { SurveyQuestionVO, SurveyAnswerReqVO, QuestionOption } from '@/api/survey/types'

interface Props {
  question: SurveyQuestionVO
  answer: SurveyAnswerReqVO
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [answer: Partial<SurveyAnswerReqVO>]
}>()

// Local selected value
const selectedValue = ref(props.answer?.answerValue || '')

// Get options from question configuration
const options = computed<QuestionOption[]>(() => {
  if (!props.question.config?.options) return []
  return props.question.config.options
})

// Watch selected value changes
watch(selectedValue, (newValue) => {
  const selectedOption = options.value.find((option) => option.value === newValue)

  emit('update', {
    answerValue: newValue,
    answerText: selectedOption?.text || newValue
  })
})

// Watch external answer changes
watch(
  () => props.answer?.answerValue,
  (newValue) => {
    if (newValue !== selectedValue.value) {
      selectedValue.value = newValue || ''
    }
  }
)
</script>

<template>
  <div class="single-choice-question">
    <RadioGroup v-model="selectedValue" class="space-y-3">
      <div
        v-for="option in options"
        :key="option.value"
        class="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors cursor-pointer"
        @click="selectedValue = option.value"
      >
        <RadioGroupItem :value="option.value" :id="`option-${option.value}`" />
        <Label
          :for="`option-${option.value}`"
          class="flex-1 cursor-pointer text-sm font-medium leading-relaxed"
        >
          {{ option.text }}
          <span v-if="option.description" class="block text-xs text-gray-500 mt-1 font-normal">
            {{ option.description }}
          </span>
        </Label>
      </div>
    </RadioGroup>

    <!-- Empty state message -->
    <div v-if="options.length === 0" class="text-center py-8 text-gray-500">
      <p>No options available for this question.</p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.single-choice-question {
  .radio-option {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8fafc;
      border-color: #cbd5e1;
    }

    &.selected {
      background-color: #eff6ff;
      border-color: #3b82f6;
    }
  }
}
</style>
