<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, Type, AlignLeft } from 'lucide-vue-next'
import type { SurveyQuestionVO, SurveyAnswerReqVO } from '@/api/survey/types'

interface Props {
  question: SurveyQuestionVO
  answer: SurveyAnswerReqVO
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [answer: Partial<SurveyAnswerReqVO>]
}>()

// Local input value
const inputValue = ref(props.answer?.answerText || '')

// Text configuration
const textConfig = computed(() => {
  const config = props.question.config || {}
  return {
    inputType: config.inputType || 'textarea', // input, textarea
    placeholder: config.placeholder || 'Please enter your answer...',
    minLength: config.minLength || 0,
    maxLength: config.maxLength || 1000,
    rows: config.rows || 4,
    pattern: config.pattern || null, // Regular expression validation
    patternMessage: config.patternMessage || 'Invalid format'
  }
})

// Character count
const characterCount = computed(() => inputValue.value.length)
const remainingChars = computed(() => textConfig.value.maxLength - characterCount.value)

// Validate input
const validationError = computed(() => {
  const value = inputValue.value.trim()
  const { minLength, maxLength, pattern, patternMessage } = textConfig.value

  if (props.question.required && !value) {
    return 'This question is required. Please provide an answer.'
  }

  if (value && value.length < minLength) {
    return `Answer must be at least ${minLength} characters long.`
  }

  if (value.length > maxLength) {
    return `Answer must not exceed ${maxLength} characters.`
  }

  if (pattern && value) {
    const regex = new RegExp(pattern)
    if (!regex.test(value)) {
      return patternMessage
    }
  }

  return null
})

// Whether to show character count
const showCharCount = computed(() => {
  return textConfig.value.maxLength > 0 && textConfig.value.maxLength <= 1000
})

// Watch input value changes
watch(inputValue, (newValue) => {
  emit('update', {
    answerText: newValue,
    answerValue: newValue
  })
})

// Watch external answer changes
watch(
  () => props.answer?.answerText,
  (newValue) => {
    if (newValue !== inputValue.value) {
      inputValue.value = newValue || ''
    }
  }
)

// Handle input restrictions
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement | HTMLTextAreaElement
  const value = target.value

  // Limit maximum length
  if (textConfig.value.maxLength > 0 && value.length > textConfig.value.maxLength) {
    target.value = value.substring(0, textConfig.value.maxLength)
    inputValue.value = target.value
  } else {
    inputValue.value = value
  }
}
</script>

<template>
  <div class="text-question">
    <!-- Input type icon and description -->
    <div class="flex items-center space-x-2 mb-4 text-sm text-gray-600">
      <component :is="textConfig.inputType === 'textarea' ? AlignLeft : Type" class="w-4 h-4" />
      <span>
        {{ textConfig.inputType === 'textarea' ? 'Long text answer' : 'Short text answer' }}
      </span>
      <span v-if="textConfig.minLength > 0" class="text-gray-500">
        (Min: {{ textConfig.minLength }} chars)
      </span>
    </div>

    <!-- Validation error message -->
    <Alert v-if="validationError" variant="destructive" class="mb-4">
      <AlertCircle class="h-4 w-4" />
      <AlertDescription>{{ validationError }}</AlertDescription>
    </Alert>

    <!-- Single line input -->
    <div v-if="textConfig.inputType === 'input'" class="space-y-2">
      <Input
        v-model="inputValue"
        :placeholder="textConfig.placeholder"
        :maxlength="textConfig.maxLength > 0 ? textConfig.maxLength : undefined"
        class="text-base"
        @input="handleInput"
      />
    </div>

    <!-- Multi-line text box -->
    <div v-else class="space-y-2">
      <Textarea
        v-model="inputValue"
        :placeholder="textConfig.placeholder"
        :rows="textConfig.rows"
        :maxlength="textConfig.maxLength > 0 ? textConfig.maxLength : undefined"
        class="text-base resize-none"
        @input="handleInput"
      />
    </div>

    <!-- Character count -->
    <div v-if="showCharCount" class="flex justify-between items-center mt-2 text-sm">
      <div class="text-gray-500">
        <span
          v-if="textConfig.minLength > 0 && characterCount < textConfig.minLength"
          class="text-orange-600"
        >
          {{ textConfig.minLength - characterCount }} more characters needed
        </span>
        <span v-else-if="characterCount >= textConfig.minLength" class="text-green-600">
          Minimum requirement met
        </span>
      </div>

      <div class="text-gray-600">
        <span
          :class="{
            'text-red-600': remainingChars < 0,
            'text-orange-600': remainingChars <= 50 && remainingChars > 0,
            'text-gray-600': remainingChars > 50
          }"
        >
          {{ characterCount }} / {{ textConfig.maxLength }}
        </span>
      </div>
    </div>

    <!-- Format hint -->
    <div v-if="textConfig.pattern" class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <p class="text-sm text-blue-800">
        <strong>Format requirement:</strong> {{ textConfig.patternMessage }}
      </p>
    </div>

    <!-- Input hint -->
    <div v-if="question.config?.hint" class="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
      <p class="text-sm text-gray-700"> <strong>Hint:</strong> {{ question.config.hint }} </p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-question {
  .input-container {
    position: relative;
  }

  .character-count {
    transition: color 0.2s ease;
  }

  .validation-message {
    animation: fadeIn 0.3s ease;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
