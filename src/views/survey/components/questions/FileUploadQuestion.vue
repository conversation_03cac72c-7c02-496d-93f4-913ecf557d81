<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { SuperUpload } from '@/components/SuperUpload'
import { FileInfo, UploadResult } from '@/components/SuperUpload/src/config'
import { checkFile } from '@/utils/fileUtil'
import { nanoid } from 'nanoid'
import { Upload, File, Image, FileText, X, CheckCircle, AlertCircle } from 'lucide-vue-next'
import type { SurveyQuestionVO, SurveyAnswerReqVO } from '@/api/survey/types'

interface Props {
  question: SurveyQuestionVO
  answer: SurveyAnswerReqVO
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [answer: Partial<SurveyAnswerReqVO>]
}>()

// File upload state
const fileInfoList = ref<FileInfo[]>([])
const uploadedFiles = ref<UploadResult[]>([])
const dragOver = ref(false)
const fileInputRef = ref<HTMLInputElement>()

// File upload configuration
const uploadConfig = computed(() => {
  const config = props.question.config || {}
  return {
    maxFiles: config.maxFiles || 1,
    maxSize: config.maxSize || config.maxFileSize || 10 * 1024 * 1024, // 10MB
    allowedTypes: config.allowedTypes || ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
    description: config.description || 'Upload your files here'
  }
})

// Validation error
const validationError = ref('')

// Get file icon
const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()

  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext || '')) {
    return Image
  } else if (['pdf'].includes(ext || '')) {
    return FileText
  } else {
    return File
  }
}

// Format file size
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Validate file
const validateFile = async (file: File) => {
  // Check file size
  if (file.size > uploadConfig.value.maxSize) {
    return `File size exceeds ${formatFileSize(uploadConfig.value.maxSize)}`
  }

  // Convert file types to uppercase, as checkFile function expects uppercase file extensions
  const allowedTypesUpperCase = uploadConfig.value.allowedTypes.map((type) => type.toUpperCase())

  // Use project's file validation tool
  const checkResult = await checkFile(file, allowedTypesUpperCase)

  if (!checkResult.can) {
    return `File type not allowed. Allowed types: ${uploadConfig.value.allowedTypes.join(', ')}`
  }

  return null
}

// SuperUpload 事件处理
const handlePrepared = (result: UploadResult) => {
  console.log('File prepared:', result)
}

const handleComplete = (result: UploadResult) => {
  console.log('File upload complete:', result)
  uploadedFiles.value.push(result)

  // 从待上传列表中移除
  fileInfoList.value = fileInfoList.value.filter((info) => info.uid !== result.fileName)

  // 更新答案
  updateAnswer()
}

const handleError = (error: string) => {
  console.error('File upload error:', error)
  validationError.value = `Upload failed: ${error}`
}

// 处理文件选择
const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement
  if (!input.files || input.files.length === 0) return

  const files = Array.from(input.files)
  await processFiles(files)

  // 重置input
  input.value = ''
}

// 删除文件
const removeFile = (fileId: number) => {
  uploadedFiles.value = uploadedFiles.value.filter((file) => file.id !== fileId)
  updateAnswer()
}

// 更新答案
const updateAnswer = () => {
  const fileData = uploadedFiles.value.map((file) => ({
    id: file.id,
    name: file.fileName,
    url: file.url
  }))

  emit('update', {
    answerValue: JSON.stringify(fileData),
    answerText: uploadedFiles.value.map((file) => file.fileName).join(', ')
  })
}

// 触发文件选择
const triggerFileSelect = () => {
  fileInputRef.value?.click()
}

// 处理拖拽上传
const handleDrop = async (event: DragEvent) => {
  dragOver.value = false

  if (!event.dataTransfer?.files) return

  const files = Array.from(event.dataTransfer.files)
  await processFiles(files)
}

// 处理文件（统一处理函数）
const processFiles = async (files: File[]) => {
  for (const file of files) {
    // 检查文件数量限制
    if (fileInfoList.value.length + uploadedFiles.value.length >= uploadConfig.value.maxFiles) {
      validationError.value = `Maximum ${uploadConfig.value.maxFiles} file(s) allowed`
      break
    }

    // 验证文件
    const error = await validateFile(file)
    if (error) {
      validationError.value = error
      continue
    }

    // 创建文件信息对象
    const fileInfo: FileInfo = {
      uid: nanoid(),
      file: file,
      type: 'survey',
      folderId: 0,
      relativePath: ''
    }

    fileInfoList.value.push(fileInfo)
    validationError.value = '' // 清除错误
  }
}

// 监听外部答案变化
watch(
  () => props.answer?.answerValue,
  (newValue) => {
    if (newValue && newValue !== JSON.stringify(uploadedFiles.value.map((f) => f.id))) {
      try {
        const fileIds = JSON.parse(newValue)
        if (Array.isArray(fileIds)) {
          // 这里应该根据文件ID获取文件信息
          // 暂时保持现有文件
        }
      } catch (error) {
        console.warn('Failed to parse file IDs:', error)
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="file-upload-question">
    <!-- Upload configuration description -->
    <div class="mb-4 text-sm text-gray-600">
      <p>{{ uploadConfig.description }}</p>
      <div class="mt-2 space-y-1">
        <p>• Maximum {{ uploadConfig.maxFiles }} file(s)</p>
        <p>• Maximum size: {{ formatFileSize(uploadConfig.maxSize) }} per file</p>
        <p>• Allowed types: {{ uploadConfig.allowedTypes.join(', ') }}</p>
      </div>
    </div>

    <!-- Validation error message -->
    <Alert v-if="validationError" variant="destructive" class="mb-4">
      <AlertCircle class="h-4 w-4" />
      <AlertDescription>{{ validationError }}</AlertDescription>
    </Alert>

    <!-- SuperUpload component -->
    <SuperUpload
      v-for="fileInfo in fileInfoList"
      :key="fileInfo.uid"
      :file-info="fileInfo"
      @prepared="handlePrepared"
      @complete="handleComplete"
      @error="handleError"
    >
      <template #default="{ progress }">
        <div
          v-if="progress.percentage > 0 && progress.percentage < 100"
          class="flex items-center space-x-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"
        >
          <Upload class="w-6 h-6 text-blue-600" />
          <div class="flex-1">
            <p class="text-sm font-medium text-gray-900">Uploading {{ fileInfo.file.name }}...</p>
            <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${progress.percentage}%` }"
              ></div>
            </div>
            <p class="text-xs text-gray-500 mt-1">{{ Math.round(progress.percentage) }}%</p>
          </div>
        </div>
      </template>
    </SuperUpload>

    <!-- 上传区域 -->
    <div
      v-if="fileInfoList.length + uploadedFiles.length < uploadConfig.maxFiles"
      class="upload-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-colors cursor-pointer hover:border-gray-400"
      :class="{ 'drag-over': dragOver }"
      @click="triggerFileSelect"
      @dragover.prevent="dragOver = true"
      @dragleave.prevent="dragOver = false"
      @drop.prevent="handleDrop"
    >
      <input
        ref="fileInputRef"
        type="file"
        :multiple="uploadConfig.maxFiles > 1"
        :accept="uploadConfig.allowedTypes.join(',')"
        class="hidden"
        @change="handleFileChange"
      />

      <div class="space-y-4">
        <Upload class="w-12 h-12 text-gray-400 mx-auto" />
        <div>
          <p class="text-lg font-medium text-gray-900"> Drop files here or click to upload </p>
          <p class="text-sm text-gray-500 mt-1">
            {{ uploadConfig.allowedTypes.join(', ') }}
          </p>
        </div>
        <Button variant="outline" size="sm"> Choose Files </Button>
      </div>
    </div>

    <!-- 已上传文件列表 -->
    <div v-if="uploadedFiles.length > 0" class="mt-6 space-y-3">
      <h4 class="text-sm font-medium text-gray-900">Uploaded Files</h4>

      <div class="space-y-2">
        <div
          v-for="file in uploadedFiles"
          :key="file.id"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
        >
          <div class="flex items-center space-x-3">
            <component :is="getFileIcon(file.fileName)" class="w-5 h-5 text-gray-500" />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">{{ file.fileName }}</p>
              <p class="text-xs text-gray-500">{{ formatFileSize(file.fileSize) }}</p>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <CheckCircle class="w-4 h-4 text-green-500" />
            <Button
              @click="removeFile(file.id)"
              variant="ghost"
              size="sm"
              class="text-red-600 hover:text-red-700"
            >
              <X class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件要求提示 -->
    <div v-if="question.config?.hint" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <p class="text-sm text-blue-800"> <strong>Note:</strong> {{ question.config.hint }} </p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.file-upload-question {
  .upload-area {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f8fafc;
    }

    &.drag-over {
      background-color: #eff6ff;
      border-color: #3b82f6;
    }
  }

  .file-item {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f1f5f9;
    }
  }
}
</style>
