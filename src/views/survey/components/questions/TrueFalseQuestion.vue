<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Check, X } from 'lucide-vue-next'
import type { SurveyQuestionVO, SurveyAnswerReqVO } from '@/api/survey/types'

interface Props {
  question: SurveyQuestionVO
  answer: SurveyAnswerReqVO
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [answer: Partial<SurveyAnswerReqVO>]
}>()

// Local selected value
const selectedValue = ref(props.answer?.answerValue || '')

// True/False question options
const options = computed(() => {
  // Get custom options from configuration, or use default True/False
  const config = props.question.config

  if (config?.customOptions) {
    return [
      {
        value: 'true',
        text: config.trueText || 'True',
        icon: Check,
        color: 'text-green-600'
      },
      {
        value: 'false',
        text: config.falseText || 'False',
        icon: X,
        color: 'text-red-600'
      }
    ]
  }

  // Default options
  return [
    {
      value: 'true',
      text: 'True',
      icon: Check,
      color: 'text-green-600'
    },
    {
      value: 'false',
      text: 'False',
      icon: X,
      color: 'text-red-600'
    }
  ]
})

// Watch selected value changes
watch(selectedValue, (newValue) => {
  const selectedOption = options.value.find((option) => option.value === newValue)

  emit('update', {
    answerValue: newValue,
    answerText: selectedOption?.text || newValue
  })
})

// Watch external answer changes
watch(
  () => props.answer?.answerValue,
  (newValue) => {
    if (newValue !== selectedValue.value) {
      selectedValue.value = newValue || ''
    }
  }
)
</script>

<template>
  <div class="true-false-question">
    <RadioGroup v-model="selectedValue" class="flex justify-center gap-8">
      <div
        v-for="option in options"
        :key="option.value"
        class="flex flex-col items-center p-6 rounded-lg border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-all cursor-pointer w-40"
        :class="{
          'border-green-300 bg-green-50 shadow-md':
            selectedValue === option.value && option.value === 'true',
          'border-red-300 bg-red-50 shadow-md':
            selectedValue === option.value && option.value === 'false'
        }"
        @click="selectedValue = option.value"
      >
        <div
          class="w-16 h-16 rounded-full flex items-center justify-center mb-3"
          :class="{
            'bg-green-100': option.value === 'true',
            'bg-red-100': option.value === 'false'
          }"
        >
          <component :is="option.icon" :class="['w-8 h-8', option.color]" />
        </div>

        <RadioGroupItem :value="option.value" :id="`option-${option.value}`" class="sr-only" />

        <Label
          :for="`option-${option.value}`"
          class="text-xl font-medium cursor-pointer"
          :class="option.color"
        >
          {{ option.text }}
        </Label>
      </div>
    </RadioGroup>

    <!-- Hint information -->
    <div v-if="question.config?.hint" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <p class="text-sm text-blue-800"> <strong>Hint:</strong> {{ question.config.hint }} </p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.true-false-question {
  .option-card {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
