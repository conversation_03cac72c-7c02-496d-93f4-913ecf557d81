<script setup lang="ts">
import { computed } from 'vue'
import type { SurveyQuestionVO, SurveyAnswerReqVO } from '@/api/survey/types'
import { SurveyQuestionTypeEnum } from '@/api/survey/types'
import SingleChoiceQuestion from './questions/SingleChoiceQuestion.vue'
import MultipleChoiceQuestion from './questions/MultipleChoiceQuestion.vue'
import TrueFalseQuestion from './questions/TrueFalseQuestion.vue'
import RatingQuestion from './questions/RatingQuestion.vue'
import FileUploadQuestion from './questions/FileUploadQuestion.vue'
import TextQuestion from './questions/TextQuestion.vue'

interface Props {
  question: SurveyQuestionVO
  answer: SurveyAnswerReqVO
}

const props = defineProps<Props>()
const emit = defineEmits<{
  update: [questionId: number, answer: Partial<SurveyAnswerReqVO>]
}>()

// Question type component mapping
const questionComponents = {
  [SurveyQuestionTypeEnum.SINGLE_CHOICE]: SingleChoiceQuestion,
  [SurveyQuestionTypeEnum.MULTIPLE_CHOICE]: MultipleChoiceQuestion,
  [SurveyQuestionTypeEnum.TRUE_FALSE]: TrueFalseQuestion,
  [SurveyQuestionTypeEnum.RATING]: RatingQuestion,
  [SurveyQuestionTypeEnum.FILE_UPLOAD]: FileUploadQuestion,
  [SurveyQuestionTypeEnum.TEXT]: TextQuestion
}

// Get component corresponding to current question type
const currentComponent = computed(() => {
  return questionComponents[props.question.questionType] || 'div'
})

// Handle answer update
const handleAnswerUpdate = (answer: Partial<SurveyAnswerReqVO>) => {
  emit('update', props.question.id, answer)
}
</script>

<template>
  <div class="question-renderer">
    <component
      :is="currentComponent"
      :question="question"
      :answer="answer"
      @update="handleAnswerUpdate"
    />
  </div>
</template>

<style scoped lang="scss">
.question-renderer {
  width: 100%;
}
</style>
