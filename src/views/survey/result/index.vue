<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ArrowLeft, CheckCircle, BarChart3, Users, TrendingUp, AlertCircle } from 'lucide-vue-next'
import * as SurveyApi from '@/api/survey'
import type { SurveyStatisticsVO } from '@/api/survey/types'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'

const route = useRoute()
const router = useRouter()

// Survey navigation tabs configuration
const surveyNavTabs = ref([{ key: 'all', label: 'All Surveys' }])
const selectedNavTab = ref('all')

// Reactive data
const loading = ref(false)
const hasError = ref(false)
const hasPermission = ref(false)
const statistics = ref<SurveyStatisticsVO | null>(null)

// Computed properties
const surveyId = computed(() => Number(route.params.id))

// Methods
const handleGoBackToDetail = () => {
  router.push(`/survey/detail/${surveyId.value}`)
}

// Check permissions and get statistics data
const fetchStatistics = async () => {
  loading.value = true
  hasError.value = false
  hasPermission.value = false

  try {
    // First check if user has permission to view statistics
    const permissionResponse = await SurveyApi.canViewStatistics(surveyId.value)

    if (!permissionResponse.data) {
      // No permission to view statistics, show submission success page directly
      hasError.value = true
      return
    }

    hasPermission.value = true

    // Has permission, get statistics data
    const response = await SurveyApi.getStatistics(surveyId.value)
    statistics.value = response.data
  } catch (error) {
    console.error('Failed to fetch statistics or check permissions:', error)
    hasError.value = true
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchStatistics()
})
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll :header-height="80" :footer-height="80">
        <!-- Header: Success message -->
        <!--        <template #header>-->
        <!--          <div class="w-full h-full px-6 py-4 bg-white border-b flex items-center justify-center">-->
        <!--            <div class="flex items-center space-x-3">-->
        <!--              <CheckCircle class="w-8 h-8 text-green-500" />-->
        <!--              <div>-->
        <!--                <h1 class="text-xl font-bold text-gray-900">Survey Submitted Successfully</h1>-->
        <!--                <p class="text-sm text-gray-600">Thank you for your participation!</p>-->
        <!--              </div>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </template>-->

        <!-- Loading state -->
        <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
          <div class="flex flex-col items-center space-y-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span class="text-gray-600 text-lg">Loading statistics data...</span>
          </div>
        </div>

        <!-- Main content area: Statistics data -->
        <div v-else class="p-6">
          <!-- Error state or no permission - only show submission success and return button -->
          <div v-if="hasError" class="flex items-center justify-center min-h-[400px]">
            <div class="text-center max-w-md mx-auto px-4">
              <CheckCircle class="w-16 h-16 text-green-500 mx-auto mb-6" />
              <h1 class="text-2xl font-bold text-gray-900 mb-4">Submission Successful!</h1>
              <p class="text-gray-600 mb-8"
                >Your survey has been successfully submitted, thank you for your participation!</p
              >
              <Button @click="handleGoBackToDetail" class="w-full">
                <ArrowLeft class="w-4 h-4 mr-2" />
                Back to Survey Details
              </Button>
            </div>
          </div>

          <!-- Statistics data display -->
          <div v-else class="survey-result-content">
            <!-- Top navigation -->
            <div class="bg-white border-b border-gray-200">
              <div class="max-w-4xl mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <Button @click="handleGoBackToDetail" variant="ghost" size="sm">
                      <ArrowLeft class="w-4 h-4 mr-2" />
                      Back to Survey Details
                    </Button>
                    <div>
                      <h1 class="text-xl font-semibold text-gray-900">Survey Statistics Results</h1>
                      <p class="text-sm text-gray-600">{{ statistics?.instanceName }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Statistics data overview -->
            <div class="max-w-4xl mx-auto px-4 py-8">
              <!-- Basic statistics cards -->
              <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <!-- Total responses -->
                <Card class="text-center">
                  <CardHeader>
                    <Users class="w-8 h-8 mx-auto text-blue-500 mb-2" />
                    <CardTitle class="text-2xl font-bold text-blue-600">
                      {{ statistics?.totalResponses || 0 }}
                    </CardTitle>
                    <CardDescription>Total Responses</CardDescription>
                  </CardHeader>
                </Card>

                <!-- Valid responses -->
                <Card class="text-center">
                  <CardHeader>
                    <CheckCircle class="w-8 h-8 mx-auto text-green-500 mb-2" />
                    <CardTitle class="text-2xl font-bold text-green-600">
                      {{ statistics?.validResponses || 0 }}
                    </CardTitle>
                    <CardDescription>Valid Responses</CardDescription>
                  </CardHeader>
                </Card>

                <!-- Average score -->
                <Card class="text-center">
                  <CardHeader>
                    <BarChart3 class="w-8 h-8 mx-auto text-yellow-500 mb-2" />
                    <CardTitle class="text-2xl font-bold text-yellow-600">
                      {{ statistics?.averageScore?.toFixed(1) || '0.0' }}
                    </CardTitle>
                    <CardDescription>Average Score</CardDescription>
                  </CardHeader>
                </Card>

                <!-- Completion rate -->
                <Card class="text-center">
                  <CardHeader>
                    <TrendingUp class="w-8 h-8 mx-auto text-purple-500 mb-2" />
                    <CardTitle class="text-2xl font-bold text-purple-600">
                      {{ (statistics?.completionRate * 100)?.toFixed(1) || '0.0' }}%
                    </CardTitle>
                    <CardDescription>Completion Rate</CardDescription>
                  </CardHeader>
                </Card>
              </div>

              <!-- Question statistics details -->
              <div
                v-if="statistics?.questionStatistics && statistics.questionStatistics.length > 0"
                class="space-y-6"
              >
                <h2 class="text-lg font-semibold text-gray-900 mb-4"
                  >Question Statistics Details</h2
                >

                <div class="space-y-4">
                  <Card
                    v-for="question in statistics.questionStatistics"
                    :key="question.questionId"
                  >
                    <CardHeader>
                      <CardTitle class="text-lg">{{ question.questionTitle }}</CardTitle>
                      <div class="flex items-center justify-between">
                        <Badge variant="outline">{{ question.questionType }}</Badge>
                        <div class="text-sm text-gray-600">
                          Total Answers: {{ question.totalAnswers }} | Average Score:
                          {{ question.averageScore?.toFixed(1) || '0.0' }}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div v-if="question.optionStatistics && question.optionStatistics.length > 0">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Option Distribution:</h4>
                        <div class="space-y-2">
                          <div
                            v-for="option in question.optionStatistics"
                            :key="option.optionValue"
                            class="flex items-center justify-between"
                          >
                            <span class="text-sm text-gray-600 flex-1">{{
                              option.optionText
                            }}</span>
                            <div class="flex items-center space-x-2 flex-1">
                              <Progress :value="option.percentage" class="flex-1 h-2" />
                              <span class="text-xs text-gray-500 w-16"
                                >{{ option.count }} times ({{
                                  option.percentage.toFixed(1)
                                }}%)</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer: Return button -->
        <!--        <template #footer>-->
        <!--          <div class="w-full h-full px-6 py-4 flex items-center justify-center">-->
        <!--            <Button @click="handleGoBackToDetail" variant="outline" size="default" class="px-6">-->
        <!--              <ArrowLeft class="w-4 h-4 mr-2" />-->
        <!--              Back to Survey Details-->
        <!--            </Button>-->
        <!--          </div>-->
        <!--        </template>-->
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
.survey-result-container {
  .survey-result-content {
    min-height: 100vh;
  }
}
</style>
