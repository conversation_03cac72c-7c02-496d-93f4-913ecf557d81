<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSurveyStore } from '@/store/modules/survey'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  ArrowRight,
  Save,
  Send,
  Clock,
  CheckCircle,
  AlertCircle,
  Home
} from 'lucide-vue-next'
import QuestionRenderer from '../components/QuestionRenderer.vue'
import { useDialog } from '@/hooks/web/useDialog'
import { useToast } from '@/components/ui/toast/use-toast'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Breadcrumb } from '@/components/ui/breadcrumb'
import { QuestionTypeMap } from '@/api/survey/types'

const route = useRoute()
const router = useRouter()
const surveyStore = useSurveyStore()
const { alert, confirm, confirmLeave } = useDialog()
const { toast } = useToast()

// Reactive data
const submitting = ref(false)
const autoSaveTimer = ref<NodeJS.Timeout | null>(null)
const startTime = ref<Date>(new Date())

// Computed properties
const surveyId = computed(() => Number(route.params.id))
const currentSurvey = computed(() => surveyStore.currentSurvey)
const currentQuestion = computed(() => surveyStore.currentQuestion)
const currentQuestionIndex = computed(() => surveyStore.currentQuestionIndex)
const progressPercentage = computed(() => surveyStore.progressPercentage)
const canSubmit = computed(() => surveyStore.canSubmit)
const answeredCount = computed(() => surveyStore.answeredCount)

// Get English question type name based on questionType enum value
const currentQuestionTypeName = computed(() => {
  if (!currentQuestion.value?.questionType) return ''
  return QuestionTypeMap[currentQuestion.value.questionType] || 'Unknown'
})

const isFirstQuestion = computed(() => currentQuestionIndex.value === 0)
const isLastQuestion = computed(() => {
  if (!currentSurvey.value?.questions) return true
  return currentQuestionIndex.value === currentSurvey.value.questions.length - 1
})

const timeSpent = computed(() => {
  const now = new Date()
  const diff = now.getTime() - startTime.value.getTime()
  const minutes = Math.floor(diff / 60000)
  const seconds = Math.floor((diff % 60000) / 1000)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

// Monitor currentSurvey changes
watch(
  currentSurvey,
  (newValue, oldValue) => {
    console.log('currentSurvey changed:', { oldValue, newValue })
  },
  { immediate: true }
)

// Monitor loading state changes
watch(
  () => surveyStore.loading,
  (newValue, oldValue) => {
    console.log('surveyStore.loading changed:', { oldValue, newValue })
  },
  { immediate: true }
)

// Methods
const handlePrevQuestion = () => {
  surveyStore.prevQuestion()
}

const handleNextQuestion = () => {
  surveyStore.nextQuestion()
}

const handleSaveDraft = async () => {
  try {
    surveyStore.saveDraft()
    // surveyStore.clearDraft()
    // Show save success message
    // toast({
    //   title: 'Save Successful',
    //   description: 'Draft saved, you can continue later',
    //   variant: 'default'
    // })
  } catch (error) {
    console.error('Failed to save draft:', error)
    // Show save failed message
    // toast({
    //   title: 'Save Failed',
    //   description: 'Failed to save draft, please try again later',
    //   variant: 'destructive'
    // })
  }
}

const handleSubmit = async () => {
  console.log('handleSubmit called')

  if (!canSubmit.value) {
    console.log('Cannot submit - validation failed')
    alert('Please complete all required questions before submitting')
    return
  }

  console.log('Showing confirmation dialog')
  const confirmed = await confirm(
    'Are you sure you want to submit the survey? You cannot modify it after submission.'
  )
  console.log('Confirmation result:', confirmed)

  if (!confirmed) {
    console.log('User cancelled submission')
    return
  }

  console.log('Starting submission')
  submitting.value = true
  try {
    await surveyStore.submitSurvey()
    console.log('Survey submitted successfully')

    // Clear draft cache after successful submission
    surveyStore.clearDraft()

    // Redirect to result page after successful submission
    await router.push(`/survey/result/${surveyId.value}`)
  } catch (error) {
    console.error('Failed to submit survey:', error)
    alert('Submission failed, please try again later')
  } finally {
    submitting.value = false
  }
}

const handleGoHome = async () => {
  const confirmed = await confirmLeave(
    'Are you sure you want to leave? Unsaved answers will be lost.'
  )
  if (confirmed) {
    router.push('/my-center/index?tab=surveys')
  }
}

const handleBackToDetail = async () => {
  const confirmed = await confirmLeave(
    'Are you sure you want to leave? Unsaved answers will be lost.'
  )
  if (confirmed) {
    router.push(`/survey/detail/${surveyId.value}`)
  }
}

const handleAnswerUpdate = (questionId: number, answer: any) => {
  surveyStore.updateAnswer(questionId, answer)
}

const setupAutoSave = () => {
  // Auto-save every 30 seconds
  autoSaveTimer.value = setInterval(() => {
    handleSaveDraft()
  }, 30000)
}

const clearAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
    autoSaveTimer.value = null
  }
}

// Lifecycle
onMounted(async () => {
  console.log('Fill page mounted, surveyId:', surveyId.value)
  console.log('Initial surveyStore.loading:', surveyStore.loading)
  console.log('Initial currentSurvey:', currentSurvey.value)

  // Disable browser default leave confirmation dialog
  disableBeforeUnload()

  try {
    console.log('Calling fetchSurveyDetail...')

    // Check if explicitly requesting to clear draft (only clear when URL has 'new=true')
    const isNewSession = route.query.new === 'true'
    console.log('Is new session (clear draft):', isNewSession)

    // Get survey details, auto-detect and load draft
    await surveyStore.fetchSurveyDetail(surveyId.value, isNewSession)
    console.log('fetchSurveyDetail completed')
    console.log('Final currentSurvey:', currentSurvey.value)
    console.log('Final surveyStore.loading:', surveyStore.loading)
    // setupAutoSave()
  } catch (error) {
    console.error('Failed to load survey:', error)
    router.push('/my-center/index?tab=surveys')
  }
})

onBeforeUnmount(() => {
  // clearAutoSave()
  // Save draft when page is unloaded
  // handleSaveDraft()
  // Restore browser default leave confirmation dialog
  enableBeforeUnload()
})

// Disable browser default form leave confirmation dialog
let beforeUnloadHandler: ((event: BeforeUnloadEvent) => void) | null = null

const disableBeforeUnload = () => {
  beforeUnloadHandler = (event: BeforeUnloadEvent) => {
    // Don't set any return value, so browser won't show confirmation dialog
    delete event.returnValue
  }
  window.addEventListener('beforeunload', beforeUnloadHandler)
}

const enableBeforeUnload = () => {
  if (beforeUnloadHandler) {
    window.removeEventListener('beforeunload', beforeUnloadHandler)
    beforeUnloadHandler = null
  }
}
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="flex items-center px-8 gap-3 w-full h-full">
            <!-- Left Section: Breadcrumb and Status -->
            <div class="flex items-center gap-3">
              <Breadcrumb />
              <Badge v-if="currentSurvey" variant="default" class="text-xs font-medium">
                In Progress
              </Badge>
            </div>

            <!-- Center Section: Survey Title and Progress -->
            <div class="flex-1 flex flex-col justify-center px-4">
              <div v-if="currentSurvey" class="text-center">
                <h1 class="text-lg font-semibold text-gray-900 truncate">{{
                  currentSurvey.name
                }}</h1>
                <!--                <div class="flex items-center justify-center gap-4 mt-3">-->
                <!--                  <span class="text-sm text-gray-600">-->
                <!--                    Question {{ currentQuestionIndex + 1 }} / {{ currentSurvey.questions?.length || 0 }}-->
                <!--                  </span>-->
                <!--                  <span class="text-sm text-gray-600">{{ progressPercentage }}%</span>-->
                <!--                  <Progress :value="progressPercentage" class="w-32 h-1.5" />-->
                <!--                </div>-->
              </div>
            </div>

            <!-- Right Section: Action Buttons -->
            <div class="flex items-center gap-2">
              <!-- Primary Action Buttons -->
              <div class="flex gap-2">
                <Button
                  @click="handleSaveDraft"
                  variant="outline"
                  size="sm"
                  class="text-xs font-medium"
                >
                  <Save class="w-4 h-4 mr-1" />
                  Save Draft
                </Button>

                <Button
                  @click="handleBackToDetail"
                  variant="outline"
                  size="sm"
                  class="text-xs font-medium"
                >
                  <ArrowLeft class="w-4 h-4 mr-1" />
                  Back to Detail
                </Button>

                <Button
                  v-if="canSubmit"
                  @click="handleSubmit"
                  size="sm"
                  class="text-xs font-medium"
                >
                  <Send class="w-4 h-4 mr-1" />
                  Submit Survey
                </Button>
              </div>

              <Separator orientation="vertical" class="h-6" />

              <!-- Secondary Action Buttons -->
              <div class="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger as-child>
                    <Button variant="ghost" size="icon" @click="handleGoHome" class="h-8 w-8">
                      <Home class="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Back to Survey Center</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </template>

        <template #default>
          <!-- Loading state -->
          <div v-if="surveyStore.loading" class="flex items-center justify-center min-h-[400px]">
            <div class="flex flex-col items-center space-y-4">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span class="text-gray-600 text-lg">Loading survey...</span>
            </div>
          </div>

          <!-- Survey filling interface -->
          <div
            v-else-if="
              currentSurvey && currentSurvey.questions && currentSurvey.questions.length > 0
            "
            class="px-8 py-6"
          >
            <div class="max-w-4xl mx-auto">
              <!-- Required question reminder -->
              <Alert
                v-if="
                  currentQuestion?.required && !surveyStore.answers[currentQuestion.id]?.answerValue
                "
                class="mb-6"
              >
                <AlertCircle class="h-4 w-4" />
                <AlertDescription
                  >This is a required question, please answer before continuing.
                </AlertDescription>
              </Alert>

              <!-- Question content -->
              <Card class="shadow-sm">
                <CardHeader class="pb-6">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <CardTitle class="text-xl">
                        Question {{ currentQuestionIndex + 1 }}
                        <span v-if="currentQuestion?.required" class="text-red-500 ml-1">*</span>
                      </CardTitle>
                      <CardDescription class="mt-3 text-base">
                        {{ currentQuestion?.title }}
                      </CardDescription>
                      <div v-if="currentQuestion?.description" class="mt-4 text-sm text-gray-600">
                        {{ currentQuestion.description }}
                      </div>
                    </div>
                    <Badge variant="outline" class="ml-4">
                      {{ currentQuestionTypeName }}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent class="pt-0">
                  <QuestionRenderer
                    v-if="currentQuestion"
                    :question="currentQuestion"
                    :answer="surveyStore.answers[currentQuestion.id]"
                    @update="handleAnswerUpdate"
                  />
                </CardContent>
              </Card>
            </div>
          </div>

          <!-- Survey exists but has no questions -->
          <div
            v-else-if="
              currentSurvey && (!currentSurvey.questions || currentSurvey.questions.length === 0)
            "
            class="p-6 flex items-center justify-center h-full"
          >
            <div class="text-center">
              <AlertCircle class="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">Survey has no questions</h3>
              <p class="text-gray-600 mb-4"
                >This survey has no questions added yet, please contact the administrator.</p
              >
            </div>
          </div>

          <!-- Error state -->
          <div v-else class="p-6 flex items-center justify-center h-full">
            <div class="text-center">
              <AlertCircle class="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h3 class="text-lg font-medium text-gray-900 mb-2">Survey not found</h3>
              <p class="text-gray-600 mb-4"
                >The survey you are looking for does not exist or is no longer available.</p
              >
            </div>
          </div>
        </template>

        <!-- Footer: Navigation buttons -->
        <template #footer>
          <div
            v-if="currentSurvey && currentSurvey.questions && currentSurvey.questions.length > 0"
            class="w-full h-full px-8 bg-white border-t flex items-center"
          >
            <div class="flex w-full items-center justify-between max-w-4xl mx-auto">
              <div class="flex items-center space-x-2">
                <Button
                  @click="handlePrevQuestion"
                  :disabled="isFirstQuestion"
                  variant="outline"
                  size="sm"
                >
                  <ArrowLeft class="w-4 h-4 mr-2" />
                  Previous
                </Button>
                <!--                <div class="text-sm text-gray-600 ml-4">-->
                <!--                  <Clock class="w-4 h-4 inline mr-1"/>-->
                <!--                  {{ timeSpent }}-->
                <!--                </div>-->
              </div>

              <span class="text-sm text-gray-600">
                Question {{ currentQuestionIndex + 1 }} / {{ currentSurvey.questions?.length || 0 }}
              </span>
              <div class="flex items-center space-x-2">
                <Button :disabled="isLastQuestion" @click="handleNextQuestion" variant="outline">
                  Next
                  <ArrowRight class="w-4 h-4 ml-2" />
                </Button>

                <!--                <Button-->
                <!--                  v-else-->
                <!--                  @click="handleSubmit"-->
                <!--                  :disabled="!canSubmit || submitting"-->
                <!--                  :loading="submitting"-->
                <!--                >-->
                <!--                  <Send class="w-4 h-4 mr-2" />-->
                <!--                  {{ submitting ? 'Submitting...' : 'Submit Survey' }}-->
                <!--                </Button>-->
              </div>
            </div>
          </div>
          <div v-else class="w-full h-full px-8 bg-white border-t flex items-center justify-center">
            <Button @click="router.push('/my-center/index?tab=surveys')" variant="outline">
              Back to Survey List
            </Button>
          </div>
        </template>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
.survey-fill-container {
  .survey-fill-content {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }
}
</style>
