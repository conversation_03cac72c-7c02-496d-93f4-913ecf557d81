<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useSurveyStore } from '@/store/modules/survey'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Search,
  ClipboardList,
  CheckCircle,
  Clock,
  Users,
  Calendar,
  TrendingUp,
  FileText,
  ArrowRight
} from 'lucide-vue-next'
import type { SurveyInstanceVO } from '@/api/survey/types'

const router = useRouter()
const surveyStore = useSurveyStore()

// Reactive data
const searchQuery = ref('')
const selectedTab = ref('available')
const loading = ref(false)

// Computed properties
const filteredSurveys = computed(() => {
  let surveys = surveyStore.surveyList

  // Filter by selected tab
  switch (selectedTab.value) {
    case 'available':
      surveys = surveys.filter(
        (survey) => survey.canParticipate && survey.userSubmissionCount === 0
      )
      break
    case 'submitted':
      surveys = surveys.filter((survey) => survey.userSubmissionCount > 0)
      break
    case 'expired':
      surveys = surveys.filter((survey) => new Date(survey.endTime) < new Date())
      break
    case 'all':
    default:
      // Show all surveys
      break
  }

  // Search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    surveys = surveys.filter(
      (survey) =>
        survey.name.toLowerCase().includes(query) ||
        survey.description.toLowerCase().includes(query)
    )
  }

  return surveys
})

const surveyStats = computed(() => surveyStore.surveyStats)

// Methods
const handleSearch = () => {
  // Search logic is handled in computed
}

const handleTabChange = (tab: string) => {
  selectedTab.value = tab
}

const handleFillSurvey = (survey: SurveyInstanceVO) => {
  router.push(`/survey/fill/${survey.id}`)
}

const handleViewResult = (survey: SurveyInstanceVO) => {
  router.push(`/survey/result/${survey.id}`)
}

const handleViewRecords = () => {
  router.push('/my-center/index?tab=surveys&surveyStatus=submitted')
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getRemainingTime = (endTime: string) => {
  const now = new Date()
  const end = new Date(endTime)
  const diff = end.getTime() - now.getTime()

  if (diff <= 0) return 'Ended'

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

  if (days > 0) return `Ends in ${days} days`
  if (hours > 0) return `Ends in ${hours} hours`
  return 'Ending soon'
}

const getSurveyStatusColor = (survey: SurveyInstanceVO) => {
  if (survey.userSubmissionCount > 0) return 'default'
  if (!survey.canParticipate) return 'secondary'
  if (new Date(survey.endTime) < new Date()) return 'destructive'
  return 'default'
}

const getSurveyStatusText = (survey: SurveyInstanceVO) => {
  if (survey.userSubmissionCount > 0) return 'Submitted'
  if (!survey.canParticipate) return 'Not Available'
  if (new Date(survey.endTime) < new Date()) return 'Expired'
  return 'Available'
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  try {
    await surveyStore.fetchSurveyList()
  } catch (error) {
    console.error('Failed to load surveys:', error)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="survey-center-container p-6 space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Survey Center</h1>
        <p class="text-gray-600 mt-1">Participate in surveys and manage your responses</p>
      </div>
      <Button @click="handleViewRecords" variant="outline">
        <FileText class="w-4 h-4 mr-2" />
        View My Records
      </Button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Total Surveys</CardTitle>
          <ClipboardList class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ surveyStats.total }}</div>
          <p class="text-xs text-muted-foreground">Available surveys</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Available</CardTitle>
          <Clock class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ surveyStats.available }}</div>
          <p class="text-xs text-muted-foreground">Ready to participate</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Submitted</CardTitle>
          <CheckCircle class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ surveyStats.submitted }}</div>
          <p class="text-xs text-muted-foreground">Completed surveys</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Completion Rate</CardTitle>
          <TrendingUp class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ surveyStats.completionRate }}%</div>
          <p class="text-xs text-muted-foreground">Your progress</p>
        </CardContent>
      </Card>
    </div>

    <!-- 搜索栏 -->
    <div class="flex items-center gap-4">
      <div class="flex-1 max-w-md">
        <div class="relative">
          <Input
            v-model="searchQuery"
            placeholder="Search surveys..."
            class="pl-10"
            @keyup.enter="handleSearch"
          />
          <Search
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
          />
        </div>
      </div>

      <Badge variant="outline" class="text-xs"> {{ filteredSurveys.length }} surveys found </Badge>
    </div>

    <!-- 问卷列表 -->
    <Tabs :value="selectedTab" @update:value="handleTabChange">
      <TabsList class="grid w-full grid-cols-4">
        <TabsTrigger value="available">Available</TabsTrigger>
        <TabsTrigger value="submitted">Submitted</TabsTrigger>
        <TabsTrigger value="expired">Expired</TabsTrigger>
        <TabsTrigger value="all">All</TabsTrigger>
      </TabsList>

      <TabsContent :value="selectedTab" class="mt-6">
        <div v-if="loading" class="text-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p class="text-gray-500 mt-2">Loading surveys...</p>
        </div>

        <div
          v-else-if="filteredSurveys.length > 0"
          class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
        >
          <Card
            v-for="survey in filteredSurveys"
            :key="survey.id"
            class="survey-card cursor-pointer hover:shadow-lg transition-all duration-200"
          >
            <CardHeader>
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <CardTitle class="text-lg line-clamp-2">{{ survey.name }}</CardTitle>
                  <CardDescription class="mt-2 line-clamp-3">
                    {{ survey.description }}
                  </CardDescription>
                </div>
                <Badge :variant="getSurveyStatusColor(survey)" class="ml-2">
                  {{ getSurveyStatusText(survey) }}
                </Badge>
              </div>
            </CardHeader>

            <CardContent>
              <div class="space-y-3">
                <!-- 时间信息 -->
                <div class="flex items-center text-sm text-gray-600">
                  <Calendar class="w-4 h-4 mr-2" />
                  <span>{{ formatDate(survey.startTime) }} - {{ formatDate(survey.endTime) }}</span>
                </div>

                <!-- 参与信息 -->
                <div class="flex items-center text-sm text-gray-600">
                  <Users class="w-4 h-4 mr-2" />
                  <span
                    >{{ survey.responseCount }} /
                    {{ survey.maxResponses || '∞' }} participants</span
                  >
                </div>

                <!-- 剩余时间 -->
                <div class="flex items-center text-sm text-gray-600">
                  <Clock class="w-4 h-4 mr-2" />
                  <span>{{ getRemainingTime(survey.endTime) }}</span>
                </div>

                <!-- 操作按钮 -->
                <div class="flex gap-2 pt-2">
                  <Button
                    v-if="survey.canParticipate && survey.userSubmissionCount === 0"
                    @click="handleFillSurvey(survey)"
                    class="flex-1"
                    size="sm"
                  >
                    Participate
                    <ArrowRight class="w-4 h-4 ml-2" />
                  </Button>

                  <Button
                    v-else-if="survey.userSubmissionCount > 0"
                    @click="handleViewResult(survey)"
                    variant="outline"
                    class="flex-1"
                    size="sm"
                  >
                    View Result
                  </Button>

                  <Button v-else disabled variant="secondary" class="flex-1" size="sm">
                    Not Available
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-12">
          <div
            class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center"
          >
            <ClipboardList class="w-8 h-8 text-gray-400" />
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No surveys found</h3>
          <p class="text-gray-500 mb-4">
            {{
              selectedTab === 'available'
                ? 'No available surveys at the moment'
                : selectedTab === 'submitted'
                  ? "You haven't submitted any surveys yet"
                  : selectedTab === 'expired'
                    ? 'No expired surveys'
                    : 'No surveys match your search criteria'
            }}
          </p>
        </div>
      </TabsContent>
    </Tabs>
  </div>
</template>

<style scoped lang="scss">
.survey-center-container {
  .survey-card {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
</style>
