<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="flex items-center px-4 gap-3 w-full h-full">
            <!-- Left Section: Breadcrumb and Status -->
            <div class="flex items-center gap-3">
              <Breadcrumb />
              <Badge
                v-if="surveyDetail"
                :variant="statusConfig.variant"
                class="text-xs font-medium"
              >
                {{ statusConfig.text }}
              </Badge>
            </div>

            <!-- Center Section: Survey Title -->
            <div class="flex-1 flex justify-center">
              <div v-if="surveyDetail" class="text-center">
                <h1 class="text-lg font-semibold text-gray-900">{{ surveyDetail.name }}</h1>
              </div>
            </div>

            <!-- Right Section: Action Buttons -->
            <div class="flex items-center gap-2">
              <!-- Primary Action Buttons -->
              <div class="flex gap-2">
                <Button
                  v-if="canParticipate"
                  @click="startSurvey()"
                  size="sm"
                  class="text-xs font-medium"
                >
                  <Play class="w-4 h-4 mr-1" />
                  {{ hasDraft ? 'Continue Survey' : 'Start Survey' }}
                </Button>

                <Button
                  v-if="canParticipate && hasDraft"
                  @click="startSurvey(true)"
                  variant="outline"
                  size="sm"
                  class="text-xs font-medium"
                >
                  <ClipboardList class="w-4 h-4 mr-1" />
                  Start New Survey
                </Button>
              </div>

              <Separator orientation="vertical" class="h-6" v-if="canParticipate" />

              <!-- Secondary Action Buttons -->
              <div class="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger as-child>
                    <Button variant="ghost" size="icon" @click="goBack" class="h-8 w-8">
                      <ArrowLeft class="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Back to Survey List</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </template>

        <!-- Content Area -->
        <div class="p-6">
          <!-- Loading state -->
          <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
            <div class="flex flex-col items-center space-y-4">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span class="text-gray-600 text-lg">Loading survey details...</span>
            </div>
          </div>

          <!-- Survey Details -->
          <div v-else-if="surveyDetail">
            <!-- Survey Information -->
            <Card>
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <Info class="w-5 h-5 mr-2" />
                  Survey Information
                </CardTitle>
              </CardHeader>
              <CardContent class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Survey Description -->
                  <div class="md:col-span-2">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Survey Description</Label>
                    <p class="mt-2 text-gray-600 leading-relaxed">
                      {{ surveyDetail.description || 'No description available' }}
                    </p>
                  </div>

                  <!-- Status -->
                  <div>
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Survey Status</Label>
                    <div class="mt-2 flex items-start">
                      <!--                <component :is="statusConfig.icon" class="w-4 h-4 mr-2 mt-0.5" :class="statusConfig.color" />-->
                      <span class="text-sm text-gray-600">{{ statusConfig.text }}</span>
                    </div>
                  </div>

                  <!-- Time Range -->
                  <div>
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Time Range</Label>
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Calendar class="w-4 h-4 mr-2" />
                      {{ formatDate(surveyDetail.startTime) }} -
                      {{ formatDate(surveyDetail.endTime) }}
                    </div>
                  </div>

                  <!-- Response Count -->
                  <div>
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Response Count</Label>
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Users class="w-4 h-4 mr-2" />
                      {{ surveyDetail.responseCount }} /
                      {{ surveyDetail.maxResponses || 'Unlimited' }}
                    </div>
                  </div>

                  <!-- Submission Limit -->
                  <div>
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Submission Limit</Label>
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <FileText class="w-4 h-4 mr-2" />
                      {{ getSubmissionFrequencyText(surveyDetail.submissionFrequency) }}
                      <span v-if="surveyDetail.submissionFrequency === 2">
                        (Max {{ surveyDetail.maxSubmissions }} times)
                      </span>
                    </div>
                  </div>

                  <!-- Anonymous -->
                  <div>
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Anonymous</Label>
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Eye class="w-4 h-4 mr-2" />
                      {{ surveyDetail.anonymousEnabled ? 'Anonymous' : 'Named' }}
                    </div>
                  </div>

                  <!-- Creator -->
                  <div>
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Creator</Label>
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <User class="w-4 h-4 mr-2" />
                      {{ surveyDetail.creatorName || 'System' }}
                    </div>
                  </div>

                  <!-- Remaining Time -->
                  <div>
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Remaining Time</Label>
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Clock class="w-4 h-4 mr-2" />
                      {{ getRemainingTime(surveyDetail.endTime) }}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- My Participation Records -->
            <Card class="mt-8">
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <History class="w-5 h-5 mr-2" />
                  My Participation Records
                </CardTitle>
              </CardHeader>
              <CardContent>
                <!-- Participation Records Table -->
                <div v-if="participationRecords.length > 0" class="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead class="w-[180px]">Submit Time</TableHead>
                        <TableHead class="w-[120px]">Status</TableHead>
                        <!--                  <TableHead class="w-[150px]">Submission Number</TableHead>-->
                        <TableHead class="w-[120px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow v-for="record in participationRecords" :key="record.id">
                        <TableCell>
                          <span class="text-sm text-gray-600">
                            {{ formatDateTime(record.submitTime) }}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" class="text-xs">
                            {{ getResponseStatusText(record.status) }}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            @click="viewRecord(record.id)"
                            variant="outline"
                            size="sm"
                            class="text-xs"
                          >
                            <Eye class="w-3 h-3 mr-1" />
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <!-- No Participation Records -->
                <div v-else class="text-center py-8 text-gray-500">
                  <ClipboardList class="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p class="text-lg font-medium mb-2">No participation records</p>
                  <p class="text-sm">You haven't participated in this survey yet</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Error state -->
          <div v-else class="flex items-center justify-center min-h-[400px]">
            <div class="text-center py-12">
              <AlertCircle class="w-16 h-16 mx-auto mb-4 text-red-500" />
              <h2 class="text-xl font-semibold text-gray-900 mb-2">Survey not found</h2>
              <p class="text-gray-600 mb-4"
                >The survey you are looking for does not exist or has been deleted.</p
              >
            </div>
          </div>
        </div>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'

import { Separator } from '@/components/ui/separator'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Breadcrumb } from '@/components/ui/breadcrumb'
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell
} from '@/components/ui/table'
import {
  Info,
  Calendar,
  Users,
  FileText,
  User,
  Clock,
  History,
  Eye,
  Play,
  ArrowLeft,
  AlertCircle,
  ClipboardList
} from 'lucide-vue-next'
import { SurveyApi } from '@/api/survey'
import type { SurveyInstanceVO } from '@/api/survey/types'
import { formatToDate, formatToDateTime } from '@/utils/dateUtil'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'

// 创建格式化函数的别名
const formatDate = (date: string) => formatToDate(date)
const formatDateTime = (date: string) => formatToDateTime(date)

// Get submission frequency text based on enum value
const getSubmissionFrequencyText = (frequency: number) => {
  return SurveyApi.formatSubmissionFrequency(frequency)
}

// Get response status text based on enum value
const getResponseStatusText = (status: number) => {
  return SurveyApi.formatResponseStatus(status)
}

const route = useRoute()
const router = useRouter()

// Reactive data
const loading = ref(true)
const surveyDetail = ref<SurveyInstanceVO | null>(null)
const participationRecords = ref<any[]>([])
const hasDraft = ref(false)

// Get survey ID
const surveyId = computed(() => route.params.id as string)

// Compute status configuration
const statusConfig = computed(() => {
  if (!surveyDetail.value)
    return { variant: 'secondary', text: 'Unknown', icon: 'AlertCircle', color: 'text-gray-500' }

  // Use API provided status formatting function
  const statusText = SurveyApi.formatSurveyStatus(surveyDetail.value.status)

  // Return corresponding configuration based on survey status
  switch (surveyDetail.value.status) {
    case 0: // Unpublished
      return { variant: 'secondary', text: statusText, icon: 'Clock', color: 'text-gray-500' }
    case 1: // In Progress
      return { variant: 'default', text: statusText, icon: 'CheckCircle', color: 'text-green-500' }
    case 2: // Ended
      return {
        variant: 'destructive',
        text: statusText,
        icon: 'AlertCircle',
        color: 'text-red-500'
      }
    case 3: // Draft
      return { variant: 'outline', text: statusText, icon: 'FileText', color: 'text-yellow-500' }
    default:
      return { variant: 'secondary', text: statusText, icon: 'AlertCircle', color: 'text-gray-500' }
  }
})

// Compute whether can participate
const canParticipate = computed(() => {
  if (!surveyDetail.value) return false

  // Use API provided utility function to check if can submit
  return SurveyApi.canSubmitSurvey(surveyDetail.value)
})

// Participation restriction message
const participationMessage = computed(() => {
  if (!surveyDetail.value) return ''

  const survey = surveyDetail.value
  const now = new Date()
  const startTime = new Date(survey.startTime)
  const endTime = new Date(survey.endTime)

  // Check survey status
  if (survey.status === 0) {
    return 'Survey not yet published'
  } else if (survey.status === 2) {
    return `Survey ended on ${formatDate(survey.endTime)}`
  } else if (survey.status === 3) {
    return 'Survey is in draft status'
  }

  // Check time range
  if (now < startTime) {
    return `Survey will start on ${formatDate(survey.startTime)}`
  } else if (now > endTime) {
    return `Survey ended on ${formatDate(survey.endTime)}`
  }

  // Check submission restrictions
  if (survey.submissionFrequency === 1 && survey.userSubmissionCount > 0) {
    return 'You have already participated in this survey, which only allows one submission'
  } else if (survey.submissionFrequency === 2) {
    const remaining = survey.maxSubmissions - survey.userSubmissionCount
    if (remaining <= 0) {
      return `You have reached the maximum submission limit (${survey.maxSubmissions})`
    }
  }

  // Check response count limit
  if (survey.maxResponses > 0 && survey.responseCount >= survey.maxResponses) {
    return 'Survey response count has reached the limit'
  }

  return ''
})

// Get remaining time
const getRemainingTime = (endTime: string) => {
  if (!surveyDetail.value) return 'Unknown'

  const now = new Date()
  const end = new Date(endTime)
  const diff = end.getTime() - now.getTime()

  // If survey has ended
  if (surveyDetail.value.status === 2 || diff <= 0) {
    return 'Ended'
  }

  // If survey hasn't started
  if (surveyDetail.value.status === 0) {
    return 'Not Started'
  }

  // If in draft status
  if (surveyDetail.value.status === 3) {
    return 'Draft'
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (days > 0) return `${days} days ${hours} hours remaining`
  if (hours > 0) return `${hours} hours ${minutes} minutes remaining`
  if (minutes > 0) return `${minutes} minutes remaining`

  return 'Ending soon'
}

// Get survey details
const fetchSurveyDetail = async () => {
  try {
    loading.value = true

    // Call real API to get survey details
    console.log('Fetching survey detail for ID:', surveyId.value)
    const response = await SurveyApi.getInstance(parseInt(surveyId.value))
    console.log('Survey detail API response:', response)

    if (response) {
      surveyDetail.value = response
      // Get participation records
      await fetchParticipationRecords()
    } else {
      console.error('No survey data in response')
      surveyDetail.value = null
    }
  } catch (error) {
    console.error('Failed to fetch survey detail:', error)
    surveyDetail.value = null
  } finally {
    loading.value = false
  }
}

// Check if local draft exists
const checkLocalDraft = () => {
  if (surveyDetail.value?.id) {
    const draftKey = `survey_draft_${surveyDetail.value.id}`
    const draftStr = localStorage.getItem(draftKey)

    if (draftStr) {
      try {
        const draftData = JSON.parse(draftStr)
        // Check if draft is expired (24 hours)
        const isExpired = Date.now() - draftData.timestamp > 24 * 60 * 60 * 1000
        hasDraft.value =
          !isExpired && draftData.answers && Object.keys(draftData.answers).length > 0
        console.log('Local draft check:', { hasDraft: hasDraft.value, isExpired, draftData })
      } catch (error) {
        console.error('Failed to parse draft data:', error)
        hasDraft.value = false
      }
    } else {
      hasDraft.value = false
    }
  }
}

// Get participation records
const fetchParticipationRecords = async () => {
  try {
    console.log('Fetching participation records for survey ID:', surveyId.value)
    // Call real API to get all user response records for specified survey
    const response = await SurveyApi.getMyAllResponses(parseInt(surveyId.value))
    console.log('Participation records API response:', response)

    // API directly returns SurveyResponseVO[] array
    if (response && Array.isArray(response)) {
      participationRecords.value = response
      console.log('Successfully loaded participation records:', response.length, 'records')
    } else {
      console.log('No participation records found or invalid response structure')
      participationRecords.value = []
    }
  } catch (error) {
    console.error('Failed to fetch participation records:', error)
    participationRecords.value = []
  }
}

// View record
const viewRecord = (recordId: number) => {
  router.push(`/survey/view/${recordId}`)
}

// Start survey
const startSurvey = (clearDraft = false) => {
  if (canParticipate.value) {
    if (clearDraft) {
      // Explicitly request to start new survey, clear draft
      router.push(`/survey/fill/${surveyId.value}?new=true`)
    } else {
      // Normal entry, auto-detect draft
      router.push(`/survey/fill/${surveyId.value}`)
    }
  }
}

// Go back
const goBack = () => {
  router.push('/my-center/index?tab=surveys')
}

// Get data when component is mounted
onMounted(async () => {
  await fetchSurveyDetail()
  // Check local draft
  checkLocalDraft()
})
</script>

<style scoped>
.survey-detail-container {
  min-height: calc(100vh - 120px);
}
</style>
