<template>
  <div
    class="group relative overflow-hidden rounded-lg border bg-card hover:shadow-lg transition-all duration-200"
    :class="[isClickable ? 'cursor-pointer' : 'cursor-not-allowed opacity-60']"
    @click="handleClick"
  >
    <!-- Image Area -->
    <div class="h-32 relative overflow-hidden bg-gradient-to-br from-slate-100 to-slate-200">
      <LazyImage
        :src="policy.cover"
        :alt="policy?.title || policy?.name"
        :aspect-ratio="'16/9'"
        class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
      />

      <!-- Status Badge -->
      <div class="absolute top-2 right-2">
        <Badge :variant="getStatusVariant(policy.status)" class="text-xs font-medium px-2 py-1">
          {{ getStatusLabel(policy.status) }}
        </Badge>
      </div>

      <!-- Department Badge -->
<!--      <div v-if="policy.departmentName" class="absolute top-2 left-2">-->
<!--        <Badge variant="outline" class="text-xs font-medium px-2 py-1 bg-white/90">-->
<!--          {{ policy.departmentName }}-->
<!--        </Badge>-->
<!--      </div>-->
    </div>

    <!-- Content Area -->
    <div class="p-4 space-y-3">
      <!-- Title -->
      <h3 class="font-medium text-sm leading-tight line-clamp-2 group-hover:text-primary transition-colors">
        {{ policy.title || 'Untitled Policy' }}
      </h3>

      <!-- Declaration -->
      <p v-if="policy.declaration" class="text-xs text-muted-foreground line-clamp-1">
        {{ policy.declaration }}
      </p>

      <!-- Metadata - Vertical Layout -->
      <div class="space-y-2 text-xs text-muted-foreground">
        <!-- Department -->
        <div v-if="policy.departmentName" class="flex items-center gap-1">
          <User class="w-3 h-3 flex-shrink-0" />
          <span class="truncate">{{ policy.departmentName }}</span>
        </div>

        <!-- Create Time -->
        <div v-if="policy.createTime" class="flex items-center gap-1">
          <Calendar class="w-3 h-3 flex-shrink-0" />
          <span>{{ formatDate(policy.createTime) }}</span>
        </div>

        <!-- Duration if available -->
        <div v-if="policy.duration" class="flex items-center gap-1">
          <Clock class="w-3 h-3 flex-shrink-0" />
          <span>{{ formatDuration(policy.duration) }}</span>
        </div>

      </div>
    </div>

    <!-- Hover Overlay -->
    <div
      class="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Calendar, User, Eye, Clock } from 'lucide-vue-next'
import type { CompanyPolicyListVO } from '@/api/company'

interface Props {
  policy: CompanyPolicyListVO
}

interface Emits {
  (e: 'click', policy: CompanyPolicyListVO): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isClickable = computed(() => {
  return props.policy.attachmentList?.length > 0 || props.policy.fileTypeList?.length > 0 || props.policy.id
})

const getStatusLabel = (status: number) => {
  switch (status) {
    case 0:
      return 'Not Started'
    case 1:
      return 'In Progress'
    case 3:
      return 'Completed'
    default:
      return 'Unknown'
  }
}

const getStatusVariant = (status: number) => {
  switch (status) {
    case 0:
      return 'secondary'
    case 1:
      return 'default'
    case 3:
      return 'outline'
    default:
      return 'secondary'
  }
}

const handleClick = () => {
  if (isClickable.value) {
    emit('click', props.policy)
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return 'N/A'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else {
    return `${minutes}m`
  }
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
