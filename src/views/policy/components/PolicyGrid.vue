<template>
  <div class="flex flex-col h-full">
    <!-- Policy List -->
    <div v-if="policyList.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
      <PolicyCard
        v-for="policy in policyList"
        :key="policy.id"
        :policy="policy"
        @click="handlePolicyClick"
      />
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="min-h-[60vh] flex flex-col items-center justify-center text-center">
      <div class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
        <FileText class="w-12 h-12 text-slate-400" />
      </div>
      <h3 class="text-lg font-semibold text-slate-900 mb-2">No Policies Found</h3>
      <p class="text-slate-500 max-w-md">
        You don't have any {{ currentTabLabel.toLowerCase() }} policies yet
      </p>
    </div>

    <!-- Loading State -->
    <div v-else class="min-h-[60vh] flex items-center justify-center">
      <div class="flex flex-col items-center gap-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p class="text-sm text-muted-foreground">Loading policies...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import PolicyCard from './PolicyCard.vue'
import { FileText } from 'lucide-vue-next'
import type { CompanyPolicyListVO } from '@/api/company'

interface Props {
  policyList: CompanyPolicyListVO[]
  loading?: boolean
  currentTabLabel?: string
}

interface Emits {
  (e: 'policy-click', policy: CompanyPolicyListVO): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  currentTabLabel: 'policies'
})

const emit = defineEmits<Emits>()

// Handle policy click
const handlePolicyClick = (policy: CompanyPolicyListVO) => {
  emit('policy-click', policy)
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
