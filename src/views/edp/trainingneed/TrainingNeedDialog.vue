<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue'
import { ArchiveX } from 'lucide-vue-next'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { TrainingNeedAPI, TrainingTypeEnum } from '@/api/edp/trainingneed'
import { AIChatApi } from '@/api/edp/chat'
import { getPositionListForCurrentUser } from '@/api/system/user'
import type { PostVO } from '@/api/system/post'
import { CirclePlus, Trash, ChartColumnStacked } from 'lucide-vue-next';
import GnerateIcon from '@/assets/icons/generateIcon.svg'
import { computed } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'

const message = useMessage()

/** ----- INTERFACE ----- */
interface TrainingContent {
  id: number
  skillName: string
  trainingType: TrainingTypeEnum | undefined
  contentCatalog: string
  fullContentCatalog?: string // 用于存储完整内容
}

interface TrainingNeed {
  id: number
  title: string
  positionName: string
  content: TrainingContent[]
}

/** ----- SETUP ----- */
const props = defineProps<{
  id?: number // 培训申请ID
  type?: 'add' | 'edit' | 'view'
  dialogTitle?: string
  dialogDescription?: string
}>() // 定义父组件通信数据
const emit = defineEmits(['finish']) // 定义完成事件
const dialogOpen = ref(false) // 对话框开关状态
const positionList = ref<PostVO[]>([]) // 岗位列表
const formErrors = ref({
  title: '',
  skills: [{
    skillName: '',
    trainingType: '',
    contentCatalog: ''
  }] as Array<{
    skillName: string
    trainingType: string
    contentCatalog: string
  }>
}) // 表单验证错误信息
const trainingNeed = ref<TrainingNeed>({
  id: 0,
  title: '',
  positionName: '',
  content: [
    {
      id: 0,
      skillName: '',
      trainingType: undefined,
      contentCatalog: ''
    }
  ]
}) // 培训申请数据
const trainingTypeOptions = ref([
  { label: 'MLC Training', value: TrainingTypeEnum.MLC_TRAINING },
  { label: 'Online Training', value: TrainingTypeEnum.ONLINE_TRAINING },
  { label: 'On-Job Training', value: TrainingTypeEnum.ON_JOB_TRAINING }
]) // 培训类型选项

// 流式输出相关状态
const generatingIndex = ref<number | null>(null) // 当前正在生成的技能卡片索引
const abortController = ref<AbortController | null>(null) // 用于取消请求
const typingQueue = ref<string[]>([]) // 打字队列
const isTyping = ref(false) // 是否正在打字

// 计算属性：为每个技能解析表格数据
const parsedTableData = computed(() => {
  const result = trainingNeed.value.content.map((skill, index) => {
    const data = parseContentCatalog(skill.contentCatalog)
    console.log(`计算属性 - 技能 ${index} 解析数据:`, data)
    return data
  })
  console.log('计算属性 parsedTableData 完整结果:', result)
  return result
})

/** ----- FUNCTIONS ----- */
/** 解析内容目录JSON数据为表格数据 */
const parseContentCatalog = (contentCatalog: string) => {
  console.log('开始解析内容目录:', contentCatalog)

  if (!contentCatalog || contentCatalog.trim() === '') {
    console.log('内容为空，返回空数组')
    return []
  }

  const trimmedContent = contentCatalog.trim()
  console.log('处理后的内容:', trimmedContent)

  // 放宽检查条件，允许更多格式的内容
  if (trimmedContent.length < 5) {
    console.log('内容太短，返回空数组')
    return []
  }

  try {
    let parsedData: any

    // 尝试多种解析方式
    if (trimmedContent.startsWith('[') || trimmedContent.startsWith('{')) {
      // 直接解析JSON
      console.log('尝试直接解析JSON')
      parsedData = JSON.parse(trimmedContent)
    } else {
      // 尝试从文本中提取JSON数组
      console.log('尝试从文本中提取JSON数组')
      const arrayMatch = trimmedContent.match(/\[[\s\S]*\]/)
      if (arrayMatch) {
        console.log('找到数组匹配:', arrayMatch[0])
        parsedData = JSON.parse(arrayMatch[0])
      } else {
        // 尝试查找对象数组的模式
        const objectArrayMatch = trimmedContent.match(/\{[\s\S]*\}/)
        if (objectArrayMatch) {
          console.log('找到对象匹配，尝试包装为数组')
          // 如果找到对象，尝试将其包装为数组
          const objectStr = objectArrayMatch[0]
          // 检查是否是多个对象用逗号分隔
          if (objectStr.includes('},{')) {
            parsedData = JSON.parse(`[${objectStr}]`)
          } else {
            parsedData = [JSON.parse(objectStr)]
          }
        } else {
          console.log('未找到有效的JSON结构')
          return []
        }
      }
    }

    console.log('初步解析结果:', parsedData)

    // 如果解析结果是对象且包含 course_recommendations 字段
    if (parsedData && typeof parsedData === 'object' && parsedData.course_recommendations) {
      console.log('提取 course_recommendations 字段')
      parsedData = parsedData.course_recommendations
    }

    // 确保结果是数组
    if (!Array.isArray(parsedData)) {
      console.log('结果不是数组，尝试转换')
      if (typeof parsedData === 'object' && parsedData !== null) {
        // 如果是单个对象，包装为数组
        parsedData = [parsedData]
      } else {
        console.log('无法转换为数组，返回空数组')
        return []
      }
    }

    console.log('最终解析的数组:', parsedData)

    // 确保每个对象都有必要的字段，支持多种字段名称
    const result = parsedData.map((item: any, index: number) => {
      console.log(`处理数组项 ${index}:`, item)
      return {
        courseName: item['Course Name'] || item.courseName || item.name || item.title || 'N/A',
        duration: item['Duration'] || item.duration || item.time || 'N/A',
        level: item['Skill Level'] || item.level || item.difficulty || 'N/A',
        description: item['Detailed Content/Covered Topics'] || item['Key Skills Gained'] || item.description || item.desc || item.summary || 'N/A',
        targetAudience: item['Target Audience'] || item.targetAudience || item.audience || item.target || 'N/A',
        keySkills: item['Key Skills'] || item.keySkills || item.skills || item.keySkillsGained || 'N/A',
        topics: item['Topics'] || item.topics || item.coveredTopics || item.content || 'N/A'
      }
    })

    console.log('最终表格数据:', result)
    return result
  } catch (error: any) {
    console.error('JSON解析失败:', error.message, '原始内容:', contentCatalog)
    return []
  }
}
/** 获取岗位列表 */
const getPositionList = async () => {
  try {
    const res = await getPositionListForCurrentUser()

    // 过滤相同name的项目，保留第一个出现的项目
    const uniquePositions = res.filter((position: PostVO, index: number, array: PostVO[]) =>
      array.findIndex((item: PostVO) => item.name === position.name) === index
    )

    positionList.value = uniquePositions
    console.log('获取岗位列表成功:', positionList.value)
    console.log('去重前数量:', res.length, '去重后数量:', uniquePositions.length)
  } catch (error) {
    console.log('获取岗位列表失败:', error)
  }
}

/** 验证表单 */
const validateForm = () => {
  // 重置错误信息
  formErrors.value.title = ''
  formErrors.value.skills = trainingNeed.value.content.map(() => ({
    skillName: '',
    trainingType: '',
    contentCatalog: ''
  }))

  let isValid = true

  // 验证标题
  if (!trainingNeed.value.title.trim()) {
    formErrors.value.title = 'Title is required'
    isValid = false
  }

  // 验证技能（只验证前5个，排除第6个警告卡片）
  const validSkills = trainingNeed.value.content.slice(0, 5)
  validSkills.forEach((skill, index) => {
    if (!skill.skillName.trim()) {
      formErrors.value.skills[index].skillName = 'Skill name is required'
      isValid = false
    }

    if (!skill.trainingType) {
      formErrors.value.skills[index].trainingType = 'Training type is required'
      isValid = false
    }

    if (!skill.contentCatalog.trim()) {
      formErrors.value.skills[index].contentCatalog = 'Content catalog is required'
      isValid = false
    }
  })

  return isValid
}

/** 获得培训申请详情 */
const getTrainingNeed = async (id: number) => {
  try {
    // 调用API
    trainingNeed.value = await TrainingNeedAPI.getTrainingNeedDetail(id) // 保存数据到响应式变量
    console.log('获取培训申请成功😊:', trainingNeed.value)
  } catch (error) {
    console.log('获取培训申请失败😫:', error)
  }
}

/** 添加技能 */
const addSkill = () => {
  // 最多允许添加6个技能卡片（第6个用于显示警告）
  if (trainingNeed.value.content.length >= 6) {
    return
  }

  trainingNeed.value.content.push({
    id: 0,
    skillName: '',
    trainingType: undefined,
    contentCatalog: ''
  })

  // 同步添加错误信息对象
  formErrors.value.skills.push({
    skillName: '',
    trainingType: '',
    contentCatalog: ''
  })
}

/** 打字效果处理队列 */
const processTypingQueue = async (skill: TrainingContent) => {
  if (typingQueue.value.length === 0) {
    // 确保显示完整内容
    if (skill.fullContentCatalog && skill.contentCatalog !== skill.fullContentCatalog) {
      skill.contentCatalog = skill.fullContentCatalog
    }
    isTyping.value = false
    return
  }

  isTyping.value = true
  const text = typingQueue.value.shift() || ''

  // 多重过滤确保没有 <think> 标签内容，并过滤空行
  let content = text
    .replace(/<think>[\s\S]*?<\/think>/gi, '') // 移除完整的think标签
    .replace(/<think>[\s\S]*$/gi, '') // 移除未闭合的think开始标签
    .replace(/^[\s\S]*?<\/think>/gi, '') // 移除未开始的think结束标签
    .split('\n') // 按行分割
    .filter((line: string) => line.trim() !== '') // 过滤空行
    .join('\n') // 重新组合
    .trim()

  console.log('打字机处理内容:', content)

  // 如果过滤后内容为空，继续处理下一个
  if (content === '') {
    await processTypingQueue(skill)
    return
  }

  // 逐字符打印文本
  for (let i = 0; i < content.length; i++) {
    // 再次检查当前字符是否是think标签的开始
    const remainingText = content.slice(i)
    if (remainingText.startsWith('<think>')) {
      // 跳过整个think标签
      const thinkEndIndex = remainingText.indexOf('</think>')
      if (thinkEndIndex !== -1) {
        i += thinkEndIndex + '</think>'.length - 1
        continue
      } else {
        // 如果没有结束标签，跳过剩余所有内容
        break
      }
    }

    skill.contentCatalog += content.charAt(i)
    await new Promise((resolve) => setTimeout(resolve, 20)) // 控制打字速度
  }

  // 处理队列中的下一条消息
  await processTypingQueue(skill)
}

/** 停止流式输出 */
const stopStream = () => {
  // 确保当前技能显示完整内容
  if (generatingIndex.value !== null) {
    const skill = trainingNeed.value.content[generatingIndex.value]
    if (skill && skill.fullContentCatalog && skill.contentCatalog !== skill.fullContentCatalog) {
      skill.contentCatalog = skill.fullContentCatalog
    }
  }

  if (abortController.value) {
    abortController.value.abort()
    abortController.value = null
  }
  generatingIndex.value = null
  isTyping.value = false
  typingQueue.value = []
}

/** 生成内容目录 */
const generateContentCatalog = async (index: number) => {
  if (generatingIndex.value !== null) {
    message.warning('正在生成中，请稍候...')
    return
  }

  const skill = trainingNeed.value.content[index]
  if (!skill.skillName) {
    message.warning('请先填写技能名称')
    return
  }

  generatingIndex.value = index
  skill.contentCatalog = '' // 清空现有内容
  skill.fullContentCatalog = '' // 清空完整内容
  typingQueue.value = [] // 清空打字队列
  isTyping.value = false // 重置打字状态

  // 创建新的AbortController
  abortController.value = new AbortController()

  let lastContent = '' // 上一次收到的完整内容

  const onMessage = (event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data)
      let rawContent = data.data?.answer || ''

      console.log('原始内容:', rawContent)

      // 跳过系统日志信息和空白行
      if (rawContent.includes('is running...') || rawContent.trim() === '') {
        return
      }

      // 检查是否包含完整的JSON结构（判断流式输出是否完成）
      const hasCompleteJson = rawContent.includes('"courses"') &&
                             rawContent.includes('[') &&
                             rawContent.includes(']') &&
                             !rawContent.includes('```json') // 确保不是刚开始的标记

      console.log('是否包含完整JSON:', hasCompleteJson)

      // 如果检测到完整内容，停止打字机并一次性显示最终内容
      if (hasCompleteJson) {
        console.log('检测到完整内容，停止打字机并一次性显示最终内容')

        // 停止打字机效果
        isTyping.value = false
        typingQueue.value = []

        // 提取并格式化最终内容
        const extractFinalContent = (content: string) => {
          try {
            let cleanContent = content
              .replace(/<think>[\s\S]*?<\/think>/gi, '') // 完整的think标签
              .replace(/<think>[\s\S]*$/gi, '') // 未闭合的think开始标签
              .replace(/^[\s\S]*?<\/think>/gi, '') // 未开始的think结束标签
              .replace(/```json/gi, '') // 移除 ```json 标记
              .replace(/```/gi, '') // 移除 ``` 标记
              .split('\n') // 按行分割
              .filter((line: string) => line.trim() !== '') // 过滤空行
              .join('\n') // 重新组合

            console.log('移除标签后:', cleanContent)

            // 查找 "courses": [ 开始的位置
            const coursesMatch = cleanContent.match(/"courses":\s*\[/)
            if (coursesMatch && coursesMatch.index !== undefined) {
              const startIndex = coursesMatch.index + coursesMatch[0].length - 1 // 包含 [

              // 从 [ 开始查找匹配的 ]
              let bracketCount = 0
              let endIndex = -1

              for (let i = startIndex; i < cleanContent.length; i++) {
                if (cleanContent[i] === '[') {
                  bracketCount++
                } else if (cleanContent[i] === ']') {
                  bracketCount--
                  if (bracketCount === 0) {
                    endIndex = i
                    break
                  }
                }
              }

              if (endIndex !== -1) {
                const jsonArrayStr = cleanContent.substring(startIndex, endIndex + 1)
                console.log('提取的courses数组:', jsonArrayStr)

                try {
                  const parsedArray = JSON.parse(jsonArrayStr)
                  if (Array.isArray(parsedArray) && parsedArray.length > 0) {
                    // 将数组中的每个对象格式化为多行，但不包含外层中括号
                    const formattedObjects = parsedArray.map(obj =>
                      JSON.stringify(obj, null, 2)
                    )
                    return formattedObjects.join(',\n')
                  }
                } catch (parseError) {
                  console.log('JSON解析失败，手动处理:', parseError)
                  const trimmed = jsonArrayStr.trim()
                  if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
                    return trimmed.slice(1, -1).trim()
                  }
                  return trimmed
                }
              }
            }

            return cleanContent.trim()
          } catch (error) {
            console.error('提取最终内容失败:', error)
            return content.replace(/<think>[\s\S]*?<\/think>/gi, '').replace(/```json/gi, '').replace(/```/gi, '').split('\n').filter((line: string) => line.trim() !== '').join('\n').trim()
          }
        }

        const finalContent = extractFinalContent(rawContent)
        if (finalContent && finalContent.trim() !== '') {
          skill.contentCatalog = finalContent
          skill.fullContentCatalog = finalContent
          console.log('最终内容已一次性设置到textarea')
        }

        lastContent = rawContent
        return
      }

      // 如果还没有完整内容，继续打字机效果
      // 先过滤掉不需要显示的内容和空行
      let filteredContent = rawContent
        .replace(/<think>[\s\S]*?<\/think>/gi, '') // 完整的think标签
        .replace(/<think>[\s\S]*$/gi, '') // 未闭合的think开始标签
        .replace(/^[\s\S]*?<\/think>/gi, '') // 未开始的think结束标签
        .replace(/```json/gi, '') // 移除 ```json 标记
        .replace(/```/gi, '') // 移除 ``` 标记
        .split('\n') // 按行分割
        .filter((line: string) => line.trim() !== '') // 过滤空行
        .join('\n') // 重新组合

      console.log('过滤后用于打字机的内容:', filteredContent)

      // 如果过滤后内容为空，直接返回
      if (!filteredContent || filteredContent.trim() === '') {
        return
      }

      // 如果是第一次接收内容
      if (lastContent === '') {
        const trimmedContent = filteredContent.trimStart()
        lastContent = rawContent // 存储原始内容用于完整性检测

        // 初始化 fullContentCatalog
        if (!skill.fullContentCatalog) {
          skill.fullContentCatalog = ''
        }
        skill.fullContentCatalog = trimmedContent

        if (trimmedContent.length > 0) {
          typingQueue.value.push(trimmedContent)
        }

        if (!isTyping.value) {
          processTypingQueue(skill)
        }
        return
      }

      // 获取新增部分
      if (rawContent.startsWith(lastContent)) {
        const rawNewPart = rawContent.slice(lastContent.length)
        // 对新增部分也进行过滤和空行过滤
        const filteredNewPart = rawNewPart
          .replace(/<think>[\s\S]*?<\/think>/gi, '')
          .replace(/<think>[\s\S]*$/gi, '')
          .replace(/^[\s\S]*?<\/think>/gi, '')
          .replace(/```json/gi, '')
          .replace(/```/gi, '')
          .split('\n') // 按行分割
          .filter((line: string) => line.trim() !== '') // 过滤空行
          .join('\n') // 重新组合

        if (filteredNewPart.length === 0) {
          lastContent = rawContent // 更新原始内容记录
          return
        }

        lastContent = rawContent
        skill.fullContentCatalog = filteredContent // 更新完整内容

        if (filteredNewPart.length > 0) {
          typingQueue.value.push(filteredNewPart)
        }

        if (!isTyping.value) {
          processTypingQueue(skill)
        }
      } else {
        // 内容不连续，重置
        lastContent = rawContent
        skill.contentCatalog = ''
        skill.fullContentCatalog = filteredContent
        typingQueue.value = []

        if (filteredContent.length > 0) {
          typingQueue.value.push(filteredContent)
        }

        if (!isTyping.value) {
          processTypingQueue(skill)
        }
      }
    } catch (error) {
      console.error('解析消息失败:', error)
    }
  }

  const onError = (error: Event) => {
    console.error('Stream error:', error)
    message.error('生成失败，请重试')
    stopStream()
  }

  const onClose = () => {
    console.log('Stream closed')

    // 流式输出结束时，如果还没有处理完整内容，进行最后一次处理
    if (lastContent && (!skill.contentCatalog || skill.contentCatalog.includes('```'))) {
      console.log('流式结束，进行最后处理:', lastContent)

      // 提取最终内容
      const extractFinalContent = (content: string) => {
        try {
          let cleanContent = content
            .replace(/<think>[\s\S]*?<\/think>/gi, '')
            .replace(/<think>[\s\S]*$/gi, '')
            .replace(/^[\s\S]*?<\/think>/gi, '')
            .replace(/```json/gi, '')
            .replace(/```/gi, '')
            .split('\n') // 按行分割
            .filter((line: string) => line.trim() !== '') // 过滤空行
            .join('\n') // 重新组合

          // 查找并格式化数组内容
          const coursesMatch = cleanContent.match(/"courses":\s*\[/)
          if (coursesMatch && coursesMatch.index !== undefined) {
            const startIndex = coursesMatch.index + coursesMatch[0].length - 1
            let bracketCount = 0
            let endIndex = -1

            for (let i = startIndex; i < cleanContent.length; i++) {
              if (cleanContent[i] === '[') bracketCount++
              else if (cleanContent[i] === ']') {
                bracketCount--
                if (bracketCount === 0) {
                  endIndex = i
                  break
                }
              }
            }

            if (endIndex !== -1) {
              const jsonArrayStr = cleanContent.substring(startIndex, endIndex + 1)
              try {
                const parsedArray = JSON.parse(jsonArrayStr)
                if (Array.isArray(parsedArray) && parsedArray.length > 0) {
                  const formattedObjects = parsedArray.map(obj => JSON.stringify(obj, null, 2))
                  return formattedObjects.join(',\n')
                }
              } catch (parseError) {
                const trimmed = jsonArrayStr.trim()
                if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
                  return trimmed.slice(1, -1).trim()
                }
                return trimmed
              }
            }
          }

          return cleanContent.trim()
        } catch (error) {
          return content.replace(/<think>[\s\S]*?<\/think>/gi, '').replace(/```json/gi, '').replace(/```/gi, '').split('\n').filter((line: string) => line.trim() !== '').join('\n').trim()
        }
      }

      const finalContent = extractFinalContent(lastContent)
      if (finalContent && finalContent.trim() !== '') {
        skill.contentCatalog = finalContent
        skill.fullContentCatalog = finalContent
        console.log('流式结束时设置最终内容')
      }
    }

    stopStream()
  }

  // 构建请求数据
  const requestData = {
    positionName: trainingNeed.value.positionName || 'IT',
    skillName: skill.skillName,
  }

  try {
    await AIChatApi.getContentCatalog(
      requestData,
      abortController.value,
      onMessage,
      onError,
      onClose
    )
  } catch (error) {
    console.error('生成内容目录失败:', error)
    message.error('生成失败，请重试')
    stopStream()
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  stopStream()
})

/** 删除技能 */
const removeSkill = (index: number) => {
  if (trainingNeed.value.content.length > 1) {
    trainingNeed.value.content.splice(index, 1)
    // 同步删除错误信息
    formErrors.value.skills.splice(index, 1)
  }
}

/** 重置表单 */
const resetForm = () => {
  trainingNeed.value = {
    id: 0,
    title: '',
    positionName: '',
    content: [
      {
        id: 0,
        skillName: '',
        trainingType: undefined,
        contentCatalog: ''
      }
    ]
  }

  // 重置错误信息
  formErrors.value.title = ''
  formErrors.value.skills = [{
    skillName: '',
    trainingType: '',
    contentCatalog: ''
  }]
}

/** 关闭对话框 */
const handleClose = () => {
  dialogOpen.value = false
  resetForm()
}

/** 保存&添加技能表单 */
const handleSubmit = async (contentStatus: number) => {
  // 验证表单
  if (!validateForm()) {
    message.warning('Please fill in all required fields')
    return
  }

  // 如果有第6个技能卡片，只提交前5个
  const validContent = trainingNeed.value.content.slice(0, 5)

  try {
    // 创建请求体
    const requestData = {
      title: trainingNeed.value.title,
      content: validContent.map((skill) => ({
        skillName: skill.skillName,
        trainingType: Number(skill.trainingType),
        contentCatalog: skill.contentCatalog,
        status: 1
      })),
      status: contentStatus // 根据不同按钮传递的contentStatus来区分保存和提交
    }

    // 根据不同的状态来判断是更新还是创建
    if (props.type === 'edit' || trainingNeed.value.id) {
      // 更新培训申请
      const updateData = {
        ...requestData,
        id: trainingNeed.value.id,
        content: validContent.map((skill) => ({
          id: skill.id,
          skillName: skill.skillName,
          trainingType: Number(skill.trainingType),
          contentCatalog: skill.contentCatalog,
          status: 1
        }))
      }
      const res = await TrainingNeedAPI.updateTrainingNeed(updateData)
      console.log('更新成功😊:', res)
    } else {
      // 添加培训申请
      const res = await TrainingNeedAPI.createTrainingNeed(requestData)
      console.log('提交成功😊:', res)
    }

    // 完成后关闭对话框并通知父组件刷新列表
    dialogOpen.value = false
    emit('finish')

    // 重置表单，以便下次打开
    resetForm()
  } catch (error) {
    console.log('提交失败😫:', error)
  }
}

/** ----- LIFECYCLE HOOK ----- */
watch(dialogOpen, async (newVal) => {
  if (newVal) {
    // 对话框打开时，先获取岗位列表
    await getPositionList()

    if (props.id) {
      // 如果有ID，获取培训申请详情
      await getTrainingNeed(props.id)
    } else {
      // 如果没有ID，重置表单
      resetForm()
    }
  }
})

// 监听 title 变化，清除错误提示
watch(() => trainingNeed.value.title, (newVal) => {
  if (newVal && newVal.trim()) {
    formErrors.value.title = ''
  }
})

// 监听技能字段变化，清除相应的错误提示
watch(() => trainingNeed.value.content, (newContent) => {
  newContent.forEach((skill, index) => {
    if (formErrors.value.skills[index]) {
      // 监听技能名称
      if (skill.skillName && skill.skillName.trim()) {
        formErrors.value.skills[index].skillName = ''
      }

      // 监听培训类型
      if (skill.trainingType) {
        formErrors.value.skills[index].trainingType = ''
      }

      // 监听内容目录
      if (skill.contentCatalog && skill.contentCatalog.trim()) {
        formErrors.value.skills[index].contentCatalog = ''
      }
    }
  })
}, { deep: true })
</script>

<template>
  <Dialog v-model:open="dialogOpen">
    <DialogTrigger as-child>
      <slot />
    </DialogTrigger>
    <DialogContent class="w-auto sm:max-w-[80vw]">
      <DialogHeader>
        <DialogTitle> {{ props.dialogTitle }} </DialogTitle>
        <DialogDescription>
          {{ props.dialogDescription }}
        </DialogDescription>
      </DialogHeader>

      <!--预览状态-信息展示-->
      <div v-if="props.type === 'view'" class="flex flex-col gap-5 py-4">
        <!--标题表单-->
        <div class="flex flex-col items-center justify-start w-full gap-4">
          <Label for="name" class="w-full text-left"> Title </Label>
          <div class="w-full">
            <Input
              v-model="trainingNeed.title"
              placeholder="Title"
              :disabled="props.type === 'view'"
              :class="['h-10' ,formErrors.title ? 'border-red-500' : '']"
            />
            <p v-if="formErrors.title" class="text-sm text-red-500 mt-1">
              {{ formErrors.title }}
            </p>
          </div>
        </div>

        <!--标题表单-->
        <div
          class="flex flex-col items-center justify-start w-full gap-4"
        >
          <Alert
            v-show="trainingNeed.content.length >= 6"
            variant="destructive"
            class="mb-2"
          >
            <ArchiveX class="h-4 w-4" />
            <AlertTitle class="">Oops!</AlertTitle>
            <AlertDescription class="">
              You can only add up to 5 skill cards per submission.
            </AlertDescription>
          </Alert>

          <!--技能列表标题-->
          <div class="flex w-full items-center justify-between">
            <Label for="name" class="w-full text-left"> Skills </Label>
            <Button
              v-if="props.type !== 'view'"
              variant="ghost"
              size="icon"
              :disabled="trainingNeed.content.length >= 6"
              @click="addSkill"
            >
              <CirclePlus />
            </Button>
          </div>

          <!-- 技能卡片列表 -->
          <ScrollArea class="w-full max-h-[400px]">
            <div
              v-for="(skill, index) in trainingNeed.content"
              :key="index"
              :class="[
                'flex flex-col gap-3 border rounded-lg w-full h-full p-3 mb-3',
                index === 5 ? 'border-red-500' : ''
              ]"
            >
              <!--技能名称&培训类型-->
              <div class="flex items-start justify-between gap-3">
                <div class="flex-1">
                  <Input
                    v-model="skill.skillName"
                    placeholder="Please input the skill you need"
                    :disabled="props.type === 'view'"
                    :class="[
                      'h-10',
                      formErrors.skills[index]?.skillName ? 'border-red-500' : '']"
                  />
                  <p v-if="formErrors.skills[index]?.skillName" class="text-sm text-red-500 mt-1">
                    {{ formErrors.skills[index].skillName }}
                  </p>
                </div>
                <div class="flex-1">
                  <Select
                    v-if="skill.trainingType || props.type !== 'view'"
                    v-model="skill.trainingType"
                    :disabled="props.type === 'view'"
                  >
                    <SelectTrigger
                      :class="[
                        'pr-2',
                        formErrors.skills[index]?.trainingType ? 'border-red-500' : ''
                      ]"
                    >
                      <SelectValue placeholder="Select Training Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel> Training Type </SelectLabel>
                        <SelectItem
                          v-for="trainingTypeOption in trainingTypeOptions"
                          :key="trainingTypeOption.value"
                          :value="trainingTypeOption.value"
                        >
                          {{ trainingTypeOption.label }}
                        </SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <p v-if="formErrors.skills[index]?.trainingType" class="text-sm text-red-500 mt-1">
                    {{ formErrors.skills[index].trainingType }}
                  </p>
                </div>
              </div>

              <!--课程目录-->
              <div class="flex flex-col gap-2 relative">
                <Button
                  variant="ghost"
                  size="icon"
                  class="absolute top-1 right-1 z-10"
                  :disabled="generatingIndex === index || props.type === 'view'"
                  @click="generateContentCatalog(index)"
                >
                  <img :src="GnerateIcon" alt="Generate Icon" />
                </Button>

                <div
                  v-loading="generatingIndex === index"
                  element-loading-text="Generating Content Catalog..."
                  :class="[
                    'border rounded-md p-4 min-h-[120px]',
                    formErrors.skills[index]?.contentCatalog ? 'border-red-500' : ''
                  ]"
                >
                  <!-- 有数据时显示表格 -->
                  <div v-if="generatingIndex !== index && parsedTableData[index] && parsedTableData[index].length > 0" class="overflow-x-auto">
                    <table class="w-full min-w-[80vw] border-collapse">
                      <thead>
                        <tr class="border-b">
                          <th class="text-left p-2 font-medium whitespace-nowrap" style="min-width: 120px;">Course Name</th>
                          <th class="text-left p-2 font-medium whitespace-nowrap" style="min-width: 80px;">Skill Level</th>
                          <th class="text-left p-2 font-medium whitespace-nowrap" style="min-width: 100px;">Duration</th>
                          <th class="text-left p-2 font-medium whitespace-nowrap" style="min-width: 100px;">Target Audience</th>
                          <th class="text-left p-2 font-medium whitespace-nowrap" style="min-width: 120px;">Key Skills</th>
                          <th class="text-left p-2 font-medium whitespace-nowrap" style="min-width: 100px;">Topics</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="(course, courseIndex) in parsedTableData[index]"
                          :key="courseIndex"
                          class="border-b hover:bg-gray-50"
                        >
                          <td class="p-2 whitespace-nowrap overflow-hidden text-ellipsis" style="max-width: 120px;" :title="course.courseName">{{ course.courseName }}</td>
                          <td class="p-2 whitespace-nowrap">{{ course.level }}</td>
                          <td class="p-2 whitespace-nowrap">{{ course.duration }}</td>
                          <td class="p-2" style="max-width: 120px;">
                            <div class="break-words line-clamp-2" :title="course.targetAudience">{{ course.targetAudience }}</div>
                          </td>
                          <td class="p-2" style="max-width: 150px;">
                            <div class="break-words line-clamp-2" :title="course.keySkills">{{ course.keySkills }}</div>
                          </td>
                          <td class="p-2" style="max-width: 120px;">
                            <div class="break-words line-clamp-2" :title="course.topics">{{ course.topics }}</div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- 空状态 -->
                  <div v-else class="flex flex-col items-center justify-center h-full gap-2">
                    <p class="text-muted-foreground text-center">
                      No course data available
                    </p>
                  </div>
                </div>
              </div>

              <!--按钮组-->
              <div class="flex items-center justify-end w-full">
                <Button
                  v-if="trainingNeed.content.length > 1 && props.type !== 'view'"
                  variant="ghost"
                  size="icon"
                  class="hover:text-red-500 transition-colors"
                  @click="removeSkill(index)"
                >
                  <Trash />
                </Button>
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>

      <!--对话框底部操作-->
      <DialogFooter class="flex !justify-between">
        <!--关闭-->
        <Button variant="ghost" @click="handleClose"> Close </Button>

        <!--保存&提交-->
        <div class="flex gap-2">
          <!--提交-->
          <Button v-if="props.type !== 'view'" variant="outline" @click="handleSubmit(1)">
            Save
          </Button>

          <!--提交-->
          <Button v-if="props.type !== 'view'" type="submit" @click="handleSubmit(2)" :disabled="trainingNeed.content.length >= 6">
            Submit
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
