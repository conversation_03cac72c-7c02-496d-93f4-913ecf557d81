import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { SurveyApi } from '@/api/survey'
import type {
  SurveyInstanceVO,
  SurveyResponseVO,
  SurveyAnswerReqVO,
  AvailableInstancesReqVO,
  SurveyStatisticsVO,
  PageReqVO
} from '@/api/survey/types'

export const useSurveyStore = defineStore('survey', () => {
  // ==================== 状态定义 ====================

  // 问卷列表相关
  const surveyList = ref<SurveyInstanceVO[]>([])
  const loading = ref(false)
  const pagination = ref({
    pageNo: 1,
    pageSize: 12,
    total: 0
  })

  // 当前问卷相关
  const currentSurvey = ref<SurveyInstanceVO | null>(null)
  const currentQuestionIndex = ref(0)
  const answers = ref<Record<number, SurveyAnswerReqVO>>({})

  // 用户记录相关
  const userResponses = ref<SurveyResponseVO[]>([])
  const responsesPagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
  })

  // 统计数据相关
  const statistics = ref<SurveyStatisticsVO | null>(null)
  const userRanking = ref<any>(null)

  // UI状态
  const submitLoading = ref(false)
  const statisticsLoading = ref(false)

  // ==================== 计算属性 ====================

  // 计算进度百分比
  const progressPercentage = computed(() => {
    if (!currentSurvey.value?.questions?.length) return 0
    return Math.round(
      ((currentQuestionIndex.value + 1) / currentSurvey.value.questions.length) * 100
    )
  })

  // 获取当前问题
  const currentQuestion = computed(() => {
    if (!currentSurvey.value?.questions?.length) return null
    return currentSurvey.value.questions[currentQuestionIndex.value]
  })

  // 检查是否可以提交
  const canSubmit = computed(() => {
    if (!currentSurvey.value) return false

    // 检查所有必答题是否已回答
    return (
      currentSurvey.value.questions?.every((question) => {
        if (!question.required) return true
        const answer = answers.value[question.id]
        return answer && (answer.answerValue || answer.answerText)
      }) || false
    )
  })

  // 已回答问题数量
  const answeredCount = computed(() => {
    if (!currentSurvey.value?.questions) return 0
    return currentSurvey.value.questions.filter((question) => {
      const answer = answers.value[question.id]
      return answer && (answer.answerValue || answer.answerText)
    }).length
  })

  // 问卷统计信息
  const surveyStats = computed(() => {
    const total = surveyList.value.length
    const available = surveyList.value.filter(
      (s) => s.canParticipate && s.userSubmissionCount === 0
    ).length
    const submitted = surveyList.value.filter((s) => s.userSubmissionCount > 0).length
    const expired = surveyList.value.filter((s) => new Date(s.endTime) < new Date()).length

    return {
      total,
      available,
      submitted,
      expired,
      completionRate: total > 0 ? Math.round((submitted / total) * 100) : 0
    }
  })

  // 获取MyCenter统计数据
  const getMyCenterStats = computed(() => {
    return {
      totalSurveys: surveyStats.value.total,
      completedSurveys: surveyStats.value.submitted,
      availableSurveys: surveyStats.value.available,
      completionRate: surveyStats.value.completionRate
    }
  })

  // ==================== Actions ====================

  // 获取问卷列表
  const fetchSurveyList = async (params: AvailableInstancesReqVO = {}) => {
    loading.value = true
    try {
      const requestParams = {
        pageNo: pagination.value.pageNo,
        pageSize: pagination.value.pageSize,
        ...params
      }

      // 调用真实API
      const response = await SurveyApi.getAvailableInstances(requestParams)
      console.log('API Response:', response)

      // 处理不同的响应格式
      if (response) {
        if (response.list) {
          // 如果response直接包含list属性
          surveyList.value = response.list || []
          pagination.value.total = response.total || 0
        } else if (response.data && response.data.list) {
          // 如果response.data包含list属性
          surveyList.value = response.data.list || []
          pagination.value.total = response.data.total || 0
        } else if (Array.isArray(response.data)) {
          // 如果data直接是数组
          surveyList.value = response.data
          pagination.value.total = response.data.length
        } else if (Array.isArray(response)) {
          // 如果response直接是数组
          surveyList.value = response
          pagination.value.total = response.length
        } else {
          // 其他格式，设置为空
          surveyList.value = []
          pagination.value.total = 0
        }
      } else {
        // 响应格式不正确，设置为空
        surveyList.value = []
        pagination.value.total = 0
      }

      if (params.pageNo) {
        pagination.value.pageNo = params.pageNo
      }
    } catch (error) {
      console.error('Failed to fetch survey list:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取问卷详情
  const fetchSurveyDetail = async (instanceId: number, clearPreviousDraft: boolean = false) => {
    console.log(
      'fetchSurveyDetail called with instanceId:',
      instanceId,
      'clearPreviousDraft:',
      clearPreviousDraft
    )
    loading.value = true
    currentSurvey.value = null // 重置状态

    try {
      console.log('Calling SurveyApi.getInstance...')
      const response = await SurveyApi.getInstance(instanceId)
      console.log('Survey detail response in store:', response)
      console.log('Response type:', typeof response)
      console.log('Response keys:', response ? Object.keys(response) : 'null')

      if (response) {
        // API直接返回数据，不需要访问.data属性
        currentSurvey.value = response
        console.log('currentSurvey.value set to:', currentSurvey.value)
        console.log('currentSurvey.value.questions:', currentSurvey.value?.questions)
        console.log('questions length:', currentSurvey.value?.questions?.length)
        console.log('questions array:', JSON.stringify(currentSurvey.value?.questions, null, 2))

        // 如果需要清除之前的草稿，先清除再初始化
        // 检查本地是否有草稿数据
        const draftKey = `survey_draft_${instanceId}`
        const draftStr = localStorage.getItem(draftKey)
        let hasDraft = false

        if (draftStr) {
          try {
            const draftData = JSON.parse(draftStr)
            // 检查草稿是否过期（24小时）
            const isExpired = Date.now() - draftData.timestamp > 24 * 60 * 60 * 1000
            hasDraft = !isExpired && draftData.answers && Object.keys(draftData.answers).length > 0
          } catch (error) {
            console.error('Failed to parse draft data:', error)
            hasDraft = false
          }
        }

        if (clearPreviousDraft) {
          // 明确要求清除草稿（点击"开始答题"时）
          clearDraft()
          initAnswers(false) // 不加载草稿数据
        } else if (hasDraft) {
          // 有草稿数据，自动加载
          initAnswers(true) // 加载本地草稿数据
        } else {
          // 没有草稿数据，初始化空答案
          initAnswers(false) // 不加载草稿数据
        }
      } else {
        console.error('Response is null or undefined')
        currentSurvey.value = null
      }

      return response
    } catch (error) {
      console.error('Failed to fetch survey detail:', error)
      currentSurvey.value = null
      throw error
    } finally {
      loading.value = false
      console.log(
        'fetchSurveyDetail completed. loading:',
        loading.value,
        'currentSurvey:',
        !!currentSurvey.value
      )
    }
  }

  // 初始化答案对象
  const initAnswers = (loadDraftData: boolean = true) => {
    if (!currentSurvey.value?.questions) return

    answers.value = {}
    currentQuestionIndex.value = 0

    currentSurvey.value.questions.forEach((question) => {
      answers.value[question.id] = {
        questionId: question.id,
        answerText: '',
        answerValue: ''
      }
    })

    // 根据参数决定是否从localStorage恢复草稿
    if (loadDraftData) {
      loadDraft()
    }
  }

  // 更新答案
  const updateAnswer = (questionId: number, answer: Partial<SurveyAnswerReqVO>) => {
    if (answers.value[questionId]) {
      Object.assign(answers.value[questionId], answer)
      saveDraft() // 自动保存草稿
    }
  }

  // 保存草稿到localStorage
  const saveDraft = () => {
    if (!currentSurvey.value) return

    const draftKey = `survey_draft_${currentSurvey.value.id}`
    const draftData = {
      answers: answers.value,
      currentQuestionIndex: currentQuestionIndex.value,
      timestamp: Date.now()
    }

    try {
      localStorage.setItem(draftKey, JSON.stringify(draftData))
    } catch (error) {
      console.warn('Failed to save draft:', error)
    }
  }

  // 从服务器加载草稿数据
  const initAnswersFromServerDraft = async (instanceId: number) => {
    console.log('initAnswersFromServerDraft called for instanceId:', instanceId)
    if (!currentSurvey.value?.questions) {
      console.log('No questions found, skipping answer initialization')
      return
    }

    try {
      // 获取用户的所有回答记录
      const responses = await SurveyApi.getMyAllResponses(instanceId)
      console.log('User responses:', responses)

      // 查找草稿记录（状态为2）
      const draftResponse = responses.find((response: any) => response.status === 2)

      if (draftResponse) {
        console.log('Found draft response:', draftResponse)

        // 获取草稿的详细信息
        const draftDetail = await SurveyApi.getResponseDetail(draftResponse.id)
        console.log('Draft detail:', draftDetail)

        // 初始化答案对象
        answers.value = {}
        currentQuestionIndex.value = 0

        // 为每个问题初始化默认值
        currentSurvey.value.questions.forEach((question) => {
          answers.value[question.id] = {
            questionId: question.id,
            answerText: '',
            answerValue: ''
          }
        })

        // 填充草稿数据
        if (draftDetail.answers) {
          draftDetail.answers.forEach((answer: any) => {
            const questionId = answer.questionId
            if (answers.value[questionId]) {
              answers.value[questionId] = {
                questionId: questionId,
                answerText: answer.answerText || '',
                answerValue: answer.answerValue || ''
              }
            }
          })
        }

        console.log('Loaded answers from server draft:', answers.value)
      } else {
        console.log('No draft found, initializing with default values')
        initAnswers(false) // 没有草稿，使用默认值初始化
      }
    } catch (error) {
      console.error('Failed to load server draft:', error)
      initAnswers(false) // 出错时使用默认值初始化
    }
  }

  // 从localStorage加载草稿
  const loadDraft = () => {
    if (!currentSurvey.value) return

    const draftKey = `survey_draft_${currentSurvey.value.id}`
    const draftStr = localStorage.getItem(draftKey)

    if (draftStr) {
      try {
        const draftData = JSON.parse(draftStr)
        // 检查草稿是否过期（24小时）
        if (Date.now() - draftData.timestamp < 24 * 60 * 60 * 1000) {
          // 合并草稿数据
          Object.keys(draftData.answers).forEach((questionId) => {
            const qId = Number(questionId)
            if (answers.value[qId]) {
              Object.assign(answers.value[qId], draftData.answers[qId])
            }
          })
          currentQuestionIndex.value = draftData.currentQuestionIndex || 0
        }
      } catch (error) {
        console.warn('Failed to load draft:', error)
      }
    }
  }

  // 清除草稿
  const clearDraft = (surveyId?: number) => {
    // 如果传入了surveyId，使用传入的ID；否则使用currentSurvey的ID
    const targetSurveyId = surveyId || currentSurvey.value?.id
    if (!targetSurveyId) {
      console.warn('No survey ID available for clearing draft')
      return
    }

    const draftKey = `survey_draft_${targetSurveyId}`
    localStorage.removeItem(draftKey)
    console.log('Draft cleared for survey:', targetSurveyId)
  }

  // 提交问卷
  const submitSurvey = async () => {
    if (!currentSurvey.value || !canSubmit.value) {
      throw new Error('问卷不能提交')
    }

    submitLoading.value = true
    try {
      const submitData = {
        instanceId: currentSurvey.value.id,
        answers: Object.values(answers.value).filter(
          (answer) => answer.answerValue || answer.answerText
        )
      }

      const response = await SurveyApi.submitSurvey(submitData)

      // 清除草稿
      // clearDraft()

      // 重新获取问卷列表以更新状态
      await fetchSurveyList()

      return response
    } catch (error) {
      console.error('Failed to submit survey:', error)
      throw error
    } finally {
      submitLoading.value = false
    }
  }

  // 获取用户回答记录
  const fetchUserResponses = async (params: PageReqVO & { instanceId?: number } = {}) => {
    loading.value = true
    try {
      const requestParams = {
        pageNo: responsesPagination.value.pageNo,
        pageSize: responsesPagination.value.pageSize,
        ...params
      }

      const response = await SurveyApi.getMyResponses(requestParams)
      userResponses.value = response.data.list
      responsesPagination.value.total = response.data.total

      if (params.pageNo) {
        responsesPagination.value.pageNo = params.pageNo
      }
    } catch (error) {
      console.error('Failed to fetch user responses:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取统计数据
  const fetchStatistics = async (instanceId: number) => {
    statisticsLoading.value = true
    try {
      const [statsResponse, rankingResponse] = await Promise.all([
        SurveyApi.getStatistics(instanceId),
        SurveyApi.getUserRanking(instanceId).catch(() => null) // 排名可能不存在
      ])

      statistics.value = statsResponse.data
      userRanking.value = rankingResponse?.data || null

      return {
        statistics: statistics.value,
        ranking: userRanking.value
      }
    } catch (error) {
      console.error('Failed to fetch statistics:', error)
      throw error
    } finally {
      statisticsLoading.value = false
    }
  }

  // 导航到下一题
  const nextQuestion = () => {
    if (!currentSurvey.value?.questions) return

    if (currentQuestionIndex.value < currentSurvey.value.questions.length - 1) {
      currentQuestionIndex.value++
      saveDraft()
    }
  }

  // 导航到上一题
  const prevQuestion = () => {
    if (currentQuestionIndex.value > 0) {
      currentQuestionIndex.value--
      saveDraft()
    }
  }

  // 跳转到指定题目
  const goToQuestion = (index: number) => {
    if (!currentSurvey.value?.questions) return

    if (index >= 0 && index < currentSurvey.value.questions.length) {
      currentQuestionIndex.value = index
      saveDraft()
    }
  }

  // 重置状态
  const resetState = () => {
    currentSurvey.value = null
    currentQuestionIndex.value = 0
    answers.value = {}
    statistics.value = null
    userRanking.value = null
  }

  // ==================== 返回 ====================

  return {
    // 状态
    surveyList,
    loading,
    pagination,
    currentSurvey,
    currentQuestionIndex,
    answers,
    userResponses,
    responsesPagination,
    statistics,
    userRanking,
    submitLoading,
    statisticsLoading,

    // 计算属性
    progressPercentage,
    currentQuestion,
    canSubmit,
    answeredCount,
    surveyStats,
    getMyCenterStats,

    // 方法
    fetchSurveyList,
    fetchSurveyDetail,
    initAnswers,
    updateAnswer,
    saveDraft,
    loadDraft,
    clearDraft,
    submitSurvey,
    fetchUserResponses,
    fetchStatistics,
    nextQuestion,
    prevQuestion,
    goToQuestion,
    resetState
  }
})
