import { ContentStatus, getContentStatusFromChapter } from '@/enums/content'

/**
 * 课程数据接口
 */
export interface CourseData {
  id: string | number
  name: string
  cover?: string
  star?: number
  progress?: number
  isRecommend?: boolean
  isNew?: boolean
  studyStatus?: number
  teacherName?: string
  topicName?: string
  duration?: number
  level?: number
  language?: number
  createTime?: string
  updateTime?: string
  [key: string]: any
}

/**
 * 通用内容数据接口
 */
export interface ContentData {
  id: string | number
  name: string
  title: string
  cover?: string
  image?: string
  rating?: number
  star?: number
  progress?: number
  isRecommended?: boolean
  isNew?: boolean
  status: ContentStatus
  author?: string
  category?: string
  duration?: number
  level?: number
  language?: number
  createdAt?: string
  updatedAt?: string
  [key: string]: any
}

/**
 * 将课程数据转换为通用内容数据格式
 * @param course 课程数据
 * @param defaultImage 默认图片
 * @returns 转换后的通用内容数据
 */
export function transformCourseToContent(course: CourseData, defaultImage?: string): ContentData {
  return {
    id: course.id,
    name: course.name,
    title: course.name,
    cover: course.cover || defaultImage,
    image: course.cover || defaultImage,
    rating: course.star,
    star: course.star,
    progress: course.progress || 0,
    isRecommended: course.isRecommend || false,
    isNew: course.isNew || false,
    status:
      course.studyStatus !== undefined
        ? getContentStatusFromChapter(course.studyStatus)
        : ContentStatus.DEFAULT,
    author: course.teacherName,
    category: course.topicName,
    duration: course.duration,
    level: course.level,
    language: course.language,
    createdAt: course.createTime,
    updatedAt: course.updateTime,
    // 保留原始数据以备后用
    ...course
  }
}

/**
 * 批量转换课程数据
 * @param courses 课程数据数组
 * @param defaultImage 默认图片
 * @returns 转换后的通用内容数据数组
 */
export function transformCoursesToContent(
  courses: CourseData[],
  defaultImage?: string
): ContentData[] {
  return courses.map((course) => transformCourseToContent(course, defaultImage))
}

/**
 * 课程状态过滤器
 */
export const CourseStatusFilters = {
  ALL: '',
  NOT_STARTED: 'not_started',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  PAUSED: 'paused',
  FAILED: 'failed'
} as const

/**
 * 课程状态标签映射
 */
export const CourseStatusLabels = {
  [CourseStatusFilters.ALL]: 'All Status',
  [CourseStatusFilters.NOT_STARTED]: 'Not Started',
  [CourseStatusFilters.IN_PROGRESS]: 'In Progress',
  [CourseStatusFilters.COMPLETED]: 'Completed',
  [CourseStatusFilters.PAUSED]: 'Paused',
  [CourseStatusFilters.FAILED]: 'Failed'
} as const

/**
 * 根据状态过滤课程
 * @param courses 课程数组
 * @param statusFilter 状态过滤器
 * @returns 过滤后的课程数组
 */
export function filterCoursesByStatus(courses: ContentData[], statusFilter: string): ContentData[] {
  if (!statusFilter || statusFilter === CourseStatusFilters.ALL) {
    return courses
  }

  const statusMap = {
    [CourseStatusFilters.NOT_STARTED]: ContentStatus.DRAFT,
    [CourseStatusFilters.IN_PROGRESS]: ContentStatus.IN_PROGRESS,
    [CourseStatusFilters.COMPLETED]: ContentStatus.COMPLETED,
    [CourseStatusFilters.PAUSED]: ContentStatus.PAUSED,
    [CourseStatusFilters.FAILED]: ContentStatus.FAILED
  }

  const targetStatus = statusMap[statusFilter]
  return targetStatus ? courses.filter((course) => course.status === targetStatus) : courses
}

/**
 * 课程搜索函数
 * @param courses 课程数组
 * @param searchQuery 搜索关键词
 * @param searchFields 搜索字段
 * @returns 搜索结果
 */
export function searchCourses(
  courses: ContentData[],
  searchQuery: string,
  searchFields: string[] = ['name', 'title', 'author', 'category']
): ContentData[] {
  if (!searchQuery.trim()) {
    return courses
  }

  const query = searchQuery.toLowerCase().trim()
  return courses.filter((course) =>
    searchFields.some((field) => {
      const value = course[field]
      return value && String(value).toLowerCase().includes(query)
    })
  )
}

/**
 * 课程排序函数
 * @param courses 课程数组
 * @param sortBy 排序字段
 * @param sortOrder 排序顺序
 * @returns 排序后的课程数组
 */
export function sortCourses(
  courses: ContentData[],
  sortBy: string = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc'
): ContentData[] {
  return [...courses].sort((a, b) => {
    const aValue = a[sortBy]
    const bValue = b[sortBy]

    if (aValue === undefined || aValue === null) return 1
    if (bValue === undefined || bValue === null) return -1

    let comparison = 0
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      comparison = aValue.localeCompare(bValue)
    } else if (typeof aValue === 'number' && typeof bValue === 'number') {
      comparison = aValue - bValue
    } else {
      comparison = String(aValue).localeCompare(String(bValue))
    }

    return sortOrder === 'asc' ? comparison : -comparison
  })
}

/**
 * 获取课程统计信息
 * @param courses 课程数组
 * @returns 统计信息
 */
export function getCourseStats(courses: ContentData[]) {
  const stats = {
    total: courses.length,
    notStarted: 0,
    inProgress: 0,
    completed: 0,
    paused: 0,
    failed: 0,
    averageProgress: 0,
    averageRating: 0
  }

  let totalProgress = 0
  let totalRating = 0
  let ratingCount = 0

  courses.forEach((course) => {
    // 统计状态
    switch (course.status) {
      case ContentStatus.DRAFT:
        stats.notStarted++
        break
      case ContentStatus.IN_PROGRESS:
        stats.inProgress++
        break
      case ContentStatus.COMPLETED:
        stats.completed++
        break
      case ContentStatus.PAUSED:
        stats.paused++
        break
      case ContentStatus.FAILED:
        stats.failed++
        break
    }

    // 统计进度
    if (course.progress !== undefined) {
      totalProgress += course.progress
    }

    // 统计评分
    if (course.rating !== undefined && course.rating > 0) {
      totalRating += course.rating
      ratingCount++
    }
  })

  // 计算平均值
  stats.averageProgress = courses.length > 0 ? totalProgress / courses.length : 0
  stats.averageRating = ratingCount > 0 ? totalRating / ratingCount : 0

  return stats
}
