<svg width="60" height="61" viewBox="0 0 60 61" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 2020">
<g id="Rectangle 3500" filter="url(#filter0_d_662_10963)">
<rect x="10" y="10.6035" width="40" height="40" rx="20" fill="#0049BB"/>
<rect x="10.5" y="11.1035" width="39" height="39" rx="19.5" stroke="url(#paint0_linear_662_10963)"/>
</g>
<g id="Group 2017" filter="url(#filter1_d_662_10963)">
<path id="Vector" d="M25.5 25.3262V24.2285C25.5 23.6072 26.0037 23.1035 26.625 23.1035H36.375C36.9963 23.1035 37.5 23.6072 37.5 24.2285V33.9785C37.5 34.5999 36.9963 35.1035 36.375 35.1035H35.2565" stroke="white" stroke-width="1.375"/>
<path id="Vector_2" d="M34.125 25.3535H23.625C23.0037 25.3535 22.5 25.8572 22.5 26.4785V36.9785C22.5 37.5998 23.0037 38.1035 23.625 38.1035H34.125C34.7463 38.1035 35.25 37.5998 35.25 36.9785V26.4785C35.25 25.8572 34.7463 25.3535 34.125 25.3535Z" fill="white" stroke="white" stroke-width="1.375" stroke-linejoin="bevel"/>
<path id="Vector_3" d="M27.9148 30.2703L29.8995 28.2043C30.4438 27.6599 31.3384 27.6719 31.8976 28.2311C32.4568 28.7902 32.4688 29.6848 31.9244 30.2292L31.2081 30.9877" stroke="#0049BB" stroke-width="1.375" stroke-linecap="square" stroke-linejoin="bevel"/>
<path id="Vector_4" d="M26.0497 32.3838C25.8584 32.5752 25.4627 32.9573 25.4627 32.9573C24.9183 33.5016 24.9035 34.4719 25.4627 35.0311C26.0218 35.5902 26.9164 35.6022 27.4608 35.0578L29.3974 33.2997" stroke="#0049BB" stroke-width="1.375" stroke-linecap="square" stroke-linejoin="bevel"/>
<path id="Vector_5" d="M27.9984 32.2266C27.7387 31.9669 27.597 31.6348 27.5743 31.299C27.5481 30.912 27.6801 30.52 27.9716 30.2285" stroke="#0049BB" stroke-width="1.375" stroke-linecap="square" stroke-linejoin="bevel"/>
<path id="Vector_6" d="M29.3706 31.3018C29.9298 31.8609 29.9418 32.7555 29.3974 33.2999" stroke="#0049BB" stroke-width="1.375" stroke-linecap="square" stroke-linejoin="bevel"/>
</g>
</g>
<defs>
<filter id="filter0_d_662_10963" x="0" y="0.603516" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0251563 0 0 0 0 0.276253 0 0 0 0 0.670833 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_662_10963"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_662_10963" result="shape"/>
</filter>
<filter id="filter1_d_662_10963" x="17.8125" y="18.416" width="24.375" height="24.375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_662_10963"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_662_10963" result="shape"/>
</filter>
<linearGradient id="paint0_linear_662_10963" x1="30" y1="10.6035" x2="30" y2="50.6035" gradientUnits="userSpaceOnUse">
<stop stop-color="#BBD5FF"/>
<stop offset="0.765" stop-color="#0049BB"/>
<stop offset="1" stop-color="#ACCCFF"/>
</linearGradient>
</defs>
</svg>
