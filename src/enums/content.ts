/**
 * 通用内容状态枚举
 */
export enum ContentStatus {
  /** 默认/未设置 */
  DEFAULT = 0,
  /** 草稿/未开始 */
  DRAFT = 1,
  /** 进行中/处理中 */
  IN_PROGRESS = 2,
  /** 完成/已发布 */
  COMPLETED = 3,
  /** 暂停 */
  PAUSED = 4,
  /** 失败/错误 */
  FAILED = 5,
  /** 锁定/私有 */
  LOCKED = 6,
  /** 待审核 */
  PENDING_REVIEW = 7,
  /** 同步中 */
  SYNCING = 8,
  /** 特色/推荐 */
  FEATURED = 9
}

/**
 * 内容显示模式枚举
 */
export enum ContentDisplayMode {
  /** 卡片模式 */
  CARD = 'card',
  /** 列表模式 */
  LIST = 'list',
  /** 网格模式 */
  GRID = 'grid',
  /** 媒体模式 */
  MEDIA = 'media',
  /** 自定义模式 */
  CUSTOM = 'custom'
}

/**
 * 内容变体枚举
 */
export enum ContentVariant {
  /** 默认样式 */
  DEFAULT = 'default',
  /** 紧凑样式 */
  COMPACT = 'compact',
  /** 详细样式 */
  DETAILED = 'detailed',
  /** 极简样式 */
  MINIMAL = 'minimal'
}

/**
 * 布局模式枚举
 */
export enum LayoutMode {
  /** 网格布局 */
  GRID = 'grid',
  /** 列表布局 */
  LIST = 'list',
  /** 瀑布流布局 */
  MASONRY = 'masonry',
  /** 轮播布局 */
  CAROUSEL = 'carousel'
}

/**
 * 宽高比枚举
 */
export enum AspectRatio {
  /** 正方形 1:1 */
  SQUARE = 'square',
  /** 宽屏 16:9 */
  WIDESCREEN = '16/9',
  /** 标准 4:3 */
  STANDARD = '4/3',
  /** 照片 3:2 */
  PHOTO = '3/2',
  /** 自动 */
  AUTO = 'auto'
}

/**
 * 内容类型枚举
 */
export enum ContentType {
  /** 课程 */
  COURSE = 'course',
  /** 文章 */
  ARTICLE = 'article',
  /** 视频 */
  VIDEO = 'video',
  /** 音频 */
  AUDIO = 'audio',
  /** 图片 */
  IMAGE = 'image',
  /** 文档 */
  DOCUMENT = 'document',
  /** 产品 */
  PRODUCT = 'product',
  /** 用户 */
  USER = 'user',
  /** 活动 */
  EVENT = 'event',
  /** 通知 */
  NOTIFICATION = 'notification',
  /** 其他 */
  OTHER = 'other'
}

/**
 * 优先级枚举
 */
export enum Priority {
  /** 低优先级 */
  LOW = 1,
  /** 普通优先级 */
  NORMAL = 2,
  /** 高优先级 */
  HIGH = 3,
  /** 紧急优先级 */
  URGENT = 4,
  /** 关键优先级 */
  CRITICAL = 5
}

/**
 * 可见性枚举
 */
export enum Visibility {
  /** 公开 */
  PUBLIC = 'public',
  /** 私有 */
  PRIVATE = 'private',
  /** 仅限好友 */
  FRIENDS_ONLY = 'friends_only',
  /** 仅限组织 */
  ORGANIZATION_ONLY = 'organization_only',
  /** 草稿 */
  DRAFT = 'draft'
}

/**
 * 内容状态图标映射
 */
export const ContentStatusIcons = {
  [ContentStatus.DEFAULT]: '⭕',
  [ContentStatus.DRAFT]: '📝',
  [ContentStatus.IN_PROGRESS]: '⏳',
  [ContentStatus.COMPLETED]: '✅',
  [ContentStatus.PAUSED]: '⏸️',
  [ContentStatus.FAILED]: '❌',
  [ContentStatus.LOCKED]: '🔒',
  [ContentStatus.PENDING_REVIEW]: '📋',
  [ContentStatus.SYNCING]: '🔄',
  [ContentStatus.FEATURED]: '⭐'
} as const

/**
 * 内容状态标签映射
 */
export const ContentStatusLabels = {
  [ContentStatus.DEFAULT]: 'Default',
  [ContentStatus.DRAFT]: 'Draft',
  [ContentStatus.IN_PROGRESS]: 'In Progress',
  [ContentStatus.COMPLETED]: 'Completed',
  [ContentStatus.PAUSED]: 'Paused',
  [ContentStatus.FAILED]: 'Failed',
  [ContentStatus.LOCKED]: 'Locked',
  [ContentStatus.PENDING_REVIEW]: 'Pending Review',
  [ContentStatus.SYNCING]: 'Syncing',
  [ContentStatus.FEATURED]: 'Featured'
} as const

/**
 * 内容状态颜色映射
 */
export const ContentStatusColors = {
  [ContentStatus.DEFAULT]: 'gray',
  [ContentStatus.DRAFT]: 'blue',
  [ContentStatus.IN_PROGRESS]: 'yellow',
  [ContentStatus.COMPLETED]: 'green',
  [ContentStatus.PAUSED]: 'orange',
  [ContentStatus.FAILED]: 'red',
  [ContentStatus.LOCKED]: 'purple',
  [ContentStatus.PENDING_REVIEW]: 'indigo',
  [ContentStatus.SYNCING]: 'cyan',
  [ContentStatus.FEATURED]: 'pink'
} as const

/**
 * 内容类型图标映射
 */
export const ContentTypeIcons = {
  [ContentType.COURSE]: '📚',
  [ContentType.ARTICLE]: '📄',
  [ContentType.VIDEO]: '🎥',
  [ContentType.AUDIO]: '🎵',
  [ContentType.IMAGE]: '🖼️',
  [ContentType.DOCUMENT]: '📋',
  [ContentType.PRODUCT]: '🛍️',
  [ContentType.USER]: '👤',
  [ContentType.EVENT]: '📅',
  [ContentType.NOTIFICATION]: '🔔',
  [ContentType.OTHER]: '📦'
} as const

/**
 * 宽高比值映射
 */
export const AspectRatioValues = {
  [AspectRatio.SQUARE]: '1 / 1',
  [AspectRatio.WIDESCREEN]: '16 / 9',
  [AspectRatio.STANDARD]: '4 / 3',
  [AspectRatio.PHOTO]: '3 / 2',
  [AspectRatio.AUTO]: 'auto'
} as const

/**
 * 课程语言枚举
 */
export enum CourseLanguage {
  ALL = 0,
  ENGLISH = 1,
  ARABIC = 2
}

/**
 * 课程级别枚举
 */
export enum CourseLevel {
  ALL = 0,
  BEGINNER = 1,
  INTERMEDIATE = 2,
  ADVANCED = 3
}

/**
 * 字幕类型枚举
 */
export enum SubtitleType {
  WITHOUT = 0,
  WITH = 1
}

/**
 * 课程来源枚举
 */
export enum CourseSource {
  LOCAL = 1,
  CLOUD = 2
}

/**
 * 课程时长类型枚举
 */
export enum DurationType {
  SHORT = 1, // <15min
  MEDIUM = 2, // 15-30min
  LONG = 3, // 30-60min
  EXTRA_LONG = 4 // >60min
}

/**
 * 课程语言标签映射
 */
export const CourseLanguageLabels = {
  [CourseLanguage.ALL]: 'All Languages',
  [CourseLanguage.ENGLISH]: 'English',
  [CourseLanguage.ARABIC]: 'Arabic'
} as const

/**
 * 课程级别标签映射
 */
export const CourseLevelLabels = {
  [CourseLevel.ALL]: 'All Levels',
  [CourseLevel.BEGINNER]: 'Beginner',
  [CourseLevel.INTERMEDIATE]: 'Intermediate',
  [CourseLevel.ADVANCED]: 'Advanced'
} as const

/**
 * 字幕类型标签映射
 */
export const SubtitleTypeLabels = {
  [SubtitleType.WITHOUT]: 'Without Subtitles',
  [SubtitleType.WITH]: 'With Subtitles'
} as const

/**
 * 课程来源标签映射
 */
export const CourseSourceLabels = {
  [CourseSource.LOCAL]: 'Local Host',
  [CourseSource.CLOUD]: 'Cloud'
} as const

/**
 * 课程时长标签映射
 */
export const DurationTypeLabels = {
  [DurationType.SHORT]: '<15min',
  [DurationType.MEDIUM]: '15-30min',
  [DurationType.LONG]: '30-60min',
  [DurationType.EXTRA_LONG]: '>60min'
} as const

/**
 * 章节状态到通用内容状态的映射
 * 用于向后兼容现有的 ChapterStatus 枚举
 */
export const ChapterStatusToContentStatus = {
  0: ContentStatus.DRAFT, // NotStart -> DRAFT
  1: ContentStatus.IN_PROGRESS, // InProgress -> IN_PROGRESS
  2: ContentStatus.FAILED, // Failed -> FAILED
  3: ContentStatus.COMPLETED // Completed -> COMPLETED
} as const

/**
 * 通用内容状态到章节状态的映射
 */
export const ContentStatusToChapterStatus = {
  [ContentStatus.DRAFT]: 0,
  [ContentStatus.IN_PROGRESS]: 1,
  [ContentStatus.FAILED]: 2,
  [ContentStatus.COMPLETED]: 3
} as const

/**
 * 获取章节状态对应的通用状态
 */
export function getContentStatusFromChapter(chapterStatus: number): ContentStatus {
  return ChapterStatusToContentStatus[chapterStatus] || ContentStatus.DEFAULT
}

/**
 * 获取通用状态对应的章节状态
 */
export function getChapterStatusFromContent(contentStatus: ContentStatus): number {
  return ContentStatusToChapterStatus[contentStatus] ?? 0
}
